-- FinanceInsight Pro 数据库初始化脚本

-- 设置字符集和时区
SET NAMES utf8mb4;
SET time_zone = '+08:00';

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS financeinsight 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE financeinsight;

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    email VARCHAR(100) UNIQUE NOT NULL COMMENT '邮箱',
    phone VARCHAR(20) UNIQUE COMMENT '手机号',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    nickname VARCHAR(100) COMMENT '昵称',
    avatar VARCHAR(500) COMMENT '头像URL',
    real_name VARCHAR(50) COMMENT '真实姓名',
    id_card VARCHAR(20) COMMENT '身份证号',
    gender INT DEFAULT 0 COMMENT '性别：0-未知，1-男，2-女',
    birthday DATETIME COMMENT '生日',
    address VARCHAR(500) COMMENT '地址',
    company VARCHAR(200) COMMENT '公司',
    position VARCHAR(100) COMMENT '职位',
    status ENUM('INACTIVE', 'ACTIVE', 'SUSPENDED', 'BANNED') DEFAULT 'ACTIVE' COMMENT '用户状态',
    is_verified BOOLEAN DEFAULT FALSE COMMENT '是否已验证',
    is_email_verified BOOLEAN DEFAULT FALSE COMMENT '邮箱是否已验证',
    is_phone_verified BOOLEAN DEFAULT FALSE COMMENT '手机是否已验证',
    subscription_type ENUM('free', 'pro', 'enterprise') DEFAULT 'free' COMMENT '订阅类型',
    subscription_expires DATETIME COMMENT '订阅到期时间',
    last_login_at DATETIME COMMENT '最后登录时间',
    last_login_ip VARCHAR(45) COMMENT '最后登录IP',
    login_count INT DEFAULT 0 COMMENT '登录次数',
    failed_login_attempts INT DEFAULT 0 COMMENT '失败登录次数',
    locked_until DATETIME COMMENT '锁定到期时间',
    password_changed_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '密码修改时间',
    preferences JSON COMMENT '用户偏好设置',
    notification_settings JSON COMMENT '通知设置',
    api_calls_today INT DEFAULT 0 COMMENT '今日API调用次数',
    api_calls_total INT DEFAULT 0 COMMENT '总API调用次数',
    recommendations_viewed INT DEFAULT 0 COMMENT '查看推荐次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted BOOLEAN DEFAULT FALSE COMMENT '是否删除',
    remark TEXT COMMENT '备注',
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_status (status),
    INDEX idx_subscription (subscription_type, subscription_expires),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 新闻表
CREATE TABLE IF NOT EXISTS news (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(500) NOT NULL COMMENT '新闻标题',
    summary TEXT COMMENT '新闻摘要',
    content LONGTEXT COMMENT '新闻内容',
    url VARCHAR(1000) UNIQUE NOT NULL COMMENT '新闻链接',
    source VARCHAR(100) NOT NULL COMMENT '新闻来源',
    author VARCHAR(100) COMMENT '作者',
    publish_time DATETIME NOT NULL COMMENT '发布时间',
    crawl_time DATETIME NOT NULL COMMENT '抓取时间',
    category VARCHAR(50) COMMENT '新闻分类',
    tags JSON COMMENT '标签列表',
    keywords JSON COMMENT '关键词列表',
    sentiment VARCHAR(20) COMMENT '情感倾向',
    sentiment_score DECIMAL(5,4) COMMENT '情感分数(-1到1)',
    sentiment_confidence DECIMAL(5,4) COMMENT '情感置信度(0到1)',
    importance_score DECIMAL(5,4) COMMENT '重要性分数(0到1)',
    importance_level INT COMMENT '重要性等级(1-5)',
    market_impact_score DECIMAL(5,4) COMMENT '市场影响分数(0到1)',
    affected_industries JSON COMMENT '影响的行业',
    affected_stocks JSON COMMENT '影响的股票',
    view_count INT DEFAULT 0 COMMENT '查看次数',
    share_count INT DEFAULT 0 COMMENT '分享次数',
    like_count INT DEFAULT 0 COMMENT '点赞次数',
    is_processed BOOLEAN DEFAULT FALSE COMMENT '是否已处理',
    is_featured BOOLEAN DEFAULT FALSE COMMENT '是否精选',
    is_hot BOOLEAN DEFAULT FALSE COMMENT '是否热点',
    quality_score DECIMAL(5,4) COMMENT '内容质量分数(0到1)',
    credibility_score DECIMAL(5,4) COMMENT '可信度分数(0到1)',
    raw_data JSON COMMENT '原始抓取数据',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted BOOLEAN DEFAULT FALSE COMMENT '是否删除',
    remark TEXT COMMENT '备注',
    INDEX idx_title (title(100)),
    INDEX idx_url (url(100)),
    INDEX idx_source (source),
    INDEX idx_publish_time (publish_time),
    INDEX idx_category (category),
    INDEX idx_sentiment (sentiment),
    INDEX idx_importance (importance_level),
    INDEX idx_hot_featured (is_hot, is_featured),
    INDEX idx_source_time (source, publish_time),
    INDEX idx_category_time (category, publish_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='新闻表';

-- 股票表
CREATE TABLE IF NOT EXISTS stocks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(10) UNIQUE NOT NULL COMMENT '股票代码',
    name VARCHAR(100) NOT NULL COMMENT '股票名称',
    full_name VARCHAR(200) COMMENT '股票全称',
    english_name VARCHAR(200) COMMENT '英文名称',
    market VARCHAR(10) NOT NULL COMMENT '所属市场',
    board VARCHAR(50) COMMENT '板块',
    industry VARCHAR(100) COMMENT '所属行业',
    sector VARCHAR(100) COMMENT '所属板块',
    concept JSON COMMENT '概念标签',
    listing_date DATE COMMENT '上市日期',
    ipo_price DECIMAL(10,4) COMMENT '发行价格',
    total_shares DECIMAL(20,0) COMMENT '总股本',
    float_shares DECIMAL(20,0) COMMENT '流通股本',
    restricted_shares DECIMAL(20,0) COMMENT '限售股本',
    market_cap DECIMAL(20,2) COMMENT '总市值',
    float_market_cap DECIMAL(20,2) COMMENT '流通市值',
    pe_ratio DECIMAL(10,4) COMMENT '市盈率',
    pb_ratio DECIMAL(10,4) COMMENT '市净率',
    ps_ratio DECIMAL(10,4) COMMENT '市销率',
    roe DECIMAL(10,4) COMMENT '净资产收益率',
    roa DECIMAL(10,4) COMMENT '总资产收益率',
    current_price DECIMAL(10,4) COMMENT '当前价格',
    prev_close DECIMAL(10,4) COMMENT '昨收价',
    change_amount DECIMAL(10,4) COMMENT '涨跌额',
    change_percent DECIMAL(10,4) COMMENT '涨跌幅',
    volume DECIMAL(20,0) COMMENT '成交量',
    turnover DECIMAL(20,2) COMMENT '成交额',
    turnover_rate DECIMAL(10,4) COMMENT '换手率',
    day_high DECIMAL(10,4) COMMENT '日最高价',
    day_low DECIMAL(10,4) COMMENT '日最低价',
    week_52_high DECIMAL(10,4) COMMENT '52周最高价',
    week_52_low DECIMAL(10,4) COMMENT '52周最低价',
    status INT DEFAULT 1 COMMENT '股票状态',
    is_tradable BOOLEAN DEFAULT TRUE COMMENT '是否可交易',
    price_updated_at DATETIME COMMENT '价格更新时间',
    info_updated_at DATETIME COMMENT '信息更新时间',
    news_count INT DEFAULT 0 COMMENT '相关新闻数量',
    recommendation_count INT DEFAULT 0 COMMENT '推荐次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted BOOLEAN DEFAULT FALSE COMMENT '是否删除',
    remark TEXT COMMENT '备注',
    INDEX idx_code (code),
    INDEX idx_name (name),
    INDEX idx_market (market),
    INDEX idx_industry (industry),
    INDEX idx_market_cap (market_cap),
    INDEX idx_change_percent (change_percent),
    INDEX idx_volume (volume),
    INDEX idx_market_industry (market, industry)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='股票表';

-- 推荐表
CREATE TABLE IF NOT EXISTS recommendations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT COMMENT '用户ID（空表示公共推荐）',
    stock_id BIGINT NOT NULL COMMENT '股票ID',
    stock_code VARCHAR(10) NOT NULL COMMENT '股票代码',
    stock_name VARCHAR(100) NOT NULL COMMENT '股票名称',
    recommendation_type VARCHAR(50) NOT NULL COMMENT '推荐类型',
    action VARCHAR(20) NOT NULL COMMENT '推荐操作',
    score DECIMAL(5,2) NOT NULL COMMENT '推荐分数(0-10)',
    confidence DECIMAL(5,4) NOT NULL COMMENT '置信度(0-1)',
    risk_level INT NOT NULL COMMENT '风险等级(1-5)',
    risk_score DECIMAL(5,4) COMMENT '风险分数(0-1)',
    risk_factors JSON COMMENT '风险因子',
    current_price DECIMAL(10,4) COMMENT '当前价格',
    target_price DECIMAL(10,4) COMMENT '目标价格',
    stop_loss DECIMAL(10,4) COMMENT '止损价格',
    expected_return DECIMAL(10,4) COMMENT '预期收益率',
    time_horizon VARCHAR(10) NOT NULL COMMENT '投资期限',
    valid_until DATETIME COMMENT '有效期至',
    reasons JSON COMMENT '推荐理由列表',
    related_news JSON COMMENT '相关新闻ID列表',
    technical_signals JSON COMMENT '技术信号',
    fundamental_factors JSON COMMENT '基本面因子',
    model_version VARCHAR(20) COMMENT '模型版本',
    algorithm VARCHAR(50) COMMENT '算法名称',
    features_used JSON COMMENT '使用的特征',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否有效',
    is_public BOOLEAN DEFAULT TRUE COMMENT '是否公开',
    is_featured BOOLEAN DEFAULT FALSE COMMENT '是否精选',
    view_count INT DEFAULT 0 COMMENT '查看次数',
    like_count INT DEFAULT 0 COMMENT '点赞次数',
    follow_count INT DEFAULT 0 COMMENT '跟随次数',
    actual_return DECIMAL(10,4) COMMENT '实际收益率',
    max_drawdown DECIMAL(10,4) COMMENT '最大回撤',
    hit_target BOOLEAN COMMENT '是否达到目标价',
    hit_stop_loss BOOLEAN COMMENT '是否触及止损',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted BOOLEAN DEFAULT FALSE COMMENT '是否删除',
    remark TEXT COMMENT '备注',
    INDEX idx_user_stock (user_id, stock_code),
    INDEX idx_stock_id (stock_id),
    INDEX idx_stock_code (stock_code),
    INDEX idx_type_score (recommendation_type, score),
    INDEX idx_risk_time (risk_level, time_horizon),
    INDEX idx_active_public (is_active, is_public),
    INDEX idx_valid_until (valid_until),
    INDEX idx_created_score (created_at, score)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='推荐表';

-- 创建默认管理员用户
INSERT IGNORE INTO users (
    username, 
    email, 
    password_hash, 
    nickname, 
    subscription_type, 
    is_verified, 
    is_email_verified
) VALUES (
    'admin', 
    '<EMAIL>', 
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', -- password: admin123
    '系统管理员', 
    'enterprise', 
    TRUE, 
    TRUE
);

-- 创建测试用户
INSERT IGNORE INTO users (
    username, 
    email, 
    password_hash, 
    nickname, 
    subscription_type
) VALUES (
    'testuser', 
    '<EMAIL>', 
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', -- password: admin123
    '测试用户', 
    'pro'
);

-- 插入一些示例股票数据
INSERT IGNORE INTO stocks (
    code, name, full_name, market, industry, 
    current_price, prev_close, change_amount, change_percent,
    market_cap, pe_ratio, pb_ratio
) VALUES 
('000001', '平安银行', '平安银行股份有限公司', 'sz', '银行', 14.25, 13.95, 0.30, 2.15, 276234384325, 4.89, 0.67),
('000002', '万科A', '万科企业股份有限公司', 'sz', '房地产开发', 18.50, 18.20, 0.30, 1.65, 205678901234, 8.5, 1.2),
('600036', '招商银行', '招商银行股份有限公司', 'sh', '银行', 42.80, 42.10, 0.70, 1.66, 1234567890123, 6.2, 1.1),
('600519', '贵州茅台', '贵州茅台酒股份有限公司', 'sh', '白酒', 1680.00, 1650.00, 30.00, 1.82, 2100000000000, 28.5, 12.8),
('000858', '五粮液', '宜宾五粮液股份有限公司', 'sz', '白酒', 158.50, 155.20, 3.30, 2.13, 612345678901, 22.1, 5.6);

COMMIT;
