#!/bin/bash

# FinanceInsight Pro 停止脚本
# 用于安全停止整个系统

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 停止服务
stop_services() {
    log_step "停止 FinanceInsight Pro 服务..."
    
    # 检查是否有运行的容器
    if [ "$(docker-compose ps -q)" ]; then
        log_info "正在停止所有服务..."
        docker-compose down
        log_info "所有服务已停止"
    else
        log_info "没有运行中的服务"
    fi
}

# 清理资源
cleanup_resources() {
    log_step "清理系统资源..."
    
    # 清理未使用的镜像
    log_info "清理未使用的Docker镜像..."
    docker image prune -f
    
    # 清理未使用的网络
    log_info "清理未使用的Docker网络..."
    docker network prune -f
    
    # 清理未使用的卷（可选）
    read -p "是否清理未使用的Docker卷？这将删除所有未使用的数据卷 (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_warn "清理未使用的Docker卷..."
        docker volume prune -f
    fi
    
    log_info "资源清理完成"
}

# 备份数据
backup_data() {
    log_step "数据备份选项..."
    
    read -p "是否备份数据库数据？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "开始备份数据..."
        
        # 创建备份目录
        backup_dir="backups/$(date +%Y%m%d_%H%M%S)"
        mkdir -p "$backup_dir"
        
        # 备份MySQL数据
        if docker-compose ps mysql | grep -q "Up"; then
            log_info "备份MySQL数据..."
            docker-compose exec mysql mysqldump -u root -pfinanceinsight123 financeinsight > "$backup_dir/mysql_backup.sql"
        fi
        
        # 备份Redis数据
        if docker-compose ps redis | grep -q "Up"; then
            log_info "备份Redis数据..."
            docker-compose exec redis redis-cli BGSAVE
            docker cp $(docker-compose ps -q redis):/data/dump.rdb "$backup_dir/redis_backup.rdb"
        fi
        
        log_info "数据备份完成，保存在: $backup_dir"
    fi
}

# 显示停止后的状态
show_final_status() {
    log_step "最终状态："
    
    # 显示容器状态
    if [ "$(docker-compose ps -q)" ]; then
        docker-compose ps
    else
        log_info "所有容器已停止"
    fi
    
    # 显示系统资源使用情况
    echo
    log_info "Docker系统信息："
    docker system df
}

# 主函数
main() {
    echo "=========================================="
    echo "    FinanceInsight Pro 停止脚本"
    echo "    智能财经新闻分析与A股推荐系统"
    echo "=========================================="
    echo
    
    # 询问是否备份数据
    backup_data
    
    # 停止服务
    stop_services
    
    # 清理资源
    cleanup_resources
    
    # 显示最终状态
    show_final_status
    
    echo
    log_info "🛑 FinanceInsight Pro 已安全停止"
    log_info "如需重新启动，请运行: ./scripts/start.sh"
}

# 处理中断信号
trap 'log_error "停止过程被中断"; exit 1' INT TERM

# 执行主函数
main "$@"
