#!/bin/bash

# FinanceInsight Pro 启动脚本
# 用于快速启动整个系统

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查Docker和Docker Compose
check_requirements() {
    log_step "检查系统要求..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_info "系统要求检查通过"
}

# 检查端口占用
check_ports() {
    log_step "检查端口占用..."
    
    ports=(80 443 3000 8000 3306 6379 9200 27017 5672 15672 9090 3001)
    occupied_ports=()
    
    for port in "${ports[@]}"; do
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            occupied_ports+=($port)
        fi
    done
    
    if [ ${#occupied_ports[@]} -gt 0 ]; then
        log_warn "以下端口被占用: ${occupied_ports[*]}"
        log_warn "请确保这些端口可用，或修改 docker-compose.yml 中的端口配置"
        read -p "是否继续启动？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    else
        log_info "端口检查通过"
    fi
}

# 创建必要的目录
create_directories() {
    log_step "创建必要的目录..."
    
    directories=(
        "backend/logs"
        "backend/uploads"
        "backend/models"
        "backend/backups"
        "deployment/mysql/data"
        "deployment/redis/data"
        "deployment/elasticsearch/data"
        "deployment/mongodb/data"
        "deployment/rabbitmq/data"
        "deployment/prometheus/data"
        "deployment/grafana/data"
        "deployment/nginx/logs"
    )
    
    for dir in "${directories[@]}"; do
        mkdir -p "$dir"
        log_info "创建目录: $dir"
    done
}

# 设置环境变量
setup_environment() {
    log_step "设置环境变量..."
    
    if [ ! -f "backend/.env" ]; then
        log_info "复制环境变量配置文件..."
        cp backend/.env.example backend/.env
        log_warn "请编辑 backend/.env 文件，配置必要的环境变量"
    fi
    
    # 生成随机密钥
    if ! grep -q "SECRET_KEY=your-super-secret-key" backend/.env; then
        log_info "环境变量已配置"
    else
        log_warn "请修改 backend/.env 中的 SECRET_KEY"
    fi
}

# 构建镜像
build_images() {
    log_step "构建Docker镜像..."
    
    log_info "构建后端镜像..."
    docker-compose build backend
    
    log_info "构建前端镜像..."
    docker-compose build frontend
    
    log_info "镜像构建完成"
}

# 启动服务
start_services() {
    log_step "启动服务..."
    
    # 首先启动基础服务
    log_info "启动基础服务 (MySQL, Redis, Elasticsearch, MongoDB, RabbitMQ)..."
    docker-compose up -d mysql redis elasticsearch mongodb rabbitmq
    
    # 等待基础服务启动
    log_info "等待基础服务启动..."
    sleep 30
    
    # 检查基础服务状态
    check_service_health "mysql" "3306"
    check_service_health "redis" "6379"
    check_service_health "elasticsearch" "9200"
    check_service_health "mongodb" "27017"
    check_service_health "rabbitmq" "5672"
    
    # 启动应用服务
    log_info "启动应用服务..."
    docker-compose up -d backend celery-worker celery-beat
    
    # 等待后端服务启动
    log_info "等待后端服务启动..."
    sleep 20
    
    # 启动前端和代理服务
    log_info "启动前端服务..."
    docker-compose up -d frontend nginx
    
    # 启动监控服务
    log_info "启动监控服务..."
    docker-compose up -d prometheus grafana
    
    log_info "所有服务启动完成"
}

# 检查服务健康状态
check_service_health() {
    local service_name=$1
    local port=$2
    local max_attempts=30
    local attempt=1
    
    log_info "检查 $service_name 服务状态..."
    
    while [ $attempt -le $max_attempts ]; do
        if nc -z localhost $port 2>/dev/null; then
            log_info "$service_name 服务已就绪"
            return 0
        fi
        
        log_info "等待 $service_name 服务启动... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    log_error "$service_name 服务启动失败"
    return 1
}

# 显示服务状态
show_status() {
    log_step "服务状态："
    docker-compose ps
}

# 显示访问信息
show_access_info() {
    log_step "访问信息："
    echo
    log_info "🌐 前端应用: http://localhost"
    log_info "🔧 API文档: http://localhost/api/docs"
    log_info "📊 Grafana监控: http://localhost:3001 (admin/financeinsight123)"
    log_info "🐰 RabbitMQ管理: http://localhost:15672 (financeinsight/financeinsight123)"
    log_info "📈 Prometheus: http://localhost:9090"
    echo
    log_info "默认管理员账号: admin / admin123"
    log_info "默认测试账号: testuser / admin123"
    echo
}

# 主函数
main() {
    echo "=========================================="
    echo "    FinanceInsight Pro 启动脚本"
    echo "    智能财经新闻分析与A股推荐系统"
    echo "=========================================="
    echo
    
    # 检查系统要求
    check_requirements
    
    # 检查端口占用
    check_ports
    
    # 创建目录
    create_directories
    
    # 设置环境变量
    setup_environment
    
    # 构建镜像
    build_images
    
    # 启动服务
    start_services
    
    # 显示状态
    show_status
    
    # 显示访问信息
    show_access_info
    
    log_info "🎉 FinanceInsight Pro 启动完成！"
}

# 处理中断信号
trap 'log_error "启动过程被中断"; exit 1' INT TERM

# 执行主函数
main "$@"
