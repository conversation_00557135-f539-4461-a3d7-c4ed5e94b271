version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: financeinsight-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: financeinsight123
      MYSQL_DATABASE: financeinsight
      MYSQL_USER: financeinsight
      MYSQL_PASSWORD: financeinsight123
      TZ: Asia/Shanghai
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./deployment/mysql/init:/docker-entrypoint-initdb.d
      - ./deployment/mysql/conf:/etc/mysql/conf.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - financeinsight-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: financeinsight-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./deployment/redis/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - financeinsight-network

  # Elasticsearch搜索引擎
  elasticsearch:
    image: elasticsearch:7.17.0
    container_name: financeinsight-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - financeinsight-network

  # MongoDB文档数据库
  mongodb:
    image: mongo:6.0
    container_name: financeinsight-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: financeinsight
      MONGO_INITDB_ROOT_PASSWORD: financeinsight123
      MONGO_INITDB_DATABASE: financeinsight
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./deployment/mongodb/init:/docker-entrypoint-initdb.d
    networks:
      - financeinsight-network

  # RabbitMQ消息队列
  rabbitmq:
    image: rabbitmq:3.11-management-alpine
    container_name: financeinsight-rabbitmq
    restart: unless-stopped
    environment:
      RABBITMQ_DEFAULT_USER: financeinsight
      RABBITMQ_DEFAULT_PASS: financeinsight123
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - financeinsight-network

  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: financeinsight-backend
    restart: unless-stopped
    environment:
      - DATABASE_URL=mysql+pymysql://financeinsight:financeinsight123@mysql:3306/financeinsight
      - REDIS_URL=redis://redis:6379/0
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - MONGODB_URL=***********************************************************************
      - RABBITMQ_URL=amqp://financeinsight:financeinsight123@rabbitmq:5672/
      - SECRET_KEY=your-super-secret-key-change-this-in-production
      - ENVIRONMENT=production
      - DEBUG=False
    ports:
      - "8000:8000"
    volumes:
      - ./backend/logs:/app/logs
      - ./backend/uploads:/app/uploads
      - ./backend/models:/app/models
    depends_on:
      - mysql
      - redis
      - elasticsearch
      - mongodb
      - rabbitmq
    networks:
      - financeinsight-network

  # Celery工作进程
  celery-worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: financeinsight-celery-worker
    restart: unless-stopped
    environment:
      - DATABASE_URL=mysql+pymysql://financeinsight:financeinsight123@mysql:3306/financeinsight
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
      - RABBITMQ_URL=amqp://financeinsight:financeinsight123@rabbitmq:5672/
      - SECRET_KEY=your-super-secret-key-change-this-in-production
      - ENVIRONMENT=production
    command: celery -A app.core.celery worker --loglevel=info --concurrency=4
    volumes:
      - ./backend/logs:/app/logs
      - ./backend/models:/app/models
    depends_on:
      - mysql
      - redis
      - rabbitmq
    networks:
      - financeinsight-network

  # Celery定时任务
  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: financeinsight-celery-beat
    restart: unless-stopped
    environment:
      - DATABASE_URL=mysql+pymysql://financeinsight:financeinsight123@mysql:3306/financeinsight
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
      - SECRET_KEY=your-super-secret-key-change-this-in-production
      - ENVIRONMENT=production
    command: celery -A app.core.celery beat --loglevel=info
    volumes:
      - ./backend/logs:/app/logs
      - celery_beat_data:/app/celerybeat-schedule
    depends_on:
      - mysql
      - redis
      - rabbitmq
    networks:
      - financeinsight-network

  # 前端Web服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: financeinsight-frontend
    restart: unless-stopped
    ports:
      - "3000:80"
    depends_on:
      - backend
    networks:
      - financeinsight-network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: financeinsight-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./deployment/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./deployment/nginx/conf.d:/etc/nginx/conf.d
      - ./deployment/nginx/ssl:/etc/nginx/ssl
      - ./deployment/nginx/logs:/var/log/nginx
    depends_on:
      - frontend
      - backend
    networks:
      - financeinsight-network

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: financeinsight-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./deployment/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - financeinsight-network

  # Grafana可视化
  grafana:
    image: grafana/grafana:latest
    container_name: financeinsight-grafana
    restart: unless-stopped
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=financeinsight123
    ports:
      - "3001:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./deployment/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./deployment/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - financeinsight-network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  elasticsearch_data:
    driver: local
  mongodb_data:
    driver: local
  rabbitmq_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  celery_beat_data:
    driver: local

networks:
  financeinsight-network:
    driver: bridge
