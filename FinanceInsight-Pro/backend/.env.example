# 应用配置
APP_NAME=FinanceInsight Pro
APP_VERSION=1.0.0
APP_DESCRIPTION=智能财经新闻分析与A股推荐系统
DEBUG=True
ENVIRONMENT=development

# 服务器配置
HOST=0.0.0.0
PORT=8000
WORKERS=4
RELOAD=True

# 数据库配置
# MySQL主数据库
DATABASE_URL=mysql+pymysql://root:password@localhost:3306/financeinsight
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=password
DB_NAME=financeinsight
DB_CHARSET=utf8mb4

# MySQL读库（可选，用于读写分离）
READ_DATABASE_URL=mysql+pymysql://root:password@localhost:3306/financeinsight

# Redis配置
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# MongoDB配置（用于存储非结构化数据）
MONGODB_URL=mongodb://localhost:27017/financeinsight
MONGODB_HOST=localhost
MONGODB_PORT=27017
MONGODB_DB=financeinsight
MONGODB_USER=
MONGODB_PASSWORD=

# Elasticsearch配置
ELASTICSEARCH_URL=http://localhost:9200
ELASTICSEARCH_HOST=localhost
ELASTICSEARCH_PORT=9200
ELASTICSEARCH_INDEX_PREFIX=financeinsight

# InfluxDB配置（用于时序数据）
INFLUXDB_URL=http://localhost:8086
INFLUXDB_TOKEN=your_influxdb_token
INFLUXDB_ORG=financeinsight
INFLUXDB_BUCKET=stock_data

# JWT配置
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# 加密配置
ENCRYPTION_KEY=your-encryption-key-32-characters

# CORS配置
ALLOWED_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]
ALLOWED_METHODS=["GET", "POST", "PUT", "DELETE", "OPTIONS"]
ALLOWED_HEADERS=["*"]

# API限流配置
RATE_LIMIT_ENABLED=True
RATE_LIMIT_FREE_USER=100/hour
RATE_LIMIT_PRO_USER=500/hour
RATE_LIMIT_ENTERPRISE_USER=2000/hour

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE=logs/app.log
LOG_ROTATION=1 day
LOG_RETENTION=30 days

# 外部API配置
# 东方财富API
EASTMONEY_API_KEY=your_eastmoney_api_key
EASTMONEY_BASE_URL=https://push2.eastmoney.com

# 同花顺API
TONGHUASHUN_API_KEY=your_tonghuashun_api_key
TONGHUASHUN_BASE_URL=https://data.10jqka.com.cn

# 新浪财经API
SINA_FINANCE_BASE_URL=https://hq.sinajs.cn

# 腾讯财经API
TENCENT_FINANCE_BASE_URL=https://qt.gtimg.cn

# 新闻源API配置
# 新华社API
XINHUA_API_KEY=your_xinhua_api_key
XINHUA_BASE_URL=https://api.xinhuanet.com

# 财新网API
CAIXIN_API_KEY=your_caixin_api_key
CAIXIN_BASE_URL=https://api.caixin.com

# 第一财经API
YICAI_API_KEY=your_yicai_api_key
YICAI_BASE_URL=https://api.yicai.com

# 路透社API
REUTERS_API_KEY=your_reuters_api_key
REUTERS_BASE_URL=https://api.reuters.com

# 彭博社API
BLOOMBERG_API_KEY=your_bloomberg_api_key
BLOOMBERG_BASE_URL=https://api.bloomberg.com

# AI模型配置
# OpenAI API（可选）
OPENAI_API_KEY=your_openai_api_key
OPENAI_BASE_URL=https://api.openai.com/v1

# 百度AI API
BAIDU_AI_API_KEY=your_baidu_api_key
BAIDU_AI_SECRET_KEY=your_baidu_secret_key

# 腾讯AI API
TENCENT_AI_SECRET_ID=your_tencent_secret_id
TENCENT_AI_SECRET_KEY=your_tencent_secret_key

# 阿里云AI API
ALIYUN_ACCESS_KEY_ID=your_aliyun_access_key_id
ALIYUN_ACCESS_KEY_SECRET=your_aliyun_access_key_secret

# 模型文件路径
MODEL_PATH=models/
BERT_MODEL_PATH=models/bert-base-chinese
SENTIMENT_MODEL_PATH=models/sentiment_model.pkl
RECOMMENDATION_MODEL_PATH=models/recommendation_model.pkl

# 消息队列配置
# RabbitMQ
RABBITMQ_URL=amqp://guest:guest@localhost:5672/
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USER=guest
RABBITMQ_PASSWORD=guest
RABBITMQ_VHOST=/

# Celery配置
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2
CELERY_TASK_SERIALIZER=json
CELERY_RESULT_SERIALIZER=json
CELERY_ACCEPT_CONTENT=["json"]
CELERY_TIMEZONE=Asia/Shanghai

# 文件存储配置
# 本地存储
UPLOAD_PATH=uploads/
MAX_FILE_SIZE=10485760  # 10MB

# 阿里云OSS
ALIYUN_OSS_ACCESS_KEY_ID=your_oss_access_key_id
ALIYUN_OSS_ACCESS_KEY_SECRET=your_oss_access_key_secret
ALIYUN_OSS_BUCKET=financeinsight-bucket
ALIYUN_OSS_ENDPOINT=https://oss-cn-hangzhou.aliyuncs.com

# 腾讯云COS
TENCENT_COS_SECRET_ID=your_cos_secret_id
TENCENT_COS_SECRET_KEY=your_cos_secret_key
TENCENT_COS_BUCKET=financeinsight-bucket
TENCENT_COS_REGION=ap-beijing

# 邮件服务配置
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_email_password
MAIL_FROM=<EMAIL>
MAIL_PORT=587
MAIL_SERVER=smtp.example.com
MAIL_TLS=True
MAIL_SSL=False

# 短信服务配置
SMS_PROVIDER=aliyun  # aliyun, tencent, twilio
ALIYUN_SMS_ACCESS_KEY_ID=your_sms_access_key_id
ALIYUN_SMS_ACCESS_KEY_SECRET=your_sms_access_key_secret
ALIYUN_SMS_SIGN_NAME=FinanceInsight
ALIYUN_SMS_TEMPLATE_CODE=SMS_123456789

# 推送服务配置
PUSH_PROVIDER=firebase  # firebase, jpush
FIREBASE_SERVER_KEY=your_firebase_server_key
JPUSH_APP_KEY=your_jpush_app_key
JPUSH_MASTER_SECRET=your_jpush_master_secret

# 监控配置
# Sentry错误追踪
SENTRY_DSN=https://<EMAIL>/project_id

# Prometheus监控
PROMETHEUS_ENABLED=True
PROMETHEUS_PORT=9090

# 健康检查
HEALTH_CHECK_ENABLED=True
HEALTH_CHECK_PATH=/health

# 缓存配置
CACHE_TTL=3600  # 1小时
CACHE_MAX_SIZE=1000
CACHE_ENABLED=True

# 爬虫配置
CRAWLER_ENABLED=True
CRAWLER_INTERVAL=300  # 5分钟
CRAWLER_CONCURRENT_REQUESTS=16
CRAWLER_DELAY=1
CRAWLER_USER_AGENT=FinanceInsight-Bot/1.0

# 数据更新配置
DATA_UPDATE_INTERVAL=60  # 1分钟
STOCK_PRICE_UPDATE_INTERVAL=5  # 5秒
NEWS_UPDATE_INTERVAL=30  # 30秒

# 推荐系统配置
RECOMMENDATION_REFRESH_INTERVAL=1800  # 30分钟
RECOMMENDATION_MODEL_RETRAIN_INTERVAL=86400  # 24小时
RECOMMENDATION_TOP_K=20

# 风控配置
RISK_CONTROL_ENABLED=True
MAX_RECOMMENDATION_RISK_LEVEL=5
RISK_WARNING_THRESHOLD=0.8

# 备份配置
BACKUP_ENABLED=True
BACKUP_INTERVAL=86400  # 24小时
BACKUP_RETENTION_DAYS=30
BACKUP_PATH=backups/

# 测试配置
TEST_DATABASE_URL=mysql+pymysql://root:password@localhost:3306/financeinsight_test
TEST_REDIS_URL=redis://localhost:6379/15
