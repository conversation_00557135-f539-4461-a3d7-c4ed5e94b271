# Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# 数据库
sqlalchemy==2.0.23
alembic==1.13.1
pymysql==1.1.0
redis==5.0.1
motor==3.3.2  # MongoDB异步驱动

# 搜索引擎
elasticsearch==8.11.0

# HTTP客户端
httpx==0.25.2
aiohttp==3.9.1
requests==2.31.0

# 数据处理
pandas==2.1.4
numpy==1.25.2
scipy==1.11.4

# 机器学习
scikit-learn==1.3.2
xgboost==2.0.2
lightgbm==4.1.0

# 深度学习
torch==2.1.1
transformers==4.36.2
sentence-transformers==2.2.2

# 自然语言处理
jieba==0.42.1
spacy==3.7.2
textblob==0.17.1
snownlp==0.12.3

# 网页抓取
scrapy==2.11.0
beautifulsoup4==4.12.2
selenium==4.15.2
playwright==1.40.0

# 任务队列
celery==5.3.4
kombu==5.3.4

# 缓存
python-redis-lock==4.0.0
diskcache==5.6.3

# 配置管理
python-dotenv==1.0.0
pyyaml==6.0.1

# 日志
loguru==0.7.2
structlog==23.2.0

# 监控
prometheus-client==0.19.0
sentry-sdk[fastapi]==1.38.0

# 测试
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2
factory-boy==3.3.0

# 代码质量
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# 安全
cryptography==41.0.8
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0
python-multipart==0.0.6

# 时间处理
arrow==1.3.0
python-dateutil==2.8.2

# 数据验证
marshmallow==3.20.1
cerberus==1.3.5

# 图像处理
pillow==10.1.0

# 文件处理
openpyxl==3.1.2
python-docx==1.1.0

# 网络工具
validators==0.22.0
tldextract==5.1.1

# 数学计算
sympy==1.12
statsmodels==0.14.0

# 并发处理
asyncio-throttle==1.0.2
aiofiles==23.2.1

# API文档
fastapi-users==12.1.2
fastapi-pagination==0.12.13

# 中间件
slowapi==0.1.9  # 限流中间件
fastapi-limiter==0.1.5

# 数据序列化
orjson==3.9.10
msgpack==1.0.7

# 环境变量
environs==10.3.0

# 工具库
more-itertools==10.1.0
toolz==0.12.0
funcy==2.0

# 金融数据
yfinance==0.2.28
tushare==1.2.89
akshare==1.12.82

# 技术指标计算
talib-binary==0.4.25
ta==0.10.2

# 时区处理
pytz==2023.3.post1

# 正则表达式
regex==2023.10.3

# 字符串处理
fuzzywuzzy==0.18.0
python-levenshtein==0.23.0

# 配置解析
configparser==6.0.0
toml==0.10.2

# 进程管理
psutil==5.9.6

# 调试工具
ipdb==0.13.13
rich==13.7.0

# 类型检查
typing-extensions==4.8.0

# 异步支持
asyncpg==0.29.0  # PostgreSQL异步驱动（备用）
aiomysql==0.2.0  # MySQL异步驱动

# 消息推送
pyfcm==1.5.4  # Firebase推送
twilio==8.11.1  # 短信服务

# 邮件服务
fastapi-mail==1.4.1
jinja2==3.1.2

# 文档生成
mkdocs==1.5.3
mkdocs-material==9.4.8

# 开发工具
pre-commit==3.6.0
bandit==1.7.5  # 安全检查
