"""
FinanceInsight Pro - 主应用入口
智能财经新闻分析与A股推荐系统
"""

from contextlib import asynccontextmanager
from fastapi import FastAPI, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
import time
import logging
from typing import Dict, Any

from app.core.config import settings
from app.core.database import init_db
from app.core.redis import init_redis
from app.core.elasticsearch import init_elasticsearch
from app.api.v1.api import api_router
from app.core.exceptions import (
    CustomHTTPException,
    ValidationException,
    DatabaseException,
    ExternalAPIException
)
from app.core.middleware import (
    LoggingMiddleware,
    RateLimitMiddleware,
    SecurityMiddleware
)
from app.core.monitoring import setup_monitoring
from app.services.scheduler import start_scheduler

# 配置日志
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL.upper()),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("🚀 启动 FinanceInsight Pro 服务...")
    
    try:
        # 初始化数据库
        await init_db()
        logger.info("✅ 数据库连接成功")
        
        # 初始化Redis
        await init_redis()
        logger.info("✅ Redis连接成功")
        
        # 初始化Elasticsearch
        await init_elasticsearch()
        logger.info("✅ Elasticsearch连接成功")
        
        # 启动定时任务
        if settings.ENVIRONMENT == "production":
            start_scheduler()
            logger.info("✅ 定时任务启动成功")
        
        # 设置监控
        setup_monitoring(app)
        logger.info("✅ 监控系统初始化成功")
        
        logger.info("🎉 FinanceInsight Pro 启动完成!")
        
    except Exception as e:
        logger.error(f"❌ 启动失败: {str(e)}")
        raise
    
    yield
    
    # 关闭时执行
    logger.info("🔄 正在关闭 FinanceInsight Pro 服务...")
    # 这里可以添加清理代码
    logger.info("👋 FinanceInsight Pro 服务已关闭")


# 创建FastAPI应用实例
app = FastAPI(
    title=settings.APP_NAME,
    description=settings.APP_DESCRIPTION,
    version=settings.APP_VERSION,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 添加中间件
# CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=settings.ALLOWED_METHODS,
    allow_headers=settings.ALLOWED_HEADERS,
)

# 可信主机中间件
if settings.ENVIRONMENT == "production":
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=settings.ALLOWED_HOSTS
    )

# 自定义中间件
app.add_middleware(LoggingMiddleware)
app.add_middleware(SecurityMiddleware)

if settings.RATE_LIMIT_ENABLED:
    app.add_middleware(RateLimitMiddleware)


# 异常处理器
@app.exception_handler(CustomHTTPException)
async def custom_http_exception_handler(request: Request, exc: CustomHTTPException):
    """自定义HTTP异常处理"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "code": exc.status_code,
            "message": exc.detail,
            "error": {
                "type": exc.__class__.__name__,
                "details": exc.details
            },
            "timestamp": time.time(),
            "request_id": getattr(request.state, "request_id", None)
        }
    )


@app.exception_handler(StarletteHTTPException)
async def http_exception_handler(request: Request, exc: StarletteHTTPException):
    """HTTP异常处理"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "code": exc.status_code,
            "message": exc.detail,
            "timestamp": time.time(),
            "request_id": getattr(request.state, "request_id", None)
        }
    )


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """请求验证异常处理"""
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={
            "code": 422,
            "message": "请求参数验证失败",
            "error": {
                "type": "ValidationError",
                "details": exc.errors()
            },
            "timestamp": time.time(),
            "request_id": getattr(request.state, "request_id", None)
        }
    )


@app.exception_handler(ValidationException)
async def validation_exception_handler(request: Request, exc: ValidationException):
    """业务验证异常处理"""
    return JSONResponse(
        status_code=status.HTTP_400_BAD_REQUEST,
        content={
            "code": 400,
            "message": exc.message,
            "error": {
                "type": "ValidationException",
                "details": exc.details
            },
            "timestamp": time.time(),
            "request_id": getattr(request.state, "request_id", None)
        }
    )


@app.exception_handler(DatabaseException)
async def database_exception_handler(request: Request, exc: DatabaseException):
    """数据库异常处理"""
    logger.error(f"数据库异常: {exc.message}", exc_info=True)
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "code": 500,
            "message": "数据库操作失败",
            "error": {
                "type": "DatabaseException"
            },
            "timestamp": time.time(),
            "request_id": getattr(request.state, "request_id", None)
        }
    )


@app.exception_handler(ExternalAPIException)
async def external_api_exception_handler(request: Request, exc: ExternalAPIException):
    """外部API异常处理"""
    logger.error(f"外部API异常: {exc.message}", exc_info=True)
    return JSONResponse(
        status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
        content={
            "code": 503,
            "message": "外部服务暂时不可用",
            "error": {
                "type": "ExternalAPIException"
            },
            "timestamp": time.time(),
            "request_id": getattr(request.state, "request_id", None)
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理"""
    logger.error(f"未处理的异常: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "code": 500,
            "message": "服务器内部错误",
            "error": {
                "type": "InternalServerError"
            },
            "timestamp": time.time(),
            "request_id": getattr(request.state, "request_id", None)
        }
    )


# 健康检查端点
@app.get("/health", tags=["健康检查"])
async def health_check() -> Dict[str, Any]:
    """健康检查接口"""
    return {
        "status": "healthy",
        "service": settings.APP_NAME,
        "version": settings.APP_VERSION,
        "timestamp": time.time()
    }


@app.get("/", tags=["根路径"])
async def root() -> Dict[str, Any]:
    """根路径接口"""
    return {
        "message": f"欢迎使用 {settings.APP_NAME}",
        "version": settings.APP_VERSION,
        "docs": "/docs",
        "redoc": "/redoc",
        "health": "/health"
    }


# 包含API路由
app.include_router(api_router, prefix=settings.API_V1_STR)


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        workers=1 if settings.DEBUG else settings.WORKERS,
        log_level=settings.LOG_LEVEL.lower(),
        access_log=True
    )
