"""
数据库连接和会话管理
"""

from typing import Generator
from sqlalchemy import create_engine, event
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool
import logging

from app.core.config import settings
from app.models.base import Base

logger = logging.getLogger(__name__)

# 创建数据库引擎
engine = create_engine(
    settings.DATABASE_URL,
    poolclass=QueuePool,
    pool_size=settings.DB_POOL_SIZE,
    max_overflow=settings.DB_MAX_OVERFLOW,
    pool_timeout=settings.DB_POOL_TIMEOUT,
    pool_recycle=settings.DB_POOL_RECYCLE,
    echo=settings.DB_ECHO,
    connect_args={
        "charset": "utf8mb4",
        "autocommit": False
    }
)

# 读库引擎（如果配置了读写分离）
read_engine = None
if settings.READ_DATABASE_URL:
    read_engine = create_engine(
        settings.READ_DATABASE_URL,
        poolclass=QueuePool,
        pool_size=settings.DB_POOL_SIZE,
        max_overflow=settings.DB_MAX_OVERFLOW,
        pool_timeout=settings.DB_POOL_TIMEOUT,
        pool_recycle=settings.DB_POOL_RECYCLE,
        echo=settings.DB_ECHO,
        connect_args={
            "charset": "utf8mb4",
            "autocommit": False
        }
    )

# 创建会话工厂
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine
)

# 读库会话工厂
ReadSessionLocal = None
if read_engine:
    ReadSessionLocal = sessionmaker(
        autocommit=False,
        autoflush=False,
        bind=read_engine
    )


def get_db() -> Generator[Session, None, None]:
    """
    获取数据库会话（写库）
    """
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"数据库会话异常: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()


def get_read_db() -> Generator[Session, None, None]:
    """
    获取只读数据库会话（读库）
    """
    if ReadSessionLocal:
        db = ReadSessionLocal()
    else:
        db = SessionLocal()
    
    try:
        yield db
    except Exception as e:
        logger.error(f"只读数据库会话异常: {str(e)}")
        raise
    finally:
        db.close()


async def init_db() -> None:
    """
    初始化数据库
    """
    try:
        # 创建所有表
        Base.metadata.create_all(bind=engine)
        logger.info("数据库表创建成功")
        
        # 测试连接
        with SessionLocal() as db:
            db.execute("SELECT 1")
            logger.info("数据库连接测试成功")
            
        # 测试读库连接（如果配置了）
        if ReadSessionLocal:
            with ReadSessionLocal() as db:
                db.execute("SELECT 1")
                logger.info("读库连接测试成功")
                
    except Exception as e:
        logger.error(f"数据库初始化失败: {str(e)}")
        raise


def close_db() -> None:
    """
    关闭数据库连接
    """
    try:
        engine.dispose()
        if read_engine:
            read_engine.dispose()
        logger.info("数据库连接已关闭")
    except Exception as e:
        logger.error(f"关闭数据库连接失败: {str(e)}")


# 数据库事件监听器
@event.listens_for(engine, "connect")
def set_sqlite_pragma(dbapi_connection, connection_record):
    """
    设置数据库连接参数
    """
    if "mysql" in settings.DATABASE_URL:
        # MySQL连接参数
        cursor = dbapi_connection.cursor()
        cursor.execute("SET SESSION sql_mode='STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'")
        cursor.execute("SET SESSION time_zone='+08:00'")
        cursor.close()


@event.listens_for(engine, "checkout")
def receive_checkout(dbapi_connection, connection_record, connection_proxy):
    """
    连接池检出事件
    """
    logger.debug("数据库连接检出")


@event.listens_for(engine, "checkin")
def receive_checkin(dbapi_connection, connection_record):
    """
    连接池检入事件
    """
    logger.debug("数据库连接检入")


class DatabaseManager:
    """
    数据库管理器
    """
    
    def __init__(self):
        self.engine = engine
        self.read_engine = read_engine
        self.session_factory = SessionLocal
        self.read_session_factory = ReadSessionLocal
    
    def get_session(self) -> Session:
        """获取写库会话"""
        return self.session_factory()
    
    def get_read_session(self) -> Session:
        """获取读库会话"""
        if self.read_session_factory:
            return self.read_session_factory()
        return self.session_factory()
    
    def create_tables(self):
        """创建所有表"""
        Base.metadata.create_all(bind=self.engine)
    
    def drop_tables(self):
        """删除所有表"""
        Base.metadata.drop_all(bind=self.engine)
    
    def get_table_names(self):
        """获取所有表名"""
        return Base.metadata.tables.keys()
    
    def execute_sql(self, sql: str, params: dict = None):
        """执行SQL语句"""
        with self.get_session() as db:
            result = db.execute(sql, params or {})
            db.commit()
            return result
    
    def execute_read_sql(self, sql: str, params: dict = None):
        """执行只读SQL语句"""
        with self.get_read_session() as db:
            result = db.execute(sql, params or {})
            return result


# 全局数据库管理器实例
db_manager = DatabaseManager()


class TransactionManager:
    """
    事务管理器
    """
    
    def __init__(self, db: Session):
        self.db = db
        self.in_transaction = False
    
    def __enter__(self):
        self.begin()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is None:
            self.commit()
        else:
            self.rollback()
    
    def begin(self):
        """开始事务"""
        if not self.in_transaction:
            self.db.begin()
            self.in_transaction = True
    
    def commit(self):
        """提交事务"""
        if self.in_transaction:
            self.db.commit()
            self.in_transaction = False
    
    def rollback(self):
        """回滚事务"""
        if self.in_transaction:
            self.db.rollback()
            self.in_transaction = False
    
    def savepoint(self, name: str = None):
        """创建保存点"""
        return self.db.begin_nested()


def with_transaction(func):
    """
    事务装饰器
    """
    def wrapper(*args, **kwargs):
        # 查找Session参数
        db = None
        for arg in args:
            if isinstance(arg, Session):
                db = arg
                break
        
        if 'db' in kwargs:
            db = kwargs['db']
        
        if db is None:
            raise ValueError("未找到数据库会话参数")
        
        with TransactionManager(db):
            return func(*args, **kwargs)
    
    return wrapper


# 数据库健康检查
def check_database_health() -> dict:
    """
    检查数据库健康状态
    """
    try:
        with SessionLocal() as db:
            # 测试写库连接
            result = db.execute("SELECT 1 as test").fetchone()
            write_status = "healthy" if result and result.test == 1 else "unhealthy"
        
        read_status = "not_configured"
        if ReadSessionLocal:
            with ReadSessionLocal() as db:
                # 测试读库连接
                result = db.execute("SELECT 1 as test").fetchone()
                read_status = "healthy" if result and result.test == 1 else "unhealthy"
        
        return {
            "write_db": write_status,
            "read_db": read_status,
            "pool_size": engine.pool.size(),
            "checked_in": engine.pool.checkedin(),
            "checked_out": engine.pool.checkedout(),
            "overflow": engine.pool.overflow(),
            "invalid": engine.pool.invalid()
        }
        
    except Exception as e:
        logger.error(f"数据库健康检查失败: {str(e)}")
        return {
            "write_db": "unhealthy",
            "read_db": "unhealthy",
            "error": str(e)
        }
