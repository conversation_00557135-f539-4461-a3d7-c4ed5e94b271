"""
推荐系统相关API端点
"""

from datetime import datetime, timedelta
from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query, Body
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.auth import get_current_user, get_current_active_user
from app.models.user import User
from app.models.recommendation import (
    Recommendation, 
    RecommendationFeedback, 
    UserPreference,
    RecommendationPerformance
)
from app.schemas.recommendation import (
    RecommendationResponse,
    RecommendationListResponse,
    RecommendationDetailResponse,
    RecommendationRefreshRequest,
    RecommendationFeedbackRequest,
    UserPreferenceResponse,
    UserPreferenceUpdate,
    PerformanceStatsResponse
)
from app.services.recommendation_service import RecommendationService
from app.services.user_preference_service import UserPreferenceService
from app.core.exceptions import ValidationException
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/", response_model=RecommendationListResponse, summary="获取推荐列表")
async def get_recommendations(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    recommendation_type: Optional[str] = Query(None, description="推荐类型"),
    risk_level: Optional[int] = Query(None, ge=1, le=5, description="风险等级"),
    time_horizon: Optional[str] = Query(None, description="投资期限"),
    min_score: Optional[float] = Query(None, ge=0, le=10, description="最小推荐分数"),
    min_confidence: Optional[float] = Query(None, ge=0, le=1, description="最小置信度"),
    is_featured: Optional[bool] = Query(None, description="是否精选"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    获取推荐列表
    
    支持多种筛选条件：
    - **recommendation_type**: 推荐类型（news_based, trend_based, mixed等）
    - **risk_level**: 风险等级（1-5）
    - **time_horizon**: 投资期限（1d, 3d, 1w, 1m）
    - **min_score**: 最小推荐分数
    - **min_confidence**: 最小置信度
    - **is_featured**: 是否精选推荐
    """
    try:
        recommendation_service = RecommendationService(db)
        
        # 构建筛选条件
        filters = {}
        if recommendation_type:
            filters['recommendation_type'] = recommendation_type
        if risk_level:
            filters['risk_level'] = risk_level
        if time_horizon:
            filters['time_horizon'] = time_horizon
        if min_score:
            filters['min_score'] = min_score
        if min_confidence:
            filters['min_confidence'] = min_confidence
        if is_featured is not None:
            filters['is_featured'] = is_featured
        
        # 获取推荐列表
        result = recommendation_service.get_recommendations(
            user_id=current_user.id,
            page=page,
            size=size,
            filters=filters
        )
        
        logger.info(f"获取推荐列表成功: user={current_user.username}, page={page}, size={size}")
        return result
        
    except Exception as e:
        logger.error(f"获取推荐列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取推荐列表失败"
        )


@router.get("/{recommendation_id}", response_model=RecommendationDetailResponse, summary="获取推荐详情")
async def get_recommendation_detail(
    recommendation_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    获取推荐详情
    
    - **recommendation_id**: 推荐ID
    """
    try:
        recommendation_service = RecommendationService(db)
        
        # 获取推荐详情
        recommendation_detail = recommendation_service.get_recommendation_detail(recommendation_id)
        if not recommendation_detail:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="推荐不存在"
            )
        
        # 增加查看次数
        recommendation_service.increment_view_count(recommendation_id)
        
        logger.info(f"获取推荐详情成功: user={current_user.username}, rec_id={recommendation_id}")
        return recommendation_detail
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取推荐详情失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取推荐详情失败"
        )


@router.post("/refresh", response_model=RecommendationListResponse, summary="刷新推荐")
async def refresh_recommendations(
    refresh_request: RecommendationRefreshRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    刷新推荐
    
    根据用户偏好和市场情况生成新的推荐：
    - **user_preferences**: 用户偏好设置
    - **force_refresh**: 是否强制刷新
    """
    try:
        recommendation_service = RecommendationService(db)
        
        # 检查用户权限
        if not current_user.can_access_feature("real_time_data"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="当前订阅类型不支持实时推荐刷新"
            )
        
        # 刷新推荐
        result = recommendation_service.refresh_recommendations(
            user_id=current_user.id,
            preferences=refresh_request.user_preferences,
            force_refresh=refresh_request.force_refresh
        )
        
        logger.info(f"刷新推荐成功: user={current_user.username}, count={len(result.get('items', []))}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"刷新推荐失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="刷新推荐失败"
        )


@router.post("/{recommendation_id}/feedback", summary="提交推荐反馈")
async def submit_feedback(
    recommendation_id: int,
    feedback_request: RecommendationFeedbackRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    提交推荐反馈
    
    - **recommendation_id**: 推荐ID
    - **feedback_type**: 反馈类型（like, dislike, follow, report）
    - **rating**: 评分（1-5）
    - **comment**: 评论
    - **execution_info**: 执行信息
    """
    try:
        recommendation_service = RecommendationService(db)
        
        # 检查推荐是否存在
        recommendation = db.query(Recommendation).filter(
            Recommendation.id == recommendation_id
        ).first()
        if not recommendation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="推荐不存在"
            )
        
        # 提交反馈
        feedback = recommendation_service.submit_feedback(
            recommendation_id=recommendation_id,
            user_id=current_user.id,
            feedback_type=feedback_request.feedback_type,
            rating=feedback_request.rating,
            comment=feedback_request.comment,
            execution_info=feedback_request.execution_info
        )
        
        logger.info(f"提交推荐反馈成功: user={current_user.username}, rec_id={recommendation_id}, type={feedback_request.feedback_type}")
        return {"message": "反馈提交成功", "feedback_id": feedback.id}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"提交推荐反馈失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="提交反馈失败"
        )


@router.get("/performance/stats", response_model=PerformanceStatsResponse, summary="获取推荐表现统计")
async def get_performance_stats(
    period: str = Query("1w", description="统计周期（1d, 1w, 1m, 3m）"),
    recommendation_type: Optional[str] = Query(None, description="推荐类型"),
    risk_level: Optional[int] = Query(None, ge=1, le=5, description="风险等级"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    获取推荐表现统计
    
    - **period**: 统计周期
    - **recommendation_type**: 推荐类型筛选
    - **risk_level**: 风险等级筛选
    """
    try:
        recommendation_service = RecommendationService(db)
        
        # 获取表现统计
        stats = recommendation_service.get_performance_stats(
            period=period,
            recommendation_type=recommendation_type,
            risk_level=risk_level
        )
        
        logger.info(f"获取推荐表现统计成功: user={current_user.username}, period={period}")
        return stats
        
    except Exception as e:
        logger.error(f"获取推荐表现统计失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取统计信息失败"
        )


@router.get("/preferences/", response_model=UserPreferenceResponse, summary="获取用户偏好")
async def get_user_preferences(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    获取用户偏好设置
    """
    try:
        preference_service = UserPreferenceService(db)
        preferences = preference_service.get_user_preferences(current_user.id)
        
        logger.info(f"获取用户偏好成功: user={current_user.username}")
        return preferences
        
    except Exception as e:
        logger.error(f"获取用户偏好失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户偏好失败"
        )


@router.put("/preferences/", response_model=UserPreferenceResponse, summary="更新用户偏好")
async def update_user_preferences(
    preference_update: UserPreferenceUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    更新用户偏好设置
    
    - **risk_tolerance**: 风险承受能力（1-5）
    - **preferred_time_horizon**: 偏好投资期限
    - **preferred_industries**: 偏好行业
    - **excluded_industries**: 排除行业
    - **preferred_stocks**: 偏好股票
    - **excluded_stocks**: 排除股票
    - **notification_settings**: 通知设置
    """
    try:
        preference_service = UserPreferenceService(db)
        
        # 更新用户偏好
        preferences = preference_service.update_user_preferences(
            user_id=current_user.id,
            preference_data=preference_update.dict(exclude_unset=True)
        )
        
        logger.info(f"更新用户偏好成功: user={current_user.username}")
        return preferences
        
    except Exception as e:
        logger.error(f"更新用户偏好失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新用户偏好失败"
        )


@router.get("/featured/", response_model=List[RecommendationResponse], summary="获取精选推荐")
async def get_featured_recommendations(
    limit: int = Query(10, ge=1, le=50, description="返回数量"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    获取精选推荐
    
    - **limit**: 返回数量限制
    """
    try:
        recommendation_service = RecommendationService(db)
        
        # 获取精选推荐
        featured_recommendations = recommendation_service.get_featured_recommendations(
            user_id=current_user.id,
            limit=limit
        )
        
        logger.info(f"获取精选推荐成功: user={current_user.username}, count={len(featured_recommendations)}")
        return featured_recommendations
        
    except Exception as e:
        logger.error(f"获取精选推荐失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取精选推荐失败"
        )


@router.get("/history/", summary="获取推荐历史")
async def get_recommendation_history(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    获取用户推荐历史
    
    - **start_date**: 开始日期
    - **end_date**: 结束日期
    """
    try:
        recommendation_service = RecommendationService(db)
        
        # 获取推荐历史
        history = recommendation_service.get_user_recommendation_history(
            user_id=current_user.id,
            page=page,
            size=size,
            start_date=start_date,
            end_date=end_date
        )
        
        logger.info(f"获取推荐历史成功: user={current_user.username}, page={page}")
        return history
        
    except Exception as e:
        logger.error(f"获取推荐历史失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取推荐历史失败"
        )
