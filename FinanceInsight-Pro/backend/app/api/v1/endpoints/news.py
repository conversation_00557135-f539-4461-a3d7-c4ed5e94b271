"""
新闻相关API端点
"""

from datetime import datetime, timedelta
from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_, or_

from app.core.database import get_db
from app.core.auth import get_current_user, get_current_active_user
from app.models.user import User
from app.models.news import News, NewsAnalysis, NewsSource
from app.schemas.news import (
    NewsResponse,
    NewsListResponse,
    NewsDetailResponse,
    NewsSearchRequest,
    HotNewsResponse
)
from app.services.news_service import NewsService
from app.services.analysis_service import AnalysisService
from app.core.exceptions import ValidationException
from app.utils.pagination import paginate
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/", response_model=NewsListResponse, summary="获取新闻列表")
async def get_news_list(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    category: Optional[str] = Query(None, description="新闻分类"),
    sentiment: Optional[str] = Query(None, description="情感倾向"),
    source: Optional[str] = Query(None, description="新闻来源"),
    keyword: Optional[str] = Query(None, description="关键词搜索"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    importance_level: Optional[int] = Query(None, ge=1, le=5, description="重要性等级"),
    is_hot: Optional[bool] = Query(None, description="是否热点"),
    is_featured: Optional[bool] = Query(None, description="是否精选"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    获取新闻列表
    
    支持多种筛选条件：
    - **category**: 新闻分类（policy, earnings, merger, international等）
    - **sentiment**: 情感倾向（positive, negative, neutral）
    - **source**: 新闻来源
    - **keyword**: 关键词搜索（标题和内容）
    - **start_date**: 开始日期
    - **end_date**: 结束日期
    - **importance_level**: 重要性等级（1-5）
    - **is_hot**: 是否热点新闻
    - **is_featured**: 是否精选新闻
    """
    try:
        news_service = NewsService(db)
        
        # 构建查询条件
        filters = {}
        if category:
            filters['category'] = category
        if sentiment:
            filters['sentiment'] = sentiment
        if source:
            filters['source'] = source
        if importance_level:
            filters['importance_level'] = importance_level
        if is_hot is not None:
            filters['is_hot'] = is_hot
        if is_featured is not None:
            filters['is_featured'] = is_featured
        if start_date:
            filters['start_date'] = start_date
        if end_date:
            filters['end_date'] = end_date
        
        # 获取新闻列表
        result = news_service.get_news_list(
            page=page,
            size=size,
            keyword=keyword,
            filters=filters
        )
        
        logger.info(f"获取新闻列表成功: user={current_user.username}, page={page}, size={size}")
        return result
        
    except Exception as e:
        logger.error(f"获取新闻列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取新闻列表失败"
        )


@router.get("/hot", response_model=List[HotNewsResponse], summary="获取热点新闻")
async def get_hot_news(
    period: str = Query("1d", description="时间周期（1h, 6h, 1d, 3d, 1w）"),
    limit: int = Query(10, ge=1, le=50, description="返回数量"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    获取热点新闻
    
    - **period**: 时间周期
      - 1h: 1小时内
      - 6h: 6小时内
      - 1d: 1天内
      - 3d: 3天内
      - 1w: 1周内
    - **limit**: 返回数量限制
    """
    try:
        news_service = NewsService(db)
        
        # 解析时间周期
        period_hours = {
            "1h": 1,
            "6h": 6,
            "1d": 24,
            "3d": 72,
            "1w": 168
        }
        
        if period not in period_hours:
            raise ValidationException("无效的时间周期")
        
        hours = period_hours[period]
        since_time = datetime.utcnow() - timedelta(hours=hours)
        
        # 获取热点新闻
        hot_news = news_service.get_hot_news(
            since_time=since_time,
            limit=limit
        )
        
        logger.info(f"获取热点新闻成功: user={current_user.username}, period={period}, count={len(hot_news)}")
        return hot_news
        
    except ValidationException:
        raise
    except Exception as e:
        logger.error(f"获取热点新闻失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取热点新闻失败"
        )


@router.get("/{news_id}", response_model=NewsDetailResponse, summary="获取新闻详情")
async def get_news_detail(
    news_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    获取新闻详情
    
    - **news_id**: 新闻ID
    """
    try:
        news_service = NewsService(db)
        
        # 获取新闻详情
        news_detail = news_service.get_news_detail(news_id)
        if not news_detail:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="新闻不存在"
            )
        
        # 增加查看次数
        news_service.increment_view_count(news_id)
        
        logger.info(f"获取新闻详情成功: user={current_user.username}, news_id={news_id}")
        return news_detail
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取新闻详情失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取新闻详情失败"
        )


@router.post("/search", response_model=NewsListResponse, summary="搜索新闻")
async def search_news(
    search_request: NewsSearchRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    搜索新闻
    
    支持复杂搜索条件：
    - **query**: 搜索关键词
    - **filters**: 筛选条件
    - **sort_by**: 排序字段
    - **sort_order**: 排序方向
    """
    try:
        news_service = NewsService(db)
        
        # 执行搜索
        result = news_service.search_news(
            query=search_request.query,
            filters=search_request.filters,
            page=search_request.page,
            size=search_request.size,
            sort_by=search_request.sort_by,
            sort_order=search_request.sort_order
        )
        
        logger.info(f"搜索新闻成功: user={current_user.username}, query='{search_request.query}'")
        return result
        
    except Exception as e:
        logger.error(f"搜索新闻失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="搜索新闻失败"
        )


@router.get("/sources/", summary="获取新闻源列表")
async def get_news_sources(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    获取新闻源列表
    """
    try:
        sources = db.query(NewsSource).filter(
            NewsSource.is_active == True,
            NewsSource.is_deleted == False
        ).all()
        
        result = []
        for source in sources:
            result.append({
                "id": source.id,
                "name": source.name,
                "domain": source.domain,
                "reliability_score": float(source.reliability_score) if source.reliability_score else None,
                "quality_score": float(source.quality_score) if source.quality_score else None,
                "total_news": source.total_news,
                "last_crawl_time": source.last_crawl_time.isoformat() if source.last_crawl_time else None
            })
        
        logger.info(f"获取新闻源列表成功: user={current_user.username}, count={len(result)}")
        return {"sources": result}
        
    except Exception as e:
        logger.error(f"获取新闻源列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取新闻源列表失败"
        )


@router.get("/categories/", summary="获取新闻分类")
async def get_news_categories(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    获取新闻分类统计
    """
    try:
        news_service = NewsService(db)
        categories = news_service.get_category_stats()
        
        logger.info(f"获取新闻分类成功: user={current_user.username}")
        return {"categories": categories}
        
    except Exception as e:
        logger.error(f"获取新闻分类失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取新闻分类失败"
        )


@router.post("/{news_id}/like", summary="点赞新闻")
async def like_news(
    news_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    点赞新闻
    
    - **news_id**: 新闻ID
    """
    try:
        news_service = NewsService(db)
        
        # 检查新闻是否存在
        news = db.query(News).filter(News.id == news_id).first()
        if not news:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="新闻不存在"
            )
        
        # 增加点赞数
        news_service.increment_like_count(news_id)
        
        logger.info(f"点赞新闻成功: user={current_user.username}, news_id={news_id}")
        return {"message": "点赞成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"点赞新闻失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="点赞失败"
        )


@router.post("/{news_id}/share", summary="分享新闻")
async def share_news(
    news_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    分享新闻
    
    - **news_id**: 新闻ID
    """
    try:
        news_service = NewsService(db)
        
        # 检查新闻是否存在
        news = db.query(News).filter(News.id == news_id).first()
        if not news:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="新闻不存在"
            )
        
        # 增加分享数
        news_service.increment_share_count(news_id)
        
        logger.info(f"分享新闻成功: user={current_user.username}, news_id={news_id}")
        return {"message": "分享成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"分享新闻失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="分享失败"
        )


@router.get("/stats/overview", summary="获取新闻统计概览")
async def get_news_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    获取新闻统计概览
    """
    try:
        news_service = NewsService(db)
        stats = news_service.get_news_stats()
        
        logger.info(f"获取新闻统计成功: user={current_user.username}")
        return stats
        
    except Exception as e:
        logger.error(f"获取新闻统计失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取统计信息失败"
        )
