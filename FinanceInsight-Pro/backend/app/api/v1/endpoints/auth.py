"""
认证相关API端点
"""

from datetime import timedelta
from typing import Any, Dict
from fastapi import APIRouter, Depends, HTTPException, status, Body
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session

from app.core.config import settings
from app.core.database import get_db
from app.core.security import (
    create_access_token,
    create_refresh_token,
    verify_password,
    get_password_hash,
    verify_token
)
from app.models.user import User, UserSession
from app.schemas.auth import (
    Token,
    TokenRefresh,
    UserRegister,
    UserLogin,
    PasswordReset,
    PasswordChange
)
from app.schemas.user import UserResponse
from app.services.user_service import UserService
from app.services.auth_service import AuthService
from app.core.exceptions import ValidationException
from app.utils.validators import validate_email, validate_password
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/register", response_model=UserResponse, summary="用户注册")
async def register(
    user_data: UserRegister,
    db: Session = Depends(get_db)
) -> Any:
    """
    用户注册
    
    - **username**: 用户名（3-50字符，字母数字下划线）
    - **email**: 邮箱地址
    - **password**: 密码（8-50字符，包含字母和数字）
    - **phone**: 手机号（可选）
    """
    try:
        # 验证邮箱格式
        if not validate_email(user_data.email):
            raise ValidationException("邮箱格式不正确")
        
        # 验证密码强度
        if not validate_password(user_data.password):
            raise ValidationException("密码必须包含字母和数字，长度8-50字符")
        
        # 检查用户名是否已存在
        existing_user = db.query(User).filter(User.username == user_data.username).first()
        if existing_user:
            raise ValidationException("用户名已存在")
        
        # 检查邮箱是否已存在
        existing_email = db.query(User).filter(User.email == user_data.email).first()
        if existing_email:
            raise ValidationException("邮箱已被注册")
        
        # 检查手机号是否已存在（如果提供）
        if user_data.phone:
            existing_phone = db.query(User).filter(User.phone == user_data.phone).first()
            if existing_phone:
                raise ValidationException("手机号已被注册")
        
        # 创建用户
        user_service = UserService(db)
        user = user_service.create_user(
            username=user_data.username,
            email=user_data.email,
            password=user_data.password,
            phone=user_data.phone,
            nickname=user_data.nickname
        )
        
        logger.info(f"用户注册成功: {user.username} ({user.email})")
        return user
        
    except ValidationException:
        raise
    except Exception as e:
        logger.error(f"用户注册失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="注册失败，请稍后重试"
        )


@router.post("/login", response_model=Token, summary="用户登录")
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
) -> Any:
    """
    用户登录
    
    - **username**: 用户名或邮箱
    - **password**: 密码
    """
    try:
        auth_service = AuthService(db)
        
        # 验证用户凭据
        user = auth_service.authenticate_user(form_data.username, form_data.password)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # 检查用户状态
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="账户已被禁用"
            )
        
        if user.is_locked:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=f"账户已被锁定，请稍后重试"
            )
        
        # 创建访问令牌
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": str(user.id)}, 
            expires_delta=access_token_expires
        )
        
        # 创建刷新令牌
        refresh_token_expires = timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
        refresh_token = create_refresh_token(
            data={"sub": str(user.id)}, 
            expires_delta=refresh_token_expires
        )
        
        # 创建会话记录
        auth_service.create_session(
            user_id=user.id,
            access_token=access_token,
            refresh_token=refresh_token,
            expires_delta=access_token_expires
        )
        
        # 更新登录信息
        user.increment_login_count()
        db.commit()
        
        logger.info(f"用户登录成功: {user.username}")
        
        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer",
            "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            "user_info": {
                "user_id": user.id,
                "username": user.username,
                "email": user.email,
                "subscription_type": user.subscription_type,
                "is_verified": user.is_verified
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"用户登录失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录失败，请稍后重试"
        )


@router.post("/refresh", response_model=Token, summary="刷新令牌")
async def refresh_token(
    token_data: TokenRefresh,
    db: Session = Depends(get_db)
) -> Any:
    """
    刷新访问令牌
    
    - **refresh_token**: 刷新令牌
    """
    try:
        auth_service = AuthService(db)
        
        # 验证刷新令牌
        payload = verify_token(token_data.refresh_token)
        user_id = int(payload.get("sub"))
        
        # 检查会话是否存在且有效
        session = db.query(UserSession).filter(
            UserSession.refresh_token == token_data.refresh_token,
            UserSession.is_active == True
        ).first()
        
        if not session or session.is_expired:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="刷新令牌无效或已过期"
            )
        
        # 获取用户信息
        user = db.query(User).filter(User.id == user_id).first()
        if not user or not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户不存在或已被禁用"
            )
        
        # 创建新的访问令牌
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        new_access_token = create_access_token(
            data={"sub": str(user.id)}, 
            expires_delta=access_token_expires
        )
        
        # 更新会话
        session.session_token = new_access_token
        session.extend_session(settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        db.commit()
        
        logger.info(f"令牌刷新成功: {user.username}")
        
        return {
            "access_token": new_access_token,
            "refresh_token": token_data.refresh_token,
            "token_type": "bearer",
            "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            "user_info": {
                "user_id": user.id,
                "username": user.username,
                "email": user.email,
                "subscription_type": user.subscription_type,
                "is_verified": user.is_verified
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"令牌刷新失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="令牌刷新失败"
        )


@router.post("/logout", summary="用户登出")
async def logout(
    token: str = Body(..., embed=True),
    db: Session = Depends(get_db)
) -> Any:
    """
    用户登出
    
    - **token**: 访问令牌
    """
    try:
        # 查找并停用会话
        session = db.query(UserSession).filter(
            UserSession.session_token == token,
            UserSession.is_active == True
        ).first()
        
        if session:
            session.is_active = False
            db.commit()
            logger.info(f"用户登出成功: user_id={session.user_id}")
        
        return {"message": "登出成功"}
        
    except Exception as e:
        logger.error(f"用户登出失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登出失败"
        )


@router.post("/forgot-password", summary="忘记密码")
async def forgot_password(
    email: str = Body(..., embed=True),
    db: Session = Depends(get_db)
) -> Any:
    """
    忘记密码 - 发送重置邮件
    
    - **email**: 邮箱地址
    """
    try:
        # 验证邮箱格式
        if not validate_email(email):
            raise ValidationException("邮箱格式不正确")
        
        # 查找用户
        user = db.query(User).filter(User.email == email).first()
        if not user:
            # 为了安全，不透露用户是否存在
            return {"message": "如果邮箱存在，重置链接已发送"}
        
        auth_service = AuthService(db)
        
        # 生成重置令牌并发送邮件
        reset_token = auth_service.generate_password_reset_token(user.id)
        # TODO: 发送重置邮件
        
        logger.info(f"密码重置邮件已发送: {email}")
        return {"message": "如果邮箱存在，重置链接已发送"}
        
    except ValidationException:
        raise
    except Exception as e:
        logger.error(f"发送重置邮件失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="发送失败，请稍后重试"
        )


@router.post("/reset-password", summary="重置密码")
async def reset_password(
    reset_data: PasswordReset,
    db: Session = Depends(get_db)
) -> Any:
    """
    重置密码
    
    - **token**: 重置令牌
    - **new_password**: 新密码
    """
    try:
        # 验证密码强度
        if not validate_password(reset_data.new_password):
            raise ValidationException("密码必须包含字母和数字，长度8-50字符")
        
        auth_service = AuthService(db)
        
        # 验证重置令牌并重置密码
        success = auth_service.reset_password(reset_data.token, reset_data.new_password)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="重置令牌无效或已过期"
            )
        
        logger.info("密码重置成功")
        return {"message": "密码重置成功"}
        
    except ValidationException:
        raise
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"密码重置失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="重置失败，请稍后重试"
        )


@router.post("/change-password", summary="修改密码")
async def change_password(
    password_data: PasswordChange,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    修改密码（需要登录）
    
    - **old_password**: 旧密码
    - **new_password**: 新密码
    """
    try:
        # 验证旧密码
        if not verify_password(password_data.old_password, current_user.password_hash):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="旧密码不正确"
            )
        
        # 验证新密码强度
        if not validate_password(password_data.new_password):
            raise ValidationException("密码必须包含字母和数字，长度8-50字符")
        
        # 检查新密码是否与旧密码相同
        if password_data.old_password == password_data.new_password:
            raise ValidationException("新密码不能与旧密码相同")
        
        # 更新密码
        current_user.password_hash = get_password_hash(password_data.new_password)
        current_user.password_changed_at = datetime.utcnow()
        db.commit()
        
        # 使所有会话失效（强制重新登录）
        db.query(UserSession).filter(
            UserSession.user_id == current_user.id,
            UserSession.is_active == True
        ).update({"is_active": False})
        db.commit()
        
        logger.info(f"密码修改成功: {current_user.username}")
        return {"message": "密码修改成功，请重新登录"}
        
    except ValidationException:
        raise
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"密码修改失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="修改失败，请稍后重试"
        )
