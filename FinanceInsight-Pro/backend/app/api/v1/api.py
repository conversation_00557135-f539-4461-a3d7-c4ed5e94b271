"""
API v1 路由汇总
"""

from fastapi import APIRouter

from app.api.v1.endpoints import (
    auth,
    users,
    news,
    stocks,
    recommendations,
    analysis,
    admin
)

api_router = APIRouter()

# 认证相关路由
api_router.include_router(
    auth.router, 
    prefix="/auth", 
    tags=["认证"]
)

# 用户相关路由
api_router.include_router(
    users.router, 
    prefix="/users", 
    tags=["用户管理"]
)

# 新闻相关路由
api_router.include_router(
    news.router, 
    prefix="/news", 
    tags=["新闻管理"]
)

# 股票相关路由
api_router.include_router(
    stocks.router, 
    prefix="/stocks", 
    tags=["股票数据"]
)

# 推荐相关路由
api_router.include_router(
    recommendations.router, 
    prefix="/recommendations", 
    tags=["推荐系统"]
)

# 分析相关路由
api_router.include_router(
    analysis.router, 
    prefix="/analysis", 
    tags=["数据分析"]
)

# 管理员相关路由
api_router.include_router(
    admin.router, 
    prefix="/admin", 
    tags=["系统管理"]
)
