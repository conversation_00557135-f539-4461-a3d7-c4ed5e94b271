"""
用户相关数据模型
"""

from datetime import datetime, timed<PERSON>ta
from typing import Optional
from sqlalchemy import Column, String, Integer, Boolean, DateTime, Text, Enum, JSON, Numeric
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum

from .base import SimpleBase


class SubscriptionType(str, enum.Enum):
    """订阅类型枚举"""
    FREE = "free"
    PRO = "pro"
    ENTERPRISE = "enterprise"


class UserStatus(int, enum.Enum):
    """用户状态枚举"""
    INACTIVE = 0
    ACTIVE = 1
    SUSPENDED = 2
    BANNED = 3


class User(SimpleBase):
    """用户模型"""
    __tablename__ = "users"
    
    # 基本信息
    username = Column(String(50), unique=True, nullable=False, index=True, comment="用户名")
    email = Column(String(100), unique=True, nullable=False, index=True, comment="邮箱")
    phone = Column(String(20), unique=True, nullable=True, index=True, comment="手机号")
    password_hash = Column(String(255), nullable=False, comment="密码哈希")
    
    # 个人信息
    nickname = Column(String(100), nullable=True, comment="昵称")
    avatar = Column(String(500), nullable=True, comment="头像URL")
    real_name = Column(String(50), nullable=True, comment="真实姓名")
    id_card = Column(String(20), nullable=True, comment="身份证号")
    gender = Column(Integer, default=0, comment="性别：0-未知，1-男，2-女")
    birthday = Column(DateTime, nullable=True, comment="生日")
    
    # 联系信息
    address = Column(String(500), nullable=True, comment="地址")
    company = Column(String(200), nullable=True, comment="公司")
    position = Column(String(100), nullable=True, comment="职位")
    
    # 账户状态
    status = Column(Enum(UserStatus), default=UserStatus.ACTIVE, nullable=False, comment="用户状态")
    is_verified = Column(Boolean, default=False, nullable=False, comment="是否已验证")
    is_email_verified = Column(Boolean, default=False, nullable=False, comment="邮箱是否已验证")
    is_phone_verified = Column(Boolean, default=False, nullable=False, comment="手机是否已验证")
    
    # 订阅信息
    subscription_type = Column(
        Enum(SubscriptionType), 
        default=SubscriptionType.FREE, 
        nullable=False, 
        comment="订阅类型"
    )
    subscription_expires = Column(DateTime, nullable=True, comment="订阅到期时间")
    
    # 登录信息
    last_login_at = Column(DateTime, nullable=True, comment="最后登录时间")
    last_login_ip = Column(String(45), nullable=True, comment="最后登录IP")
    login_count = Column(Integer, default=0, nullable=False, comment="登录次数")
    
    # 安全信息
    failed_login_attempts = Column(Integer, default=0, nullable=False, comment="失败登录次数")
    locked_until = Column(DateTime, nullable=True, comment="锁定到期时间")
    password_changed_at = Column(DateTime, default=func.now(), comment="密码修改时间")
    
    # 偏好设置
    preferences = Column(JSON, nullable=True, comment="用户偏好设置")
    notification_settings = Column(JSON, nullable=True, comment="通知设置")
    
    # 统计信息
    api_calls_today = Column(Integer, default=0, nullable=False, comment="今日API调用次数")
    api_calls_total = Column(Integer, default=0, nullable=False, comment="总API调用次数")
    recommendations_viewed = Column(Integer, default=0, nullable=False, comment="查看推荐次数")
    
    # 关联关系
    # user_sessions = relationship("UserSession", back_populates="user")
    # user_logs = relationship("UserLog", back_populates="user")
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', email='{self.email}')>"
    
    @property
    def is_active(self) -> bool:
        """是否激活"""
        return self.status == UserStatus.ACTIVE and not self.is_deleted
    
    @property
    def is_locked(self) -> bool:
        """是否被锁定"""
        if self.locked_until is None:
            return False
        return datetime.utcnow() < self.locked_until
    
    @property
    def is_subscription_active(self) -> bool:
        """订阅是否有效"""
        if self.subscription_type == SubscriptionType.FREE:
            return True
        if self.subscription_expires is None:
            return False
        return datetime.utcnow() < self.subscription_expires
    
    @property
    def subscription_days_left(self) -> int:
        """订阅剩余天数"""
        if self.subscription_type == SubscriptionType.FREE:
            return -1  # 免费用户无限制
        if self.subscription_expires is None:
            return 0
        delta = self.subscription_expires - datetime.utcnow()
        return max(0, delta.days)
    
    def get_rate_limit(self) -> str:
        """获取用户的API限流配置"""
        if self.subscription_type == SubscriptionType.ENTERPRISE:
            return "2000/hour"
        elif self.subscription_type == SubscriptionType.PRO:
            return "500/hour"
        else:
            return "100/hour"
    
    def can_access_feature(self, feature: str) -> bool:
        """检查用户是否可以访问某个功能"""
        if not self.is_active or not self.is_subscription_active:
            return False
        
        # 功能权限映射
        feature_permissions = {
            "basic_news": [SubscriptionType.FREE, SubscriptionType.PRO, SubscriptionType.ENTERPRISE],
            "advanced_analysis": [SubscriptionType.PRO, SubscriptionType.ENTERPRISE],
            "real_time_data": [SubscriptionType.PRO, SubscriptionType.ENTERPRISE],
            "custom_alerts": [SubscriptionType.PRO, SubscriptionType.ENTERPRISE],
            "api_access": [SubscriptionType.PRO, SubscriptionType.ENTERPRISE],
            "bulk_export": [SubscriptionType.ENTERPRISE],
            "custom_models": [SubscriptionType.ENTERPRISE],
        }
        
        allowed_types = feature_permissions.get(feature, [])
        return self.subscription_type in allowed_types
    
    def increment_login_count(self, ip: str = None):
        """增加登录次数"""
        self.login_count += 1
        self.last_login_at = func.now()
        if ip:
            self.last_login_ip = ip
        self.failed_login_attempts = 0  # 重置失败次数
    
    def increment_failed_login(self):
        """增加失败登录次数"""
        self.failed_login_attempts += 1
        # 如果失败次数超过5次，锁定账户1小时
        if self.failed_login_attempts >= 5:
            self.locked_until = datetime.utcnow() + timedelta(hours=1)
    
    def reset_failed_login(self):
        """重置失败登录次数"""
        self.failed_login_attempts = 0
        self.locked_until = None
    
    def extend_subscription(self, days: int):
        """延长订阅"""
        if self.subscription_expires is None:
            self.subscription_expires = datetime.utcnow()
        self.subscription_expires += timedelta(days=days)
    
    def upgrade_subscription(self, new_type: SubscriptionType, days: int = 30):
        """升级订阅"""
        self.subscription_type = new_type
        if new_type != SubscriptionType.FREE:
            if self.subscription_expires is None or self.subscription_expires < datetime.utcnow():
                self.subscription_expires = datetime.utcnow() + timedelta(days=days)
            else:
                self.subscription_expires += timedelta(days=days)


class UserSession(SimpleBase):
    """用户会话模型"""
    __tablename__ = "user_sessions"
    
    user_id = Column(Integer, nullable=False, index=True, comment="用户ID")
    session_token = Column(String(255), unique=True, nullable=False, index=True, comment="会话令牌")
    refresh_token = Column(String(255), unique=True, nullable=True, index=True, comment="刷新令牌")
    expires_at = Column(DateTime, nullable=False, comment="过期时间")
    ip_address = Column(String(45), nullable=True, comment="IP地址")
    user_agent = Column(Text, nullable=True, comment="用户代理")
    device_info = Column(JSON, nullable=True, comment="设备信息")
    is_active = Column(Boolean, default=True, nullable=False, comment="是否活跃")
    
    # user = relationship("User", back_populates="user_sessions")
    
    def __repr__(self):
        return f"<UserSession(id={self.id}, user_id={self.user_id}, token='{self.session_token[:10]}...')>"
    
    @property
    def is_expired(self) -> bool:
        """是否过期"""
        return datetime.utcnow() > self.expires_at
    
    def extend_session(self, minutes: int = 30):
        """延长会话"""
        self.expires_at = datetime.utcnow() + timedelta(minutes=minutes)


class UserLog(SimpleBase):
    """用户操作日志模型"""
    __tablename__ = "user_logs"
    
    user_id = Column(Integer, nullable=False, index=True, comment="用户ID")
    action = Column(String(100), nullable=False, comment="操作类型")
    resource = Column(String(100), nullable=True, comment="操作资源")
    resource_id = Column(String(50), nullable=True, comment="资源ID")
    ip_address = Column(String(45), nullable=True, comment="IP地址")
    user_agent = Column(Text, nullable=True, comment="用户代理")
    request_data = Column(JSON, nullable=True, comment="请求数据")
    response_data = Column(JSON, nullable=True, comment="响应数据")
    status_code = Column(Integer, nullable=True, comment="状态码")
    execution_time = Column(Numeric(10, 3), nullable=True, comment="执行时间(秒)")
    
    # user = relationship("User", back_populates="user_logs")
    
    def __repr__(self):
        return f"<UserLog(id={self.id}, user_id={self.user_id}, action='{self.action}')>"
