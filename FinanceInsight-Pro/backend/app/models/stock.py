"""
股票相关数据模型
"""

from datetime import datetime, date
from typing import Optional, List
from sqlalchemy import Column, String, Integer, Text, DateTime, Numeric, JSON, Boolean, Date, Index
from sqlalchemy.orm import relationship
import enum

from .base import SimpleBase


class Market(str, enum.Enum):
    """市场枚举"""
    SH = "sh"  # 上海证券交易所
    SZ = "sz"  # 深圳证券交易所
    BJ = "bj"  # 北京证券交易所
    HK = "hk"  # 香港证券交易所
    US = "us"  # 美国证券交易所


class StockStatus(int, enum.Enum):
    """股票状态枚举"""
    NORMAL = 1  # 正常交易
    SUSPENDED = 2  # 停牌
    DELISTED = 3  # 退市
    ST = 4  # ST股票
    STAR_ST = 5  # *ST股票


class Stock(SimpleBase):
    """股票基础信息模型"""
    __tablename__ = "stocks"
    
    # 基本信息
    code = Column(String(10), unique=True, nullable=False, index=True, comment="股票代码")
    name = Column(String(100), nullable=False, index=True, comment="股票名称")
    full_name = Column(String(200), nullable=True, comment="股票全称")
    english_name = Column(String(200), nullable=True, comment="英文名称")
    
    # 市场信息
    market = Column(String(10), nullable=False, index=True, comment="所属市场")
    board = Column(String(50), nullable=True, comment="板块")
    industry = Column(String(100), nullable=True, index=True, comment="所属行业")
    sector = Column(String(100), nullable=True, comment="所属板块")
    concept = Column(JSON, nullable=True, comment="概念标签")
    
    # 上市信息
    listing_date = Column(Date, nullable=True, comment="上市日期")
    ipo_price = Column(Numeric(10, 4), nullable=True, comment="发行价格")
    
    # 股本信息
    total_shares = Column(Numeric(20, 0), nullable=True, comment="总股本")
    float_shares = Column(Numeric(20, 0), nullable=True, comment="流通股本")
    restricted_shares = Column(Numeric(20, 0), nullable=True, comment="限售股本")
    
    # 财务指标
    market_cap = Column(Numeric(20, 2), nullable=True, comment="总市值")
    float_market_cap = Column(Numeric(20, 2), nullable=True, comment="流通市值")
    pe_ratio = Column(Numeric(10, 4), nullable=True, comment="市盈率")
    pb_ratio = Column(Numeric(10, 4), nullable=True, comment="市净率")
    ps_ratio = Column(Numeric(10, 4), nullable=True, comment="市销率")
    roe = Column(Numeric(10, 4), nullable=True, comment="净资产收益率")
    roa = Column(Numeric(10, 4), nullable=True, comment="总资产收益率")
    
    # 价格信息（最新）
    current_price = Column(Numeric(10, 4), nullable=True, comment="当前价格")
    prev_close = Column(Numeric(10, 4), nullable=True, comment="昨收价")
    change_amount = Column(Numeric(10, 4), nullable=True, comment="涨跌额")
    change_percent = Column(Numeric(10, 4), nullable=True, comment="涨跌幅")
    
    # 交易信息
    volume = Column(Numeric(20, 0), nullable=True, comment="成交量")
    turnover = Column(Numeric(20, 2), nullable=True, comment="成交额")
    turnover_rate = Column(Numeric(10, 4), nullable=True, comment="换手率")
    
    # 价格区间
    day_high = Column(Numeric(10, 4), nullable=True, comment="日最高价")
    day_low = Column(Numeric(10, 4), nullable=True, comment="日最低价")
    week_52_high = Column(Numeric(10, 4), nullable=True, comment="52周最高价")
    week_52_low = Column(Numeric(10, 4), nullable=True, comment="52周最低价")
    
    # 状态信息
    status = Column(Integer, default=StockStatus.NORMAL, comment="股票状态")
    is_tradable = Column(Boolean, default=True, comment="是否可交易")
    
    # 更新时间
    price_updated_at = Column(DateTime, nullable=True, comment="价格更新时间")
    info_updated_at = Column(DateTime, nullable=True, comment="信息更新时间")
    
    # 统计信息
    news_count = Column(Integer, default=0, comment="相关新闻数量")
    recommendation_count = Column(Integer, default=0, comment="推荐次数")
    
    # 索引
    __table_args__ = (
        Index('idx_stock_market_industry', 'market', 'industry'),
        Index('idx_stock_market_cap', 'market_cap'),
        Index('idx_stock_change_percent', 'change_percent'),
        Index('idx_stock_volume', 'volume'),
        Index('idx_stock_status', 'status', 'is_tradable'),
    )
    
    def __repr__(self):
        return f"<Stock(id={self.id}, code='{self.code}', name='{self.name}')>"
    
    @property
    def display_code(self) -> str:
        """显示用的股票代码"""
        if self.market == Market.SH:
            return f"1A{self.code}" if self.code.startswith("000") else f"sh{self.code}"
        elif self.market == Market.SZ:
            return f"sz{self.code}"
        return self.code
    
    @property
    def is_st_stock(self) -> bool:
        """是否为ST股票"""
        return self.status in [StockStatus.ST, StockStatus.STAR_ST]
    
    @property
    def is_suspended(self) -> bool:
        """是否停牌"""
        return self.status == StockStatus.SUSPENDED
    
    @property
    def market_cap_display(self) -> str:
        """市值显示"""
        if self.market_cap is None:
            return "未知"
        
        cap = float(self.market_cap)
        if cap >= 1e12:
            return f"{cap/1e12:.2f}万亿"
        elif cap >= 1e8:
            return f"{cap/1e8:.2f}亿"
        elif cap >= 1e4:
            return f"{cap/1e4:.2f}万"
        else:
            return f"{cap:.2f}"


class StockPrice(SimpleBase):
    """股票价格历史数据模型"""
    __tablename__ = "stock_prices"
    
    stock_id = Column(Integer, nullable=False, index=True, comment="股票ID")
    stock_code = Column(String(10), nullable=False, index=True, comment="股票代码")
    trade_date = Column(Date, nullable=False, index=True, comment="交易日期")
    
    # OHLCV数据
    open_price = Column(Numeric(10, 4), nullable=True, comment="开盘价")
    high_price = Column(Numeric(10, 4), nullable=True, comment="最高价")
    low_price = Column(Numeric(10, 4), nullable=True, comment="最低价")
    close_price = Column(Numeric(10, 4), nullable=True, comment="收盘价")
    volume = Column(Numeric(20, 0), nullable=True, comment="成交量")
    turnover = Column(Numeric(20, 2), nullable=True, comment="成交额")
    
    # 复权价格
    adj_close = Column(Numeric(10, 4), nullable=True, comment="后复权收盘价")
    adj_factor = Column(Numeric(10, 6), nullable=True, comment="复权因子")
    
    # 涨跌信息
    change_amount = Column(Numeric(10, 4), nullable=True, comment="涨跌额")
    change_percent = Column(Numeric(10, 4), nullable=True, comment="涨跌幅")
    
    # 交易信息
    turnover_rate = Column(Numeric(10, 4), nullable=True, comment="换手率")
    
    # 索引
    __table_args__ = (
        Index('idx_price_stock_date', 'stock_code', 'trade_date'),
        Index('idx_price_date_volume', 'trade_date', 'volume'),
        Index('idx_price_change_percent', 'change_percent'),
    )
    
    def __repr__(self):
        return f"<StockPrice(stock_code='{self.stock_code}', date='{self.trade_date}', close={self.close_price})>"


class StockIndicator(SimpleBase):
    """股票技术指标模型"""
    __tablename__ = "stock_indicators"
    
    stock_id = Column(Integer, nullable=False, index=True, comment="股票ID")
    stock_code = Column(String(10), nullable=False, index=True, comment="股票代码")
    trade_date = Column(Date, nullable=False, index=True, comment="交易日期")
    
    # 移动平均线
    ma5 = Column(Numeric(10, 4), nullable=True, comment="5日均线")
    ma10 = Column(Numeric(10, 4), nullable=True, comment="10日均线")
    ma20 = Column(Numeric(10, 4), nullable=True, comment="20日均线")
    ma30 = Column(Numeric(10, 4), nullable=True, comment="30日均线")
    ma60 = Column(Numeric(10, 4), nullable=True, comment="60日均线")
    ma120 = Column(Numeric(10, 4), nullable=True, comment="120日均线")
    ma250 = Column(Numeric(10, 4), nullable=True, comment="250日均线")
    
    # MACD指标
    macd_dif = Column(Numeric(10, 6), nullable=True, comment="MACD DIF")
    macd_dea = Column(Numeric(10, 6), nullable=True, comment="MACD DEA")
    macd_histogram = Column(Numeric(10, 6), nullable=True, comment="MACD柱状图")
    
    # KDJ指标
    kdj_k = Column(Numeric(10, 4), nullable=True, comment="KDJ K值")
    kdj_d = Column(Numeric(10, 4), nullable=True, comment="KDJ D值")
    kdj_j = Column(Numeric(10, 4), nullable=True, comment="KDJ J值")
    
    # RSI指标
    rsi6 = Column(Numeric(10, 4), nullable=True, comment="6日RSI")
    rsi12 = Column(Numeric(10, 4), nullable=True, comment="12日RSI")
    rsi24 = Column(Numeric(10, 4), nullable=True, comment="24日RSI")
    
    # 布林带
    boll_upper = Column(Numeric(10, 4), nullable=True, comment="布林带上轨")
    boll_mid = Column(Numeric(10, 4), nullable=True, comment="布林带中轨")
    boll_lower = Column(Numeric(10, 4), nullable=True, comment="布林带下轨")
    
    # 成交量指标
    vol_ma5 = Column(Numeric(20, 0), nullable=True, comment="5日成交量均线")
    vol_ma10 = Column(Numeric(20, 0), nullable=True, comment="10日成交量均线")
    
    # 索引
    __table_args__ = (
        Index('idx_indicator_stock_date', 'stock_code', 'trade_date'),
    )
    
    def __repr__(self):
        return f"<StockIndicator(stock_code='{self.stock_code}', date='{self.trade_date}')>"


class StockFinancial(SimpleBase):
    """股票财务数据模型"""
    __tablename__ = "stock_financials"
    
    stock_id = Column(Integer, nullable=False, index=True, comment="股票ID")
    stock_code = Column(String(10), nullable=False, index=True, comment="股票代码")
    report_date = Column(Date, nullable=False, index=True, comment="报告期")
    report_type = Column(String(20), nullable=False, comment="报告类型")  # Q1, Q2, Q3, annual
    
    # 基本每股收益
    eps = Column(Numeric(10, 4), nullable=True, comment="每股收益")
    bvps = Column(Numeric(10, 4), nullable=True, comment="每股净资产")
    
    # 盈利能力
    revenue = Column(Numeric(20, 2), nullable=True, comment="营业收入")
    net_profit = Column(Numeric(20, 2), nullable=True, comment="净利润")
    gross_profit_margin = Column(Numeric(10, 4), nullable=True, comment="毛利率")
    net_profit_margin = Column(Numeric(10, 4), nullable=True, comment="净利率")
    
    # 成长能力
    revenue_growth = Column(Numeric(10, 4), nullable=True, comment="营收增长率")
    profit_growth = Column(Numeric(10, 4), nullable=True, comment="净利润增长率")
    
    # 偿债能力
    current_ratio = Column(Numeric(10, 4), nullable=True, comment="流动比率")
    quick_ratio = Column(Numeric(10, 4), nullable=True, comment="速动比率")
    debt_to_equity = Column(Numeric(10, 4), nullable=True, comment="资产负债率")
    
    # 运营能力
    inventory_turnover = Column(Numeric(10, 4), nullable=True, comment="存货周转率")
    receivables_turnover = Column(Numeric(10, 4), nullable=True, comment="应收账款周转率")
    total_asset_turnover = Column(Numeric(10, 4), nullable=True, comment="总资产周转率")
    
    # 现金流
    operating_cash_flow = Column(Numeric(20, 2), nullable=True, comment="经营现金流")
    free_cash_flow = Column(Numeric(20, 2), nullable=True, comment="自由现金流")
    
    # 索引
    __table_args__ = (
        Index('idx_financial_stock_date', 'stock_code', 'report_date'),
        Index('idx_financial_type_date', 'report_type', 'report_date'),
    )
    
    def __repr__(self):
        return f"<StockFinancial(stock_code='{self.stock_code}', date='{self.report_date}', type='{self.report_type}')>"


class Industry(SimpleBase):
    """行业分类模型"""
    __tablename__ = "industries"
    
    code = Column(String(20), unique=True, nullable=False, comment="行业代码")
    name = Column(String(100), nullable=False, comment="行业名称")
    parent_code = Column(String(20), nullable=True, comment="父级行业代码")
    level = Column(Integer, default=1, comment="层级")
    description = Column(Text, nullable=True, comment="行业描述")
    
    # 统计信息
    stock_count = Column(Integer, default=0, comment="股票数量")
    total_market_cap = Column(Numeric(20, 2), nullable=True, comment="总市值")
    avg_pe_ratio = Column(Numeric(10, 4), nullable=True, comment="平均市盈率")
    
    def __repr__(self):
        return f"<Industry(code='{self.code}', name='{self.name}')>"
