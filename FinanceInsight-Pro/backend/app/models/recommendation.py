"""
推荐系统相关数据模型
"""

from datetime import datetime, timedelta
from typing import Optional, List
from sqlalchemy import Column, String, Integer, Text, DateTime, Numeric, JSON, Boolean, Index
from sqlalchemy.orm import relationship
import enum

from .base import SimpleBase


class RecommendationType(str, enum.Enum):
    """推荐类型枚举"""
    NEWS_BASED = "news_based"  # 基于新闻的推荐
    TREND_BASED = "trend_based"  # 基于趋势的推荐
    TECHNICAL_BASED = "technical_based"  # 基于技术分析的推荐
    FUNDAMENTAL_BASED = "fundamental_based"  # 基于基本面的推荐
    MIXED = "mixed"  # 混合推荐


class RecommendationAction(str, enum.Enum):
    """推荐操作枚举"""
    BUY = "buy"  # 买入
    SELL = "sell"  # 卖出
    HOLD = "hold"  # 持有
    WATCH = "watch"  # 观察


class RiskLevel(int, enum.Enum):
    """风险等级枚举"""
    VERY_LOW = 1  # 极低风险
    LOW = 2  # 低风险
    MEDIUM = 3  # 中等风险
    HIGH = 4  # 高风险
    VERY_HIGH = 5  # 极高风险


class TimeHorizon(str, enum.Enum):
    """投资期限枚举"""
    INTRADAY = "1d"  # 日内
    SHORT_TERM = "3d"  # 短期（3天）
    MEDIUM_TERM = "1w"  # 中期（1周）
    LONG_TERM = "1m"  # 长期（1个月）
    EXTENDED = "3m"  # 超长期（3个月）


class Recommendation(SimpleBase):
    """推荐记录模型"""
    __tablename__ = "recommendations"
    
    # 基本信息
    user_id = Column(Integer, nullable=True, index=True, comment="用户ID（空表示公共推荐）")
    stock_id = Column(Integer, nullable=False, index=True, comment="股票ID")
    stock_code = Column(String(10), nullable=False, index=True, comment="股票代码")
    stock_name = Column(String(100), nullable=False, comment="股票名称")
    
    # 推荐信息
    recommendation_type = Column(String(50), nullable=False, index=True, comment="推荐类型")
    action = Column(String(20), nullable=False, comment="推荐操作")
    score = Column(Numeric(5, 2), nullable=False, comment="推荐分数(0-10)")
    confidence = Column(Numeric(5, 4), nullable=False, comment="置信度(0-1)")
    
    # 风险评估
    risk_level = Column(Integer, nullable=False, comment="风险等级(1-5)")
    risk_score = Column(Numeric(5, 4), nullable=True, comment="风险分数(0-1)")
    risk_factors = Column(JSON, nullable=True, comment="风险因子")
    
    # 价格预测
    current_price = Column(Numeric(10, 4), nullable=True, comment="当前价格")
    target_price = Column(Numeric(10, 4), nullable=True, comment="目标价格")
    stop_loss = Column(Numeric(10, 4), nullable=True, comment="止损价格")
    expected_return = Column(Numeric(10, 4), nullable=True, comment="预期收益率")
    
    # 时间信息
    time_horizon = Column(String(10), nullable=False, comment="投资期限")
    valid_until = Column(DateTime, nullable=True, comment="有效期至")
    
    # 推荐理由
    reasons = Column(JSON, nullable=True, comment="推荐理由列表")
    related_news = Column(JSON, nullable=True, comment="相关新闻ID列表")
    technical_signals = Column(JSON, nullable=True, comment="技术信号")
    fundamental_factors = Column(JSON, nullable=True, comment="基本面因子")
    
    # 模型信息
    model_version = Column(String(20), nullable=True, comment="模型版本")
    algorithm = Column(String(50), nullable=True, comment="算法名称")
    features_used = Column(JSON, nullable=True, comment="使用的特征")
    
    # 状态信息
    is_active = Column(Boolean, default=True, comment="是否有效")
    is_public = Column(Boolean, default=True, comment="是否公开")
    is_featured = Column(Boolean, default=False, comment="是否精选")
    
    # 统计信息
    view_count = Column(Integer, default=0, comment="查看次数")
    like_count = Column(Integer, default=0, comment="点赞次数")
    follow_count = Column(Integer, default=0, comment="跟随次数")
    
    # 执行结果（用于回测）
    actual_return = Column(Numeric(10, 4), nullable=True, comment="实际收益率")
    max_drawdown = Column(Numeric(10, 4), nullable=True, comment="最大回撤")
    hit_target = Column(Boolean, nullable=True, comment="是否达到目标价")
    hit_stop_loss = Column(Boolean, nullable=True, comment="是否触及止损")
    
    # 索引
    __table_args__ = (
        Index('idx_rec_user_stock', 'user_id', 'stock_code'),
        Index('idx_rec_type_score', 'recommendation_type', 'score'),
        Index('idx_rec_risk_time', 'risk_level', 'time_horizon'),
        Index('idx_rec_active_public', 'is_active', 'is_public'),
        Index('idx_rec_valid_until', 'valid_until'),
        Index('idx_rec_created_score', 'created_at', 'score'),
    )
    
    def __repr__(self):
        return f"<Recommendation(id={self.id}, stock='{self.stock_code}', action='{self.action}', score={self.score})>"
    
    @property
    def is_expired(self) -> bool:
        """是否过期"""
        if self.valid_until is None:
            return False
        return datetime.utcnow() > self.valid_until
    
    @property
    def days_left(self) -> int:
        """剩余天数"""
        if self.valid_until is None:
            return -1
        delta = self.valid_until - datetime.utcnow()
        return max(0, delta.days)
    
    @property
    def risk_label(self) -> str:
        """风险等级标签"""
        labels = {1: "极低", 2: "低", 3: "中", 4: "高", 5: "极高"}
        return labels.get(self.risk_level, "未知")
    
    @property
    def action_label(self) -> str:
        """操作标签"""
        labels = {
            "buy": "买入",
            "sell": "卖出", 
            "hold": "持有",
            "watch": "观察"
        }
        return labels.get(self.action, "未知")
    
    @property
    def potential_return(self) -> Optional[float]:
        """潜在收益率"""
        if self.current_price is None or self.target_price is None:
            return None
        return float((self.target_price - self.current_price) / self.current_price * 100)
    
    def increment_view(self):
        """增加查看次数"""
        self.view_count += 1
    
    def increment_like(self):
        """增加点赞次数"""
        self.like_count += 1
    
    def increment_follow(self):
        """增加跟随次数"""
        self.follow_count += 1
    
    def set_featured(self, is_featured: bool = True):
        """设置精选状态"""
        self.is_featured = is_featured
    
    def deactivate(self):
        """停用推荐"""
        self.is_active = False
    
    def update_result(self, actual_return: float, max_drawdown: float = None, 
                     hit_target: bool = None, hit_stop_loss: bool = None):
        """更新执行结果"""
        self.actual_return = actual_return
        if max_drawdown is not None:
            self.max_drawdown = max_drawdown
        if hit_target is not None:
            self.hit_target = hit_target
        if hit_stop_loss is not None:
            self.hit_stop_loss = hit_stop_loss


class RecommendationFeedback(SimpleBase):
    """推荐反馈模型"""
    __tablename__ = "recommendation_feedback"
    
    recommendation_id = Column(Integer, nullable=False, index=True, comment="推荐ID")
    user_id = Column(Integer, nullable=False, index=True, comment="用户ID")
    
    # 反馈类型
    feedback_type = Column(String(20), nullable=False, comment="反馈类型")  # like, dislike, follow, report
    rating = Column(Integer, nullable=True, comment="评分(1-5)")
    comment = Column(Text, nullable=True, comment="评论")
    
    # 执行情况
    did_execute = Column(Boolean, nullable=True, comment="是否执行")
    execution_price = Column(Numeric(10, 4), nullable=True, comment="执行价格")
    execution_time = Column(DateTime, nullable=True, comment="执行时间")
    
    def __repr__(self):
        return f"<RecommendationFeedback(id={self.id}, rec_id={self.recommendation_id}, type='{self.feedback_type}')>"


class RecommendationPerformance(SimpleBase):
    """推荐表现统计模型"""
    __tablename__ = "recommendation_performance"
    
    # 时间维度
    date = Column(DateTime, nullable=False, index=True, comment="统计日期")
    period = Column(String(20), nullable=False, comment="统计周期")  # daily, weekly, monthly
    
    # 推荐维度
    recommendation_type = Column(String(50), nullable=True, comment="推荐类型")
    risk_level = Column(Integer, nullable=True, comment="风险等级")
    time_horizon = Column(String(10), nullable=True, comment="投资期限")
    
    # 数量统计
    total_recommendations = Column(Integer, default=0, comment="总推荐数")
    active_recommendations = Column(Integer, default=0, comment="活跃推荐数")
    expired_recommendations = Column(Integer, default=0, comment="过期推荐数")
    
    # 表现统计
    avg_score = Column(Numeric(5, 2), nullable=True, comment="平均分数")
    avg_confidence = Column(Numeric(5, 4), nullable=True, comment="平均置信度")
    avg_return = Column(Numeric(10, 4), nullable=True, comment="平均收益率")
    win_rate = Column(Numeric(5, 4), nullable=True, comment="胜率")
    
    # 风险统计
    avg_max_drawdown = Column(Numeric(10, 4), nullable=True, comment="平均最大回撤")
    sharpe_ratio = Column(Numeric(10, 4), nullable=True, comment="夏普比率")
    
    # 用户反馈
    total_views = Column(Integer, default=0, comment="总查看数")
    total_likes = Column(Integer, default=0, comment="总点赞数")
    total_follows = Column(Integer, default=0, comment="总跟随数")
    avg_rating = Column(Numeric(3, 2), nullable=True, comment="平均评分")
    
    # 索引
    __table_args__ = (
        Index('idx_perf_date_period', 'date', 'period'),
        Index('idx_perf_type_date', 'recommendation_type', 'date'),
        Index('idx_perf_risk_date', 'risk_level', 'date'),
    )
    
    def __repr__(self):
        return f"<RecommendationPerformance(date='{self.date}', period='{self.period}', type='{self.recommendation_type}')>"


class UserPreference(SimpleBase):
    """用户偏好模型"""
    __tablename__ = "user_preferences"
    
    user_id = Column(Integer, unique=True, nullable=False, index=True, comment="用户ID")
    
    # 风险偏好
    risk_tolerance = Column(Integer, default=3, comment="风险承受能力(1-5)")
    max_risk_level = Column(Integer, default=5, comment="最大风险等级")
    
    # 投资偏好
    preferred_time_horizon = Column(String(10), default="1w", comment="偏好投资期限")
    preferred_industries = Column(JSON, nullable=True, comment="偏好行业")
    excluded_industries = Column(JSON, nullable=True, comment="排除行业")
    preferred_stocks = Column(JSON, nullable=True, comment="偏好股票")
    excluded_stocks = Column(JSON, nullable=True, comment="排除股票")
    
    # 推荐偏好
    preferred_recommendation_types = Column(JSON, nullable=True, comment="偏好推荐类型")
    min_confidence = Column(Numeric(5, 4), default=0.6, comment="最小置信度")
    min_score = Column(Numeric(5, 2), default=6.0, comment="最小推荐分数")
    
    # 通知偏好
    notification_settings = Column(JSON, nullable=True, comment="通知设置")
    alert_thresholds = Column(JSON, nullable=True, comment="提醒阈值")
    
    # 显示偏好
    display_settings = Column(JSON, nullable=True, comment="显示设置")
    
    def __repr__(self):
        return f"<UserPreference(user_id={self.user_id}, risk_tolerance={self.risk_tolerance})>"
    
    def matches_recommendation(self, recommendation: Recommendation) -> bool:
        """检查推荐是否符合用户偏好"""
        # 检查风险等级
        if recommendation.risk_level > self.max_risk_level:
            return False
        
        # 检查置信度
        if recommendation.confidence < self.min_confidence:
            return False
        
        # 检查分数
        if recommendation.score < self.min_score:
            return False
        
        # 检查排除的股票
        if self.excluded_stocks and recommendation.stock_code in self.excluded_stocks:
            return False
        
        # 检查推荐类型
        if (self.preferred_recommendation_types and 
            recommendation.recommendation_type not in self.preferred_recommendation_types):
            return False
        
        return True


class RecommendationAlert(SimpleBase):
    """推荐提醒模型"""
    __tablename__ = "recommendation_alerts"
    
    user_id = Column(Integer, nullable=False, index=True, comment="用户ID")
    recommendation_id = Column(Integer, nullable=False, index=True, comment="推荐ID")
    
    # 提醒类型
    alert_type = Column(String(50), nullable=False, comment="提醒类型")
    trigger_condition = Column(JSON, nullable=True, comment="触发条件")
    
    # 提醒状态
    is_triggered = Column(Boolean, default=False, comment="是否已触发")
    triggered_at = Column(DateTime, nullable=True, comment="触发时间")
    is_sent = Column(Boolean, default=False, comment="是否已发送")
    sent_at = Column(DateTime, nullable=True, comment="发送时间")
    
    # 提醒内容
    title = Column(String(200), nullable=True, comment="提醒标题")
    message = Column(Text, nullable=True, comment="提醒内容")
    
    def __repr__(self):
        return f"<RecommendationAlert(id={self.id}, user_id={self.user_id}, type='{self.alert_type}')>"
