"""
新闻相关数据模型
"""

from datetime import datetime
from typing import Optional, List
from sqlalchemy import Column, String, Integer, Text, DateTime, Numeric, JSON, Boolean, Index
from sqlalchemy.dialects.mysql import LONGTEXT
from sqlalchemy.orm import relationship
import enum

from .base import SimpleBase


class NewsCategory(str, enum.Enum):
    """新闻分类枚举"""
    POLICY = "policy"  # 政策
    EARNINGS = "earnings"  # 业绩
    MERGER = "merger"  # 重组并购
    INTERNATIONAL = "international"  # 国际事件
    INDUSTRY = "industry"  # 行业动态
    COMPANY = "company"  # 公司新闻
    MARKET = "market"  # 市场动态
    REGULATION = "regulation"  # 监管政策
    ECONOMIC = "economic"  # 经济数据
    OTHER = "other"  # 其他


class NewsSentiment(str, enum.Enum):
    """新闻情感枚举"""
    POSITIVE = "positive"  # 正面
    NEGATIVE = "negative"  # 负面
    NEUTRAL = "neutral"  # 中性


class NewsImportance(int, enum.Enum):
    """新闻重要性枚举"""
    LOW = 1  # 低
    MEDIUM = 2  # 中
    HIGH = 3  # 高
    CRITICAL = 4  # 重要
    URGENT = 5  # 紧急


class News(SimpleBase):
    """新闻模型"""
    __tablename__ = "news"
    
    # 基本信息
    title = Column(String(500), nullable=False, index=True, comment="新闻标题")
    summary = Column(Text, nullable=True, comment="新闻摘要")
    content = Column(LONGTEXT, nullable=True, comment="新闻内容")
    url = Column(String(1000), unique=True, nullable=False, index=True, comment="新闻链接")
    source = Column(String(100), nullable=False, index=True, comment="新闻来源")
    author = Column(String(100), nullable=True, comment="作者")
    
    # 时间信息
    publish_time = Column(DateTime, nullable=False, index=True, comment="发布时间")
    crawl_time = Column(DateTime, nullable=False, index=True, comment="抓取时间")
    
    # 分类信息
    category = Column(String(50), nullable=True, index=True, comment="新闻分类")
    tags = Column(JSON, nullable=True, comment="标签列表")
    keywords = Column(JSON, nullable=True, comment="关键词列表")
    
    # 情感分析
    sentiment = Column(String(20), nullable=True, index=True, comment="情感倾向")
    sentiment_score = Column(Numeric(5, 4), nullable=True, comment="情感分数(-1到1)")
    sentiment_confidence = Column(Numeric(5, 4), nullable=True, comment="情感置信度(0到1)")
    
    # 重要性评分
    importance_score = Column(Numeric(5, 4), nullable=True, comment="重要性分数(0到1)")
    importance_level = Column(Integer, nullable=True, comment="重要性等级(1-5)")
    
    # 市场影响
    market_impact_score = Column(Numeric(5, 4), nullable=True, comment="市场影响分数(0到1)")
    affected_industries = Column(JSON, nullable=True, comment="影响的行业")
    affected_stocks = Column(JSON, nullable=True, comment="影响的股票")
    
    # 统计信息
    view_count = Column(Integer, default=0, nullable=False, comment="查看次数")
    share_count = Column(Integer, default=0, nullable=False, comment="分享次数")
    like_count = Column(Integer, default=0, nullable=False, comment="点赞次数")
    
    # 状态信息
    is_processed = Column(Boolean, default=False, nullable=False, comment="是否已处理")
    is_featured = Column(Boolean, default=False, nullable=False, comment="是否精选")
    is_hot = Column(Boolean, default=False, nullable=False, comment="是否热点")
    
    # 质量评分
    quality_score = Column(Numeric(5, 4), nullable=True, comment="内容质量分数(0到1)")
    credibility_score = Column(Numeric(5, 4), nullable=True, comment="可信度分数(0到1)")
    
    # 原始数据
    raw_data = Column(JSON, nullable=True, comment="原始抓取数据")
    
    # 索引
    __table_args__ = (
        Index('idx_news_publish_time', 'publish_time'),
        Index('idx_news_source_time', 'source', 'publish_time'),
        Index('idx_news_category_time', 'category', 'publish_time'),
        Index('idx_news_sentiment_time', 'sentiment', 'publish_time'),
        Index('idx_news_importance', 'importance_level', 'publish_time'),
        Index('idx_news_hot_featured', 'is_hot', 'is_featured', 'publish_time'),
    )
    
    def __repr__(self):
        return f"<News(id={self.id}, title='{self.title[:50]}...', source='{self.source}')>"
    
    @property
    def is_recent(self) -> bool:
        """是否是最近的新闻（24小时内）"""
        if not self.publish_time:
            return False
        return (datetime.utcnow() - self.publish_time).total_seconds() < 86400
    
    @property
    def age_hours(self) -> float:
        """新闻年龄（小时）"""
        if not self.publish_time:
            return float('inf')
        return (datetime.utcnow() - self.publish_time).total_seconds() / 3600
    
    @property
    def sentiment_label(self) -> str:
        """情感标签"""
        if self.sentiment_score is None:
            return "未知"
        if self.sentiment_score > 0.1:
            return "正面"
        elif self.sentiment_score < -0.1:
            return "负面"
        else:
            return "中性"
    
    @property
    def importance_label(self) -> str:
        """重要性标签"""
        if self.importance_level is None:
            return "未知"
        labels = {1: "低", 2: "中", 3: "高", 4: "重要", 5: "紧急"}
        return labels.get(self.importance_level, "未知")
    
    def increment_view(self):
        """增加查看次数"""
        self.view_count += 1
    
    def increment_share(self):
        """增加分享次数"""
        self.share_count += 1
    
    def increment_like(self):
        """增加点赞次数"""
        self.like_count += 1
    
    def set_hot(self, is_hot: bool = True):
        """设置热点状态"""
        self.is_hot = is_hot
    
    def set_featured(self, is_featured: bool = True):
        """设置精选状态"""
        self.is_featured = is_featured


class NewsEntity(SimpleBase):
    """新闻实体模型"""
    __tablename__ = "news_entities"
    
    news_id = Column(Integer, nullable=False, index=True, comment="新闻ID")
    entity_text = Column(String(200), nullable=False, comment="实体文本")
    entity_type = Column(String(50), nullable=False, comment="实体类型")
    confidence = Column(Numeric(5, 4), nullable=True, comment="置信度")
    start_pos = Column(Integer, nullable=True, comment="开始位置")
    end_pos = Column(Integer, nullable=True, comment="结束位置")
    
    # 索引
    __table_args__ = (
        Index('idx_entity_news_type', 'news_id', 'entity_type'),
        Index('idx_entity_text_type', 'entity_text', 'entity_type'),
    )
    
    def __repr__(self):
        return f"<NewsEntity(id={self.id}, text='{self.entity_text}', type='{self.entity_type}')>"


class NewsKeyword(SimpleBase):
    """新闻关键词模型"""
    __tablename__ = "news_keywords"
    
    news_id = Column(Integer, nullable=False, index=True, comment="新闻ID")
    keyword = Column(String(100), nullable=False, comment="关键词")
    weight = Column(Numeric(5, 4), nullable=True, comment="权重")
    tf_idf = Column(Numeric(10, 6), nullable=True, comment="TF-IDF值")
    
    # 索引
    __table_args__ = (
        Index('idx_keyword_news', 'news_id', 'keyword'),
        Index('idx_keyword_weight', 'keyword', 'weight'),
    )
    
    def __repr__(self):
        return f"<NewsKeyword(id={self.id}, keyword='{self.keyword}', weight={self.weight})>"


class NewsAnalysis(SimpleBase):
    """新闻分析结果模型"""
    __tablename__ = "news_analysis"
    
    news_id = Column(Integer, unique=True, nullable=False, index=True, comment="新闻ID")
    
    # 情感分析详细结果
    sentiment_details = Column(JSON, nullable=True, comment="情感分析详细结果")
    emotion_scores = Column(JSON, nullable=True, comment="情绪分数")
    
    # 实体识别结果
    entities = Column(JSON, nullable=True, comment="实体识别结果")
    relations = Column(JSON, nullable=True, comment="实体关系")
    
    # 关键词提取结果
    keywords = Column(JSON, nullable=True, comment="关键词提取结果")
    phrases = Column(JSON, nullable=True, comment="关键短语")
    
    # 分类结果
    categories = Column(JSON, nullable=True, comment="分类结果")
    topics = Column(JSON, nullable=True, comment="主题分析")
    
    # 市场影响分析
    market_impact = Column(JSON, nullable=True, comment="市场影响分析")
    industry_impact = Column(JSON, nullable=True, comment="行业影响分析")
    stock_correlation = Column(JSON, nullable=True, comment="股票关联分析")
    
    # 时效性分析
    urgency_score = Column(Numeric(5, 4), nullable=True, comment="紧急程度")
    time_sensitivity = Column(JSON, nullable=True, comment="时间敏感性分析")
    
    # 可信度分析
    credibility_factors = Column(JSON, nullable=True, comment="可信度因子")
    source_reliability = Column(Numeric(5, 4), nullable=True, comment="来源可靠性")
    
    # 处理信息
    analysis_version = Column(String(20), nullable=True, comment="分析版本")
    processing_time = Column(Numeric(10, 3), nullable=True, comment="处理时间(秒)")
    model_info = Column(JSON, nullable=True, comment="模型信息")
    
    def __repr__(self):
        return f"<NewsAnalysis(id={self.id}, news_id={self.news_id})>"


class NewsSource(SimpleBase):
    """新闻源模型"""
    __tablename__ = "news_sources"
    
    name = Column(String(100), unique=True, nullable=False, comment="新闻源名称")
    domain = Column(String(200), nullable=True, comment="域名")
    base_url = Column(String(500), nullable=True, comment="基础URL")
    rss_url = Column(String(500), nullable=True, comment="RSS链接")
    
    # 配置信息
    crawler_config = Column(JSON, nullable=True, comment="爬虫配置")
    update_frequency = Column(Integer, default=300, comment="更新频率(秒)")
    
    # 质量评估
    reliability_score = Column(Numeric(5, 4), nullable=True, comment="可靠性分数")
    quality_score = Column(Numeric(5, 4), nullable=True, comment="质量分数")
    
    # 统计信息
    total_news = Column(Integer, default=0, comment="总新闻数")
    last_crawl_time = Column(DateTime, nullable=True, comment="最后抓取时间")
    last_success_time = Column(DateTime, nullable=True, comment="最后成功时间")
    
    # 状态信息
    is_active = Column(Boolean, default=True, comment="是否启用")
    is_verified = Column(Boolean, default=False, comment="是否已验证")
    
    def __repr__(self):
        return f"<NewsSource(id={self.id}, name='{self.name}', domain='{self.domain}')>"
