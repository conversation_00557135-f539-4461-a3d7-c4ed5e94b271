"""
数据库模型基类
"""

from datetime import datetime
from typing import Any, Dict, Optional
from sqlalchemy import Column, DateTime, Integer, Bo<PERSON>an, Text
from sqlalchemy.ext.declarative import declarative_base, declared_attr
from sqlalchemy.orm import Session
from sqlalchemy.sql import func
import uuid


class CustomBase:
    """自定义基类，提供通用字段和方法"""
    
    # 主键ID
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    
    # 创建时间
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    
    # 更新时间
    updated_at = Column(
        DateTime, 
        default=func.now(), 
        onupdate=func.now(), 
        nullable=False,
        comment="更新时间"
    )
    
    # 是否删除（软删除）
    is_deleted = Column(Boolean, default=False, nullable=False, comment="是否删除")
    
    # 备注
    remark = Column(Text, nullable=True, comment="备注")
    
    @declared_attr
    def __tablename__(cls) -> str:
        """自动生成表名"""
        return cls.__name__.lower()
    
    def to_dict(self, exclude: Optional[list] = None) -> Dict[str, Any]:
        """转换为字典"""
        exclude = exclude or []
        result = {}
        for column in self.__table__.columns:
            if column.name not in exclude:
                value = getattr(self, column.name)
                if isinstance(value, datetime):
                    value = value.isoformat()
                result[column.name] = value
        return result
    
    def update_from_dict(self, data: Dict[str, Any], exclude: Optional[list] = None) -> None:
        """从字典更新属性"""
        exclude = exclude or ['id', 'created_at', 'updated_at']
        for key, value in data.items():
            if key not in exclude and hasattr(self, key):
                setattr(self, key, value)
    
    @classmethod
    def create(cls, db: Session, **kwargs) -> "CustomBase":
        """创建实例"""
        instance = cls(**kwargs)
        db.add(instance)
        db.commit()
        db.refresh(instance)
        return instance
    
    def update(self, db: Session, **kwargs) -> "CustomBase":
        """更新实例"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
        db.commit()
        db.refresh(self)
        return self
    
    def delete(self, db: Session, soft: bool = True) -> None:
        """删除实例"""
        if soft:
            self.is_deleted = True
            db.commit()
        else:
            db.delete(self)
            db.commit()
    
    @classmethod
    def get_by_id(cls, db: Session, id: int, include_deleted: bool = False):
        """根据ID获取实例"""
        query = db.query(cls).filter(cls.id == id)
        if not include_deleted:
            query = query.filter(cls.is_deleted == False)
        return query.first()
    
    @classmethod
    def get_all(cls, db: Session, include_deleted: bool = False, limit: int = 100, offset: int = 0):
        """获取所有实例"""
        query = db.query(cls)
        if not include_deleted:
            query = query.filter(cls.is_deleted == False)
        return query.offset(offset).limit(limit).all()
    
    @classmethod
    def count(cls, db: Session, include_deleted: bool = False) -> int:
        """统计数量"""
        query = db.query(cls)
        if not include_deleted:
            query = query.filter(cls.is_deleted == False)
        return query.count()
    
    def __repr__(self) -> str:
        return f"<{self.__class__.__name__}(id={self.id})>"


# 创建基类
Base = declarative_base(cls=CustomBase)


class TimestampMixin:
    """时间戳混入类"""
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)


class SoftDeleteMixin:
    """软删除混入类"""
    is_deleted = Column(Boolean, default=False, nullable=False)
    deleted_at = Column(DateTime, nullable=True)
    
    def soft_delete(self, db: Session):
        """软删除"""
        self.is_deleted = True
        self.deleted_at = func.now()
        db.commit()
    
    def restore(self, db: Session):
        """恢复删除"""
        self.is_deleted = False
        self.deleted_at = None
        db.commit()


class UUIDMixin:
    """UUID混入类"""
    uuid = Column(Text, default=lambda: str(uuid.uuid4()), unique=True, nullable=False)


class CreatorMixin:
    """创建者混入类"""
    created_by = Column(Integer, nullable=True, comment="创建者ID")
    updated_by = Column(Integer, nullable=True, comment="更新者ID")


class VersionMixin:
    """版本控制混入类"""
    version = Column(Integer, default=1, nullable=False, comment="版本号")
    
    def increment_version(self):
        """增加版本号"""
        self.version += 1


class StatusMixin:
    """状态混入类"""
    status = Column(Integer, default=1, nullable=False, comment="状态：1-正常，0-禁用")
    
    def enable(self, db: Session):
        """启用"""
        self.status = 1
        db.commit()
    
    def disable(self, db: Session):
        """禁用"""
        self.status = 0
        db.commit()
    
    @property
    def is_enabled(self) -> bool:
        """是否启用"""
        return self.status == 1
    
    @property
    def is_disabled(self) -> bool:
        """是否禁用"""
        return self.status == 0


class SortMixin:
    """排序混入类"""
    sort_order = Column(Integer, default=0, nullable=False, comment="排序顺序")


# 组合基类
class FullBase(Base, TimestampMixin, SoftDeleteMixin, UUIDMixin, CreatorMixin, VersionMixin, StatusMixin):
    """完整功能基类"""
    __abstract__ = True


class SimpleBase(Base, TimestampMixin, SoftDeleteMixin):
    """简单基类"""
    __abstract__ = True
