"""
新闻服务类
"""

from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_, or_, func, text
from sqlalchemy.exc import SQLAlchemyError

from app.models.news import News, NewsAnalysis, NewsEntity, NewsKeyword, NewsSource
from app.models.stock import Stock
from app.core.exceptions import DatabaseException, ValidationException
from app.utils.pagination import paginate
import logging

logger = logging.getLogger(__name__)


class NewsService:
    """新闻服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_news_list(
        self, 
        page: int = 1, 
        size: int = 20, 
        keyword: str = None,
        filters: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        获取新闻列表
        
        Args:
            page: 页码
            size: 每页数量
            keyword: 搜索关键词
            filters: 筛选条件
            
        Returns:
            包含新闻列表和分页信息的字典
        """
        try:
            # 构建基础查询
            query = self.db.query(News).filter(
                News.is_deleted == False
            )
            
            # 应用筛选条件
            if filters:
                if 'category' in filters:
                    query = query.filter(News.category == filters['category'])
                
                if 'sentiment' in filters:
                    query = query.filter(News.sentiment == filters['sentiment'])
                
                if 'source' in filters:
                    query = query.filter(News.source == filters['source'])
                
                if 'importance_level' in filters:
                    query = query.filter(News.importance_level == filters['importance_level'])
                
                if 'is_hot' in filters:
                    query = query.filter(News.is_hot == filters['is_hot'])
                
                if 'is_featured' in filters:
                    query = query.filter(News.is_featured == filters['is_featured'])
                
                if 'start_date' in filters:
                    query = query.filter(News.publish_time >= filters['start_date'])
                
                if 'end_date' in filters:
                    query = query.filter(News.publish_time <= filters['end_date'])
            
            # 关键词搜索
            if keyword:
                search_condition = or_(
                    News.title.contains(keyword),
                    News.summary.contains(keyword),
                    News.content.contains(keyword)
                )
                query = query.filter(search_condition)
            
            # 排序
            query = query.order_by(desc(News.publish_time))
            
            # 分页
            result = paginate(query, page, size)
            
            # 转换为响应格式
            items = []
            for news in result['items']:
                items.append(self._format_news_item(news))
            
            return {
                'items': items,
                'total': result['total'],
                'page': result['page'],
                'size': result['size'],
                'pages': result['pages']
            }
            
        except SQLAlchemyError as e:
            logger.error(f"获取新闻列表数据库错误: {str(e)}")
            raise DatabaseException("获取新闻列表失败")
        except Exception as e:
            logger.error(f"获取新闻列表未知错误: {str(e)}")
            raise
    
    def get_news_detail(self, news_id: int) -> Optional[Dict[str, Any]]:
        """
        获取新闻详情
        
        Args:
            news_id: 新闻ID
            
        Returns:
            新闻详情字典或None
        """
        try:
            # 获取新闻基本信息
            news = self.db.query(News).filter(
                News.id == news_id,
                News.is_deleted == False
            ).first()
            
            if not news:
                return None
            
            # 获取分析结果
            analysis = self.db.query(NewsAnalysis).filter(
                NewsAnalysis.news_id == news_id
            ).first()
            
            # 获取实体信息
            entities = self.db.query(NewsEntity).filter(
                NewsEntity.news_id == news_id
            ).all()
            
            # 获取关键词
            keywords = self.db.query(NewsKeyword).filter(
                NewsKeyword.news_id == news_id
            ).order_by(desc(NewsKeyword.weight)).all()
            
            # 获取相关股票
            related_stocks = []
            if news.affected_stocks:
                stock_codes = news.affected_stocks
                stocks = self.db.query(Stock).filter(
                    Stock.code.in_(stock_codes)
                ).all()
                
                for stock in stocks:
                    related_stocks.append({
                        'code': stock.code,
                        'name': stock.name,
                        'current_price': float(stock.current_price) if stock.current_price else None,
                        'change_percent': float(stock.change_percent) if stock.change_percent else None
                    })
            
            # 构建详情响应
            detail = self._format_news_detail(news, analysis, entities, keywords, related_stocks)
            
            return detail
            
        except SQLAlchemyError as e:
            logger.error(f"获取新闻详情数据库错误: {str(e)}")
            raise DatabaseException("获取新闻详情失败")
        except Exception as e:
            logger.error(f"获取新闻详情未知错误: {str(e)}")
            raise
    
    def get_hot_news(self, since_time: datetime, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取热点新闻
        
        Args:
            since_time: 起始时间
            limit: 返回数量限制
            
        Returns:
            热点新闻列表
        """
        try:
            # 查询热点新闻
            hot_news = self.db.query(News).filter(
                News.is_deleted == False,
                News.publish_time >= since_time,
                or_(
                    News.is_hot == True,
                    News.importance_level >= 4,
                    News.view_count >= 1000
                )
            ).order_by(
                desc(News.importance_score),
                desc(News.view_count),
                desc(News.publish_time)
            ).limit(limit).all()
            
            # 格式化结果
            result = []
            for news in hot_news:
                result.append(self._format_hot_news_item(news))
            
            return result
            
        except SQLAlchemyError as e:
            logger.error(f"获取热点新闻数据库错误: {str(e)}")
            raise DatabaseException("获取热点新闻失败")
        except Exception as e:
            logger.error(f"获取热点新闻未知错误: {str(e)}")
            raise
    
    def search_news(
        self,
        query: str,
        filters: Dict[str, Any] = None,
        page: int = 1,
        size: int = 20,
        sort_by: str = "publish_time",
        sort_order: str = "desc"
    ) -> Dict[str, Any]:
        """
        搜索新闻
        
        Args:
            query: 搜索查询
            filters: 筛选条件
            page: 页码
            size: 每页数量
            sort_by: 排序字段
            sort_order: 排序方向
            
        Returns:
            搜索结果
        """
        try:
            # 构建搜索查询
            search_query = self.db.query(News).filter(
                News.is_deleted == False
            )
            
            # 全文搜索
            if query:
                search_condition = or_(
                    News.title.contains(query),
                    News.summary.contains(query),
                    News.content.contains(query)
                )
                search_query = search_query.filter(search_condition)
            
            # 应用筛选条件
            if filters:
                # 这里可以添加更多筛选逻辑
                pass
            
            # 排序
            if sort_by == "relevance" and query:
                # 相关性排序（简单实现）
                search_query = search_query.order_by(
                    desc(News.importance_score),
                    desc(News.publish_time)
                )
            elif sort_by == "publish_time":
                if sort_order == "desc":
                    search_query = search_query.order_by(desc(News.publish_time))
                else:
                    search_query = search_query.order_by(News.publish_time)
            elif sort_by == "importance":
                if sort_order == "desc":
                    search_query = search_query.order_by(desc(News.importance_score))
                else:
                    search_query = search_query.order_by(News.importance_score)
            
            # 分页
            result = paginate(search_query, page, size)
            
            # 格式化结果
            items = []
            for news in result['items']:
                items.append(self._format_news_item(news))
            
            return {
                'items': items,
                'total': result['total'],
                'page': result['page'],
                'size': result['size'],
                'pages': result['pages'],
                'query': query
            }
            
        except SQLAlchemyError as e:
            logger.error(f"搜索新闻数据库错误: {str(e)}")
            raise DatabaseException("搜索新闻失败")
        except Exception as e:
            logger.error(f"搜索新闻未知错误: {str(e)}")
            raise
    
    def get_category_stats(self) -> List[Dict[str, Any]]:
        """
        获取新闻分类统计
        
        Returns:
            分类统计列表
        """
        try:
            # 统计各分类的新闻数量
            stats = self.db.query(
                News.category,
                func.count(News.id).label('count'),
                func.avg(News.sentiment_score).label('avg_sentiment'),
                func.avg(News.importance_score).label('avg_importance')
            ).filter(
                News.is_deleted == False,
                News.category.isnot(None)
            ).group_by(News.category).all()
            
            result = []
            for stat in stats:
                result.append({
                    'category': stat.category,
                    'count': stat.count,
                    'avg_sentiment': float(stat.avg_sentiment) if stat.avg_sentiment else None,
                    'avg_importance': float(stat.avg_importance) if stat.avg_importance else None
                })
            
            return result
            
        except SQLAlchemyError as e:
            logger.error(f"获取分类统计数据库错误: {str(e)}")
            raise DatabaseException("获取分类统计失败")
        except Exception as e:
            logger.error(f"获取分类统计未知错误: {str(e)}")
            raise
    
    def get_news_stats(self) -> Dict[str, Any]:
        """
        获取新闻统计概览
        
        Returns:
            统计概览字典
        """
        try:
            # 总新闻数
            total_news = self.db.query(func.count(News.id)).filter(
                News.is_deleted == False
            ).scalar()
            
            # 今日新闻数
            today = datetime.utcnow().date()
            today_news = self.db.query(func.count(News.id)).filter(
                News.is_deleted == False,
                func.date(News.publish_time) == today
            ).scalar()
            
            # 热点新闻数
            hot_news = self.db.query(func.count(News.id)).filter(
                News.is_deleted == False,
                News.is_hot == True
            ).scalar()
            
            # 精选新闻数
            featured_news = self.db.query(func.count(News.id)).filter(
                News.is_deleted == False,
                News.is_featured == True
            ).scalar()
            
            # 新闻源数量
            active_sources = self.db.query(func.count(NewsSource.id)).filter(
                NewsSource.is_deleted == False,
                NewsSource.is_active == True
            ).scalar()
            
            return {
                'total_news': total_news,
                'today_news': today_news,
                'hot_news': hot_news,
                'featured_news': featured_news,
                'active_sources': active_sources
            }
            
        except SQLAlchemyError as e:
            logger.error(f"获取新闻统计数据库错误: {str(e)}")
            raise DatabaseException("获取新闻统计失败")
        except Exception as e:
            logger.error(f"获取新闻统计未知错误: {str(e)}")
            raise
    
    def increment_view_count(self, news_id: int) -> None:
        """增加新闻查看次数"""
        try:
            self.db.query(News).filter(News.id == news_id).update({
                News.view_count: News.view_count + 1
            })
            self.db.commit()
        except SQLAlchemyError as e:
            logger.error(f"增加查看次数数据库错误: {str(e)}")
            self.db.rollback()
    
    def increment_like_count(self, news_id: int) -> None:
        """增加新闻点赞次数"""
        try:
            self.db.query(News).filter(News.id == news_id).update({
                News.like_count: News.like_count + 1
            })
            self.db.commit()
        except SQLAlchemyError as e:
            logger.error(f"增加点赞次数数据库错误: {str(e)}")
            self.db.rollback()
    
    def increment_share_count(self, news_id: int) -> None:
        """增加新闻分享次数"""
        try:
            self.db.query(News).filter(News.id == news_id).update({
                News.share_count: News.share_count + 1
            })
            self.db.commit()
        except SQLAlchemyError as e:
            logger.error(f"增加分享次数数据库错误: {str(e)}")
            self.db.rollback()
    
    def _format_news_item(self, news: News) -> Dict[str, Any]:
        """格式化新闻列表项"""
        return {
            'id': news.id,
            'title': news.title,
            'summary': news.summary,
            'source': news.source,
            'url': news.url,
            'publish_time': news.publish_time.isoformat() if news.publish_time else None,
            'category': news.category,
            'sentiment': news.sentiment,
            'sentiment_score': float(news.sentiment_score) if news.sentiment_score else None,
            'importance_level': news.importance_level,
            'importance_score': float(news.importance_score) if news.importance_score else None,
            'is_hot': news.is_hot,
            'is_featured': news.is_featured,
            'view_count': news.view_count,
            'like_count': news.like_count,
            'share_count': news.share_count,
            'tags': news.tags,
            'keywords': news.keywords
        }
    
    def _format_news_detail(
        self, 
        news: News, 
        analysis: NewsAnalysis = None,
        entities: List[NewsEntity] = None,
        keywords: List[NewsKeyword] = None,
        related_stocks: List[Dict] = None
    ) -> Dict[str, Any]:
        """格式化新闻详情"""
        detail = self._format_news_item(news)
        
        # 添加详细内容
        detail.update({
            'content': news.content,
            'author': news.author,
            'crawl_time': news.crawl_time.isoformat() if news.crawl_time else None,
            'market_impact_score': float(news.market_impact_score) if news.market_impact_score else None,
            'affected_industries': news.affected_industries,
            'affected_stocks': news.affected_stocks,
            'quality_score': float(news.quality_score) if news.quality_score else None,
            'credibility_score': float(news.credibility_score) if news.credibility_score else None
        })
        
        # 添加分析结果
        if analysis:
            detail['analysis'] = {
                'sentiment_details': analysis.sentiment_details,
                'emotion_scores': analysis.emotion_scores,
                'entities': analysis.entities,
                'relations': analysis.relations,
                'keywords': analysis.keywords,
                'phrases': analysis.phrases,
                'categories': analysis.categories,
                'topics': analysis.topics,
                'market_impact': analysis.market_impact,
                'industry_impact': analysis.industry_impact,
                'stock_correlation': analysis.stock_correlation,
                'urgency_score': float(analysis.urgency_score) if analysis.urgency_score else None,
                'time_sensitivity': analysis.time_sensitivity,
                'credibility_factors': analysis.credibility_factors,
                'source_reliability': float(analysis.source_reliability) if analysis.source_reliability else None
            }
        
        # 添加实体信息
        if entities:
            detail['entities'] = [
                {
                    'text': entity.entity_text,
                    'type': entity.entity_type,
                    'confidence': float(entity.confidence) if entity.confidence else None
                }
                for entity in entities
            ]
        
        # 添加关键词信息
        if keywords:
            detail['keyword_details'] = [
                {
                    'keyword': kw.keyword,
                    'weight': float(kw.weight) if kw.weight else None,
                    'tf_idf': float(kw.tf_idf) if kw.tf_idf else None
                }
                for kw in keywords
            ]
        
        # 添加相关股票
        if related_stocks:
            detail['related_stocks'] = related_stocks
        
        return detail
    
    def _format_hot_news_item(self, news: News) -> Dict[str, Any]:
        """格式化热点新闻项"""
        item = self._format_news_item(news)
        item['heat_score'] = self._calculate_heat_score(news)
        return item
    
    def _calculate_heat_score(self, news: News) -> float:
        """计算新闻热度分数"""
        # 简单的热度计算公式
        importance_weight = 0.4
        view_weight = 0.3
        time_weight = 0.2
        engagement_weight = 0.1
        
        importance_score = float(news.importance_score or 0)
        view_score = min(news.view_count / 10000, 1.0)  # 归一化到0-1
        
        # 时间衰减
        if news.publish_time:
            hours_ago = (datetime.utcnow() - news.publish_time).total_seconds() / 3600
            time_score = max(0, 1 - hours_ago / 24)  # 24小时内线性衰减
        else:
            time_score = 0
        
        engagement_score = min((news.like_count + news.share_count) / 1000, 1.0)
        
        heat_score = (
            importance_score * importance_weight +
            view_score * view_weight +
            time_score * time_weight +
            engagement_score * engagement_weight
        )
        
        return round(heat_score, 4)
