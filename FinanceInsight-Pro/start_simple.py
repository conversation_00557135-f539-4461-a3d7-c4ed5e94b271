#!/usr/bin/env python3
"""
FinanceInsight Pro 简化启动脚本
用于快速测试API功能，不依赖数据库
"""

import sys
import os
sys.path.append('backend')

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from datetime import datetime
import uvicorn

# 创建FastAPI应用
app = FastAPI(
    title="FinanceInsight Pro API",
    description="智能财经新闻分析与A股推荐系统",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 模拟数据
mock_news = [
    {
        "id": 1,
        "title": "央行降准释放流动性1.2万亿元",
        "summary": "央行宣布下调存款准备金率0.5个百分点，释放长期资金约1.2万亿元",
        "source": "新华社",
        "publish_time": "2024-01-15T10:30:00Z",
        "sentiment": "positive",
        "sentiment_score": 0.85,
        "importance_level": 5,
        "category": "policy",
        "view_count": 1250,
        "like_count": 89,
        "affected_stocks": ["000001", "600036", "000002"]
    },
    {
        "id": 2,
        "title": "科技股集体上涨，AI概念持续火热",
        "summary": "受人工智能技术发展推动，科技股板块今日集体上涨",
        "source": "财经网",
        "publish_time": "2024-01-15T14:20:00Z",
        "sentiment": "positive",
        "sentiment_score": 0.72,
        "importance_level": 4,
        "category": "market",
        "view_count": 890,
        "like_count": 56,
        "affected_stocks": ["000858", "600519"]
    }
]

mock_stocks = [
    {
        "id": 1,
        "code": "000001",
        "name": "平安银行",
        "market": "sz",
        "industry": "银行",
        "current_price": 14.25,
        "prev_close": 13.95,
        "change_amount": 0.30,
        "change_percent": 2.15,
        "volume": 45678900,
        "market_cap": 276234384325,
        "pe_ratio": 4.89,
        "pb_ratio": 0.67
    },
    {
        "id": 2,
        "code": "600036",
        "name": "招商银行",
        "market": "sh",
        "industry": "银行",
        "current_price": 42.80,
        "prev_close": 42.10,
        "change_amount": 0.70,
        "change_percent": 1.66,
        "volume": 23456789,
        "market_cap": 1234567890123,
        "pe_ratio": 6.2,
        "pb_ratio": 1.1
    }
]

mock_recommendations = [
    {
        "id": 1,
        "stock_code": "000001",
        "stock_name": "平安银行",
        "action": "buy",
        "score": 8.5,
        "confidence": 0.87,
        "risk_level": 3,
        "current_price": 14.25,
        "target_price": 16.50,
        "expected_return": 15.79,
        "time_horizon": "1m",
        "reasons": [
            {"type": "news", "description": "央行降准政策利好银行股"},
            {"type": "technical", "description": "突破重要阻力位"}
        ]
    },
    {
        "id": 2,
        "stock_code": "600036",
        "stock_name": "招商银行",
        "action": "hold",
        "score": 7.2,
        "confidence": 0.78,
        "risk_level": 2,
        "current_price": 42.80,
        "target_price": 45.00,
        "expected_return": 5.14,
        "time_horizon": "3m",
        "reasons": [
            {"type": "fundamental", "description": "基本面稳健"},
            {"type": "valuation", "description": "估值合理"}
        ]
    }
]

# 根路径
@app.get("/")
async def root():
    return {
        "message": "欢迎使用 FinanceInsight Pro API",
        "version": "1.0.0",
        "docs": "/docs",
        "status": "running",
        "timestamp": datetime.now().isoformat()
    }

# 健康检查
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "service": "FinanceInsight Pro",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat()
    }

# 新闻API
@app.get("/api/v1/news")
async def get_news(page: int = 1, size: int = 20, category: str = None):
    filtered_news = mock_news
    if category:
        filtered_news = [n for n in mock_news if n.get("category") == category]
    
    return {
        "items": filtered_news,
        "total": len(filtered_news),
        "page": page,
        "size": size
    }

@app.get("/api/v1/news/{news_id}")
async def get_news_detail(news_id: int):
    news = next((n for n in mock_news if n["id"] == news_id), None)
    if not news:
        raise HTTPException(status_code=404, detail="新闻不存在")
    
    # 增加详细信息
    news_detail = news.copy()
    news_detail.update({
        "content": "这是新闻的详细内容...",
        "author": "财经记者",
        "tags": ["央行", "货币政策", "银行"],
        "related_stocks": [
            {"code": "000001", "name": "平安银行", "correlation": 0.89},
            {"code": "600036", "name": "招商银行", "correlation": 0.85}
        ]
    })
    
    return news_detail

@app.get("/api/v1/news/hot")
async def get_hot_news(limit: int = 10):
    # 按重要性和查看数排序
    sorted_news = sorted(mock_news, key=lambda x: (x["importance_level"], x["view_count"]), reverse=True)
    return sorted_news[:limit]

# 股票API
@app.get("/api/v1/stocks")
async def get_stocks(page: int = 1, size: int = 20, market: str = None):
    filtered_stocks = mock_stocks
    if market:
        filtered_stocks = [s for s in mock_stocks if s.get("market") == market]
    
    return {
        "items": filtered_stocks,
        "total": len(filtered_stocks),
        "page": page,
        "size": size
    }

@app.get("/api/v1/stocks/{stock_code}")
async def get_stock_detail(stock_code: str):
    stock = next((s for s in mock_stocks if s["code"] == stock_code), None)
    if not stock:
        raise HTTPException(status_code=404, detail="股票不存在")
    
    # 增加详细信息
    stock_detail = stock.copy()
    stock_detail.update({
        "full_name": f"{stock['name']}股份有限公司",
        "listing_date": "1991-04-03",
        "total_shares": 19405918198,
        "float_shares": 19405918198,
        "day_high": stock["current_price"] * 1.02,
        "day_low": stock["current_price"] * 0.98,
        "week_52_high": stock["current_price"] * 1.25,
        "week_52_low": stock["current_price"] * 0.75
    })
    
    return stock_detail

# 推荐API
@app.get("/api/v1/recommendations")
async def get_recommendations(page: int = 1, size: int = 20, risk_level: int = None):
    filtered_recs = mock_recommendations
    if risk_level:
        filtered_recs = [r for r in mock_recommendations if r.get("risk_level") == risk_level]
    
    return {
        "items": filtered_recs,
        "total": len(filtered_recs),
        "page": page,
        "size": size
    }

@app.get("/api/v1/recommendations/{rec_id}")
async def get_recommendation_detail(rec_id: int):
    rec = next((r for r in mock_recommendations if r["id"] == rec_id), None)
    if not rec:
        raise HTTPException(status_code=404, detail="推荐不存在")
    
    return rec

# 分析API
@app.get("/api/v1/analysis/sentiment")
async def get_sentiment_analysis():
    return {
        "overall_sentiment": 0.65,
        "positive_ratio": 0.45,
        "negative_ratio": 0.25,
        "neutral_ratio": 0.30,
        "trend": [
            {"date": "2024-01-15", "sentiment": 0.65},
            {"date": "2024-01-14", "sentiment": 0.58},
            {"date": "2024-01-13", "sentiment": 0.72}
        ]
    }

@app.get("/api/v1/analysis/market")
async def get_market_analysis():
    return {
        "market_status": "bullish",
        "confidence": 0.78,
        "key_factors": [
            "央行政策支持",
            "经济数据向好",
            "市场流动性充裕"
        ],
        "risk_factors": [
            "国际市场波动",
            "政策不确定性"
        ]
    }

# 统计API
@app.get("/api/v1/stats/overview")
async def get_stats_overview():
    return {
        "total_news": len(mock_news),
        "total_stocks": len(mock_stocks),
        "total_recommendations": len(mock_recommendations),
        "active_users": 1250,
        "api_calls_today": 15678,
        "system_uptime": "99.9%"
    }

def main():
    """主函数"""
    print("🚀 启动 FinanceInsight Pro 简化API服务器")
    print("=" * 50)
    print("📋 功能特性:")
    print("  ✅ 新闻数据API")
    print("  ✅ 股票数据API") 
    print("  ✅ 推荐系统API")
    print("  ✅ 数据分析API")
    print("  ✅ 统计信息API")
    print()
    print("🌐 访问地址:")
    print("  - API根路径: http://localhost:8000")
    print("  - API文档: http://localhost:8000/docs")
    print("  - 健康检查: http://localhost:8000/health")
    print()
    print("📚 示例API:")
    print("  - 获取新闻: http://localhost:8000/api/v1/news")
    print("  - 获取股票: http://localhost:8000/api/v1/stocks")
    print("  - 获取推荐: http://localhost:8000/api/v1/recommendations")
    print()
    print("⚡ 按 Ctrl+C 停止服务器")
    print("=" * 50)
    
    try:
        uvicorn.run(
            app, 
            host="0.0.0.0", 
            port=8000, 
            log_level="info",
            reload=False
        )
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
        print("👋 感谢使用 FinanceInsight Pro!")

if __name__ == "__main__":
    main()
