# FinanceInsight Pro - API设计文档

## API概述

### 基础信息
- **Base URL**: `https://api.financeinsight.com/v1`
- **协议**: HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: JWT Bearer Token

### 通用响应格式
```json
{
    "code": 200,
    "message": "success",
    "data": {},
    "timestamp": "2024-01-01T00:00:00Z",
    "request_id": "uuid-string"
}
```

### 状态码说明
- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 禁止访问
- `404`: 资源不存在
- `429`: 请求频率超限
- `500`: 服务器内部错误

## 认证授权

### 1. 用户注册
```http
POST /auth/register
Content-Type: application/json

{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "password123",
    "phone": "13800138000"
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "注册成功",
    "data": {
        "user_id": 12345,
        "username": "testuser",
        "email": "<EMAIL>",
        "subscription_type": "free"
    }
}
```

### 2. 用户登录
```http
POST /auth/login
Content-Type: application/json

{
    "username": "testuser",
    "password": "password123"
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "登录成功",
    "data": {
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "expires_in": 3600,
        "user_info": {
            "user_id": 12345,
            "username": "testuser",
            "subscription_type": "pro"
        }
    }
}
```

### 3. Token刷新
```http
POST /auth/refresh
Content-Type: application/json

{
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

## 新闻相关API

### 1. 获取新闻列表
```http
GET /news?page=1&size=20&category=政策&sentiment=positive&start_date=2024-01-01&end_date=2024-01-31
Authorization: Bearer {access_token}
```

**查询参数**:
- `page`: 页码，默认1
- `size`: 每页数量，默认20，最大100
- `category`: 新闻分类（政策、业绩、重组、国际等）
- `sentiment`: 情感倾向（positive、negative、neutral）
- `start_date`: 开始日期
- `end_date`: 结束日期
- `source`: 新闻来源
- `keyword`: 关键词搜索

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "total": 1000,
        "page": 1,
        "size": 20,
        "items": [
            {
                "id": "news_001",
                "title": "央行降准释放流动性1.2万亿元",
                "summary": "央行宣布下调存款准备金率0.5个百分点...",
                "source": "新华社",
                "url": "https://example.com/news/001",
                "publish_time": "2024-01-15T10:30:00Z",
                "sentiment_score": 0.85,
                "importance_score": 0.92,
                "categories": ["政策", "货币政策"],
                "related_stocks": ["000001", "600036", "000002"]
            }
        ]
    }
}
```

### 2. 获取新闻详情
```http
GET /news/{news_id}
Authorization: Bearer {access_token}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "id": "news_001",
        "title": "央行降准释放流动性1.2万亿元",
        "content": "完整新闻内容...",
        "source": "新华社",
        "author": "记者姓名",
        "url": "https://example.com/news/001",
        "publish_time": "2024-01-15T10:30:00Z",
        "sentiment_analysis": {
            "score": 0.85,
            "polarity": "positive",
            "confidence": 0.92,
            "keywords": ["降准", "流动性", "货币政策"]
        },
        "entities": [
            {
                "text": "中国人民银行",
                "type": "ORG",
                "confidence": 0.98
            }
        ],
        "market_impact": {
            "overall_score": 0.88,
            "affected_industries": {
                "银行": 0.95,
                "地产": 0.78,
                "基建": 0.82
            }
        },
        "related_stocks": [
            {
                "code": "000001",
                "name": "平安银行",
                "correlation": 0.89,
                "impact_type": "positive"
            }
        ]
    }
}
```

### 3. 获取热点新闻
```http
GET /news/hot?period=1d&limit=10
Authorization: Bearer {access_token}
```

**查询参数**:
- `period`: 时间周期（1h、6h、1d、3d、1w）
- `limit`: 返回数量，默认10

## 股票相关API

### 1. 获取股票列表
```http
GET /stocks?market=all&industry=银行&page=1&size=50
Authorization: Bearer {access_token}
```

**查询参数**:
- `market`: 市场（sh、sz、all）
- `industry`: 行业分类
- `page`: 页码
- `size`: 每页数量

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "total": 500,
        "items": [
            {
                "code": "000001",
                "name": "平安银行",
                "industry": "银行",
                "market": "sz",
                "listing_date": "1991-04-03",
                "market_cap": 280000000000,
                "current_price": 14.25,
                "change_percent": 2.15
            }
        ]
    }
}
```

### 2. 获取股票详情
```http
GET /stocks/{stock_code}
Authorization: Bearer {access_token}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "code": "000001",
        "name": "平安银行",
        "industry": "银行",
        "market": "sz",
        "listing_date": "1991-04-03",
        "basic_info": {
            "total_shares": 19405918198,
            "float_shares": 19405918198,
            "market_cap": 276234384325,
            "pe_ratio": 4.89,
            "pb_ratio": 0.67,
            "roe": 13.68
        },
        "price_info": {
            "current": 14.25,
            "open": 14.10,
            "high": 14.35,
            "low": 14.05,
            "close_prev": 13.95,
            "change": 0.30,
            "change_percent": 2.15,
            "volume": 45678900,
            "turnover": 651234567.89
        },
        "recent_news_count": 15,
        "sentiment_trend": "positive"
    }
}
```

### 3. 获取股票实时价格
```http
GET /stocks/{stock_code}/price
Authorization: Bearer {access_token}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "code": "000001",
        "name": "平安银行",
        "price": 14.25,
        "change": 0.30,
        "change_percent": 2.15,
        "volume": 45678900,
        "turnover": 651234567.89,
        "timestamp": "2024-01-15T14:30:00Z",
        "market_status": "trading"
    }
}
```

## 推荐相关API

### 1. 获取股票推荐
```http
GET /recommendations?type=news_based&risk_level=3&limit=20
Authorization: Bearer {access_token}
```

**查询参数**:
- `type`: 推荐类型（news_based、trend_based、mixed）
- `risk_level`: 风险等级（1-5）
- `limit`: 返回数量
- `time_horizon`: 投资期限（1d、3d、1w、1m）

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "recommendations": [
            {
                "id": "rec_001",
                "stock_code": "000001",
                "stock_name": "平安银行",
                "score": 8.5,
                "recommendation": "BUY",
                "risk_level": 3,
                "confidence": 0.87,
                "target_price": 16.50,
                "stop_loss": 13.20,
                "time_horizon": "1m",
                "reasons": [
                    {
                        "type": "news",
                        "description": "央行降准政策利好银行股",
                        "impact_score": 0.85,
                        "news_id": "news_001"
                    },
                    {
                        "type": "technical",
                        "description": "突破重要阻力位",
                        "impact_score": 0.72
                    }
                ],
                "related_news": ["news_001", "news_002"],
                "created_at": "2024-01-15T15:00:00Z"
            }
        ],
        "market_overview": {
            "overall_sentiment": "positive",
            "hot_industries": ["银行", "地产", "基建"],
            "risk_warning": "注意政策变化风险"
        }
    }
}
```

### 2. 刷新推荐
```http
POST /recommendations/refresh
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "user_preferences": {
        "risk_tolerance": 3,
        "investment_horizon": "1m",
        "preferred_industries": ["银行", "科技"],
        "excluded_stocks": ["000002"]
    }
}
```

### 3. 获取推荐详情
```http
GET /recommendations/{recommendation_id}
Authorization: Bearer {access_token}
```

## 分析相关API

### 1. 情感分析结果
```http
GET /analysis/sentiment?period=1w&granularity=daily
Authorization: Bearer {access_token}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "period": "1w",
        "sentiment_trend": [
            {
                "date": "2024-01-15",
                "overall_sentiment": 0.65,
                "positive_ratio": 0.45,
                "negative_ratio": 0.25,
                "neutral_ratio": 0.30,
                "news_count": 156
            }
        ],
        "industry_sentiment": {
            "银行": 0.78,
            "地产": 0.45,
            "科技": 0.62
        },
        "hot_keywords": [
            {"word": "降准", "sentiment": 0.85, "frequency": 45},
            {"word": "流动性", "sentiment": 0.72, "frequency": 38}
        ]
    }
}
```

### 2. 关联分析结果
```http
GET /analysis/correlation?stock_code=000001&news_id=news_001
Authorization: Bearer {access_token}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "stock_code": "000001",
        "news_id": "news_001",
        "correlation_score": 0.89,
        "correlation_factors": {
            "direct_mention": 0.95,
            "industry_relevance": 0.85,
            "supply_chain": 0.60,
            "market_sentiment": 0.78
        },
        "impact_prediction": {
            "direction": "positive",
            "magnitude": 0.82,
            "confidence": 0.87,
            "time_decay": {
                "1d": 0.90,
                "3d": 0.75,
                "1w": 0.60,
                "1m": 0.40
            }
        }
    }
}
```

### 3. 趋势分析
```http
GET /analysis/trends?type=market&period=1m
Authorization: Bearer {access_token}
```

## 用户相关API

### 1. 获取用户信息
```http
GET /user/profile
Authorization: Bearer {access_token}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "user_id": 12345,
        "username": "testuser",
        "email": "<EMAIL>",
        "phone": "13800138000",
        "subscription_type": "pro",
        "subscription_expires": "2024-12-31T23:59:59Z",
        "preferences": {
            "risk_tolerance": 3,
            "investment_horizon": "1m",
            "preferred_industries": ["银行", "科技"],
            "notification_settings": {
                "email": true,
                "sms": false,
                "push": true
            }
        },
        "usage_stats": {
            "api_calls_today": 156,
            "api_limit_daily": 1000,
            "recommendations_viewed": 45
        }
    }
}
```

### 2. 更新用户信息
```http
PUT /user/profile
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "preferences": {
        "risk_tolerance": 4,
        "preferred_industries": ["银行", "科技", "医药"]
    }
}
```

## 错误处理

### 错误响应格式
```json
{
    "code": 400,
    "message": "请求参数错误",
    "error": {
        "type": "ValidationError",
        "details": [
            {
                "field": "stock_code",
                "message": "股票代码格式不正确"
            }
        ]
    },
    "timestamp": "2024-01-15T15:30:00Z",
    "request_id": "uuid-string"
}
```

### 常见错误码
- `40001`: 参数缺失
- `40002`: 参数格式错误
- `40003`: 参数值超出范围
- `40101`: Token无效
- `40102`: Token过期
- `40301`: 权限不足
- `40401`: 资源不存在
- `42901`: 请求频率超限
- `50001`: 数据库错误
- `50002`: 外部API调用失败
- `50003`: 服务暂时不可用

## API限流规则

### 限流策略
- **免费用户**: 100次/小时，1000次/天
- **专业用户**: 500次/小时，10000次/天
- **企业用户**: 2000次/小时，50000次/天

### 限流响应头
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1642248000
```

## SDK和示例代码

### Python SDK示例
```python
from financeinsight import FinanceInsightClient

client = FinanceInsightClient(api_key="your_api_key")

# 获取推荐
recommendations = client.get_recommendations(
    type="news_based",
    risk_level=3,
    limit=10
)

# 获取新闻
news = client.get_news(
    category="政策",
    sentiment="positive",
    page=1,
    size=20
)
```

### JavaScript SDK示例
```javascript
import { FinanceInsightClient } from 'financeinsight-js';

const client = new FinanceInsightClient({
    apiKey: 'your_api_key',
    baseURL: 'https://api.financeinsight.com/v1'
});

// 获取股票信息
const stock = await client.stocks.get('000001');

// 获取推荐
const recommendations = await client.recommendations.list({
    type: 'news_based',
    riskLevel: 3
});
```
