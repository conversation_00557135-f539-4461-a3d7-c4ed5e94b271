# FinanceInsight Pro 部署指南

## 系统要求

### 硬件要求
- **CPU**: 4核心以上
- **内存**: 8GB以上（推荐16GB）
- **存储**: 100GB以上可用空间
- **网络**: 稳定的互联网连接

### 软件要求
- **操作系统**: Linux (Ubuntu 20.04+, CentOS 8+) 或 macOS
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **Git**: 2.0+

## 快速部署

### 1. 克隆项目
```bash
git clone https://github.com/financeinsight/financeinsight-pro.git
cd financeinsight-pro
```

### 2. 配置环境变量
```bash
# 复制环境变量模板
cp backend/.env.example backend/.env

# 编辑环境变量文件
vim backend/.env
```

**重要配置项：**
```bash
# 应用配置
SECRET_KEY=your-super-secret-key-change-this-in-production
ENVIRONMENT=production
DEBUG=False

# 数据库配置
DATABASE_URL=mysql+pymysql://financeinsight:financeinsight123@mysql:3306/financeinsight
REDIS_URL=redis://redis:6379/0

# 外部API密钥（需要申请）
EASTMONEY_API_KEY=your_eastmoney_api_key
TONGHUASHUN_API_KEY=your_tonghuashun_api_key
XINHUA_API_KEY=your_xinhua_api_key
```

### 3. 一键启动
```bash
# 给脚本执行权限
chmod +x scripts/start.sh scripts/stop.sh

# 启动系统
./scripts/start.sh
```

### 4. 访问系统
- **前端应用**: http://localhost
- **API文档**: http://localhost/api/docs
- **管理后台**: http://localhost/admin
- **监控面板**: http://localhost:3001

**默认账号：**
- 管理员: `admin` / `admin123`
- 测试用户: `testuser` / `admin123`

## 详细部署步骤

### 1. 环境准备

#### 安装Docker
```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# CentOS/RHEL
sudo yum install -y docker
sudo systemctl start docker
sudo systemctl enable docker
```

#### 安装Docker Compose
```bash
sudo curl -L "https://github.com/docker/compose/releases/download/v2.12.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. 项目配置

#### 目录结构
```
FinanceInsight-Pro/
├── backend/                 # 后端服务
├── frontend/               # 前端应用
├── deployment/             # 部署配置
├── scripts/               # 部署脚本
├── docs/                  # 文档
├── docker-compose.yml     # Docker编排文件
└── README.md
```

#### 环境变量配置
编辑 `backend/.env` 文件，配置以下关键参数：

```bash
# 数据库配置
DATABASE_URL=mysql+pymysql://financeinsight:financeinsight123@mysql:3306/financeinsight
REDIS_URL=redis://redis:6379/0
MONGODB_URL=***********************************************************************
ELASTICSEARCH_URL=http://elasticsearch:9200

# 安全配置
SECRET_KEY=生成一个强密码
ENCRYPTION_KEY=32位加密密钥

# API限流
RATE_LIMIT_ENABLED=True
RATE_LIMIT_FREE_USER=100/hour
RATE_LIMIT_PRO_USER=500/hour

# 外部API配置
EASTMONEY_API_KEY=东方财富API密钥
TONGHUASHUN_API_KEY=同花顺API密钥
SINA_FINANCE_BASE_URL=https://hq.sinajs.cn

# 邮件服务
MAIL_SERVER=smtp.example.com
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_email_password

# 监控配置
SENTRY_DSN=https://<EMAIL>/project_id
```

### 3. 服务启动

#### 分步启动
```bash
# 1. 启动基础服务
docker-compose up -d mysql redis elasticsearch mongodb rabbitmq

# 2. 等待基础服务就绪
sleep 30

# 3. 启动应用服务
docker-compose up -d backend celery-worker celery-beat

# 4. 启动前端服务
docker-compose up -d frontend nginx

# 5. 启动监控服务
docker-compose up -d prometheus grafana
```

#### 检查服务状态
```bash
# 查看所有服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f backend
docker-compose logs -f frontend

# 检查服务健康状态
curl http://localhost/health
curl http://localhost/api/health
```

### 4. 数据初始化

#### 数据库初始化
数据库会在首次启动时自动初始化，包括：
- 创建数据库表结构
- 插入默认管理员账号
- 插入示例股票数据

#### 手动初始化（如需要）
```bash
# 进入后端容器
docker-compose exec backend bash

# 运行数据库迁移
alembic upgrade head

# 创建管理员用户
python scripts/create_admin.py
```

## 生产环境配置

### 1. 安全配置

#### SSL证书配置
```bash
# 将SSL证书放置到指定目录
mkdir -p deployment/nginx/ssl
cp your_domain.crt deployment/nginx/ssl/
cp your_domain.key deployment/nginx/ssl/

# 修改nginx配置启用HTTPS
vim deployment/nginx/conf.d/default.conf
```

#### 防火墙配置
```bash
# 开放必要端口
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

### 2. 性能优化

#### 数据库优化
```sql
-- MySQL配置优化
SET GLOBAL innodb_buffer_pool_size = 2G;
SET GLOBAL max_connections = 1000;
SET GLOBAL query_cache_size = 256M;
```

#### Redis优化
```bash
# Redis配置
maxmemory 2gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
```

#### 应用优化
```bash
# 增加worker进程数
WORKERS=8

# 启用缓存
CACHE_ENABLED=True
CACHE_TTL=3600

# 启用压缩
GZIP_ENABLED=True
```

### 3. 监控配置

#### Prometheus配置
编辑 `deployment/prometheus/prometheus.yml`：
```yaml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'financeinsight-backend'
    static_configs:
      - targets: ['backend:8000']
  
  - job_name: 'financeinsight-mysql'
    static_configs:
      - targets: ['mysql:3306']
```

#### Grafana仪表板
- 导入预配置的仪表板
- 配置告警规则
- 设置通知渠道

## 维护操作

### 1. 日常维护

#### 查看日志
```bash
# 查看应用日志
docker-compose logs -f --tail=100 backend

# 查看错误日志
docker-compose logs -f backend | grep ERROR

# 查看访问日志
docker-compose logs -f nginx
```

#### 备份数据
```bash
# 数据库备份
docker-compose exec mysql mysqldump -u root -pfinanceinsight123 financeinsight > backup_$(date +%Y%m%d).sql

# Redis备份
docker-compose exec redis redis-cli BGSAVE
docker cp $(docker-compose ps -q redis):/data/dump.rdb redis_backup_$(date +%Y%m%d).rdb
```

#### 更新应用
```bash
# 拉取最新代码
git pull origin main

# 重新构建镜像
docker-compose build

# 重启服务
docker-compose up -d
```

### 2. 故障排除

#### 常见问题

**服务无法启动**
```bash
# 检查端口占用
netstat -tlnp | grep :8000

# 检查磁盘空间
df -h

# 检查内存使用
free -h
```

**数据库连接失败**
```bash
# 检查数据库状态
docker-compose exec mysql mysql -u root -pfinanceinsight123 -e "SELECT 1"

# 重启数据库
docker-compose restart mysql
```

**API响应慢**
```bash
# 检查系统负载
top
htop

# 检查数据库性能
docker-compose exec mysql mysql -u root -pfinanceinsight123 -e "SHOW PROCESSLIST"
```

### 3. 扩容部署

#### 水平扩容
```bash
# 增加后端实例
docker-compose up -d --scale backend=3

# 增加worker实例
docker-compose up -d --scale celery-worker=4
```

#### 负载均衡
使用Nginx配置负载均衡：
```nginx
upstream backend {
    server backend_1:8000;
    server backend_2:8000;
    server backend_3:8000;
}
```

## 安全建议

1. **定期更新系统和依赖**
2. **使用强密码和密钥**
3. **启用HTTPS和安全头**
4. **配置防火墙规则**
5. **定期备份数据**
6. **监控系统日志**
7. **限制API访问频率**
8. **定期安全审计**

## 技术支持

如遇到部署问题，请：
1. 查看日志文件
2. 检查系统资源
3. 参考故障排除指南
4. 联系技术支持团队

**联系方式：**
- 邮箱: <EMAIL>
- 文档: https://docs.financeinsight.com
- 问题反馈: https://github.com/financeinsight/financeinsight-pro/issues
