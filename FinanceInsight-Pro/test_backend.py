#!/usr/bin/env python3
"""
后端服务简单测试
不依赖数据库，测试基本API结构
"""

import sys
import os
sys.path.append('backend')

def test_imports():
    """测试基本导入"""
    print("🔍 测试Python模块导入...")
    
    try:
        # 测试FastAPI导入
        from fastapi import FastAPI
        print("✅ FastAPI导入成功")
        
        # 测试Pydantic导入
        from pydantic import BaseModel
        print("✅ Pydantic导入成功")
        
        # 测试项目模块导入
        from app.core.config import Settings
        print("✅ 配置模块导入成功")
        
        from app.models.base import Base
        print("✅ 基础模型导入成功")
        
        from app.models.user import User
        print("✅ 用户模型导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_config():
    """测试配置"""
    print("\n🔍 测试配置系统...")
    
    try:
        from app.core.config import Settings
        
        # 创建配置实例
        settings = Settings()
        
        print(f"✅ 应用名称: {settings.APP_NAME}")
        print(f"✅ 应用版本: {settings.APP_VERSION}")
        print(f"✅ 环境: {settings.ENVIRONMENT}")
        print(f"✅ 调试模式: {settings.DEBUG}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_models():
    """测试数据模型"""
    print("\n🔍 测试数据模型...")
    
    try:
        from app.models.user import User, SubscriptionType, UserStatus
        from app.models.news import News, NewsCategory, NewsSentiment
        from app.models.stock import Stock, Market
        from app.models.recommendation import Recommendation, RecommendationType
        
        print("✅ 用户模型定义正确")
        print("✅ 新闻模型定义正确")
        print("✅ 股票模型定义正确")
        print("✅ 推荐模型定义正确")
        
        # 测试枚举
        print(f"✅ 订阅类型: {list(SubscriptionType)}")
        print(f"✅ 新闻分类: {list(NewsCategory)}")
        print(f"✅ 股票市场: {list(Market)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型测试失败: {e}")
        return False

def test_api_structure():
    """测试API结构"""
    print("\n🔍 测试API结构...")
    
    try:
        from fastapi import FastAPI
        from app.api.v1.api import api_router
        
        # 创建FastAPI应用
        app = FastAPI(title="Test App")
        app.include_router(api_router, prefix="/api/v1")
        
        print("✅ API路由结构正确")
        print(f"✅ 路由数量: {len(app.routes)}")
        
        return True
        
    except Exception as e:
        print(f"❌ API结构测试失败: {e}")
        return False

def create_mock_app():
    """创建模拟应用进行测试"""
    print("\n🔍 创建模拟应用...")
    
    try:
        from fastapi import FastAPI
        from fastapi.responses import JSONResponse
        
        app = FastAPI(
            title="FinanceInsight Pro Mock API",
            description="智能财经新闻分析与A股推荐系统 - 模拟API",
            version="1.0.0"
        )
        
        @app.get("/")
        async def root():
            return {"message": "FinanceInsight Pro API", "status": "running"}
        
        @app.get("/health")
        async def health():
            return {"status": "healthy", "service": "FinanceInsight Pro"}
        
        @app.get("/api/v1/news")
        async def get_news():
            return {
                "items": [
                    {
                        "id": 1,
                        "title": "央行降准释放流动性",
                        "source": "新华社",
                        "sentiment": "positive",
                        "importance": 5
                    }
                ],
                "total": 1
            }
        
        @app.get("/api/v1/stocks")
        async def get_stocks():
            return {
                "items": [
                    {
                        "code": "000001",
                        "name": "平安银行",
                        "price": 14.25,
                        "change": 2.15
                    }
                ],
                "total": 1
            }
        
        @app.get("/api/v1/recommendations")
        async def get_recommendations():
            return {
                "items": [
                    {
                        "id": 1,
                        "stock_code": "000001",
                        "stock_name": "平安银行",
                        "action": "buy",
                        "score": 8.5,
                        "risk_level": 3
                    }
                ],
                "total": 1
            }
        
        print("✅ 模拟应用创建成功")
        return app
        
    except Exception as e:
        print(f"❌ 模拟应用创建失败: {e}")
        return None

def run_mock_server():
    """运行模拟服务器"""
    print("\n🚀 启动模拟服务器...")
    
    try:
        import uvicorn
        app = create_mock_app()
        
        if app:
            print("✅ 模拟服务器准备就绪")
            print("🌐 访问地址: http://localhost:8000")
            print("📚 API文档: http://localhost:8000/docs")
            print("⚡ 按 Ctrl+C 停止服务器")
            
            # 启动服务器
            uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
        
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")

def main():
    """主函数"""
    print("🧪 FinanceInsight Pro 后端测试")
    print("=" * 50)
    
    # 运行测试
    tests = [
        test_imports,
        test_config,
        test_models,
        test_api_structure
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        
        # 询问是否启动模拟服务器
        try:
            response = input("\n是否启动模拟API服务器？(y/N): ")
            if response.lower() in ['y', 'yes']:
                run_mock_server()
        except KeyboardInterrupt:
            print("\n👋 测试结束")
    else:
        print("❌ 部分测试失败，请检查依赖和代码")

if __name__ == "__main__":
    main()
