import React, { Suspense } from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { ConfigProvider, App as AntdApp, theme } from 'antd'
import { Provider } from 'react-redux'
import { PersistGate } from 'redux-persist/integration/react'
import { HelmetProvider } from 'react-helmet-async'
import { ErrorBoundary } from 'react-error-boundary'
import zhCN from 'antd/locale/zh_CN'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'

import { store, persistor } from '@/store'
import { useAppSelector } from '@/hooks/redux'
import Layout from '@/components/Layout'
import Loading from '@/components/Loading'
import ErrorFallback from '@/components/ErrorFallback'
import AuthGuard from '@/components/AuthGuard'

// 页面组件懒加载
const Dashboard = React.lazy(() => import('@/pages/Dashboard'))
const News = React.lazy(() => import('@/pages/News'))
const NewsDetail = React.lazy(() => import('@/pages/News/Detail'))
const Stocks = React.lazy(() => import('@/pages/Stocks'))
const StockDetail = React.lazy(() => import('@/pages/Stocks/Detail'))
const Recommendations = React.lazy(() => import('@/pages/Recommendations'))
const RecommendationDetail = React.lazy(() => import('@/pages/Recommendations/Detail'))
const Analysis = React.lazy(() => import('@/pages/Analysis'))
const Profile = React.lazy(() => import('@/pages/Profile'))
const Settings = React.lazy(() => import('@/pages/Settings'))
const Login = React.lazy(() => import('@/pages/Auth/Login'))
const Register = React.lazy(() => import('@/pages/Auth/Register'))
const NotFound = React.lazy(() => import('@/pages/NotFound'))

// 设置dayjs中文
dayjs.locale('zh-cn')

// 主题配置
const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isDarkMode } = useAppSelector((state) => state.theme)
  
  return (
    <ConfigProvider
      locale={zhCN}
      theme={{
        algorithm: isDarkMode ? theme.darkAlgorithm : theme.defaultAlgorithm,
        token: {
          colorPrimary: '#1890ff',
          colorSuccess: '#52c41a',
          colorWarning: '#faad14',
          colorError: '#f5222d',
          fontSize: 14,
          borderRadius: 6,
        },
        components: {
          Layout: {
            headerBg: isDarkMode ? '#001529' : '#ffffff',
            siderBg: isDarkMode ? '#001529' : '#ffffff',
            bodyBg: isDarkMode ? '#141414' : '#f0f2f5',
          },
          Menu: {
            darkItemBg: '#001529',
            darkSubMenuItemBg: '#000c17',
            darkItemSelectedBg: '#1890ff',
          },
          Card: {
            headerBg: isDarkMode ? '#1f1f1f' : '#fafafa',
          },
          Table: {
            headerBg: isDarkMode ? '#1f1f1f' : '#fafafa',
          },
        },
      }}
    >
      <AntdApp>
        {children}
      </AntdApp>
    </ConfigProvider>
  )
}

// 路由配置
const AppRoutes: React.FC = () => {
  return (
    <Suspense fallback={<Loading />}>
      <Routes>
        {/* 公开路由 */}
        <Route path="/login" element={<Login />} />
        <Route path="/register" element={<Register />} />
        
        {/* 受保护的路由 */}
        <Route path="/" element={<AuthGuard><Layout /></AuthGuard>}>
          <Route index element={<Navigate to="/dashboard" replace />} />
          <Route path="dashboard" element={<Dashboard />} />
          
          {/* 新闻模块 */}
          <Route path="news" element={<News />} />
          <Route path="news/:id" element={<NewsDetail />} />
          
          {/* 股票模块 */}
          <Route path="stocks" element={<Stocks />} />
          <Route path="stocks/:code" element={<StockDetail />} />
          
          {/* 推荐模块 */}
          <Route path="recommendations" element={<Recommendations />} />
          <Route path="recommendations/:id" element={<RecommendationDetail />} />
          
          {/* 分析模块 */}
          <Route path="analysis" element={<Analysis />} />
          
          {/* 用户模块 */}
          <Route path="profile" element={<Profile />} />
          <Route path="settings" element={<Settings />} />
        </Route>
        
        {/* 404页面 */}
        <Route path="*" element={<NotFound />} />
      </Routes>
    </Suspense>
  )
}

// 主应用组件
const App: React.FC = () => {
  return (
    <ErrorBoundary
      FallbackComponent={ErrorFallback}
      onError={(error, errorInfo) => {
        console.error('应用错误:', error, errorInfo)
        // 这里可以添加错误上报逻辑
      }}
    >
      <HelmetProvider>
        <Provider store={store}>
          <PersistGate loading={<Loading />} persistor={persistor}>
            <Router>
              <ThemeProvider>
                <AppRoutes />
              </ThemeProvider>
            </Router>
          </PersistGate>
        </Provider>
      </HelmetProvider>
    </ErrorBoundary>
  )
}

export default App
