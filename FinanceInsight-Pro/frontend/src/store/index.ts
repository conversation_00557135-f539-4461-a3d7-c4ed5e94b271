import { configureStore } from '@reduxjs/toolkit'
import { persistStore, persistReducer } from 'redux-persist'
import storage from 'redux-persist/lib/storage'
import { combineReducers } from '@reduxjs/toolkit'

// 导入各个slice
import authSlice from './slices/authSlice'
import userSlice from './slices/userSlice'
import themeSlice from './slices/themeSlice'
import newsSlice from './slices/newsSlice'
import stockSlice from './slices/stockSlice'
import recommendationSlice from './slices/recommendationSlice'
import analysisSlice from './slices/analysisSlice'

// 持久化配置
const persistConfig = {
  key: 'financeinsight-pro',
  storage,
  whitelist: ['auth', 'user', 'theme'], // 只持久化这些reducer
  blacklist: ['news', 'stock', 'recommendation', 'analysis'], // 不持久化这些reducer
}

// 根reducer
const rootReducer = combineReducers({
  auth: authSlice,
  user: userSlice,
  theme: themeSlice,
  news: newsSlice,
  stock: stockSlice,
  recommendation: recommendationSlice,
  analysis: analysisSlice,
})

// 持久化reducer
const persistedReducer = persistReducer(persistConfig, rootReducer)

// 配置store
export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
        ignoredPaths: ['register'],
      },
    }),
  devTools: __DEV__,
})

// 创建persistor
export const persistor = persistStore(store)

// 导出类型
export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch

// 清除持久化数据的方法
export const clearPersistedData = () => {
  persistor.purge()
}
