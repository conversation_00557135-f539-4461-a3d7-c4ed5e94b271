import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import { message } from 'antd'
import { authAPI } from '@/services/auth'
import { setToken, removeToken } from '@/utils/auth'
import type { LoginRequest, RegisterRequest, TokenResponse, UserInfo } from '@/types/auth'

// 异步action - 登录
export const login = createAsyncThunk(
  'auth/login',
  async (loginData: LoginRequest, { rejectWithValue }) => {
    try {
      const response = await authAPI.login(loginData)
      setToken(response.access_token)
      message.success('登录成功')
      return response
    } catch (error: any) {
      message.error(error.message || '登录失败')
      return rejectWithValue(error.message || '登录失败')
    }
  }
)

// 异步action - 注册
export const register = createAsyncThunk(
  'auth/register',
  async (registerData: RegisterRequest, { rejectWithValue }) => {
    try {
      const response = await authAPI.register(registerData)
      message.success('注册成功，请登录')
      return response
    } catch (error: any) {
      message.error(error.message || '注册失败')
      return rejectWithValue(error.message || '注册失败')
    }
  }
)

// 异步action - 刷新令牌
export const refreshToken = createAsyncThunk(
  'auth/refreshToken',
  async (refreshToken: string, { rejectWithValue }) => {
    try {
      const response = await authAPI.refreshToken(refreshToken)
      setToken(response.access_token)
      return response
    } catch (error: any) {
      removeToken()
      return rejectWithValue(error.message || '令牌刷新失败')
    }
  }
)

// 异步action - 登出
export const logout = createAsyncThunk(
  'auth/logout',
  async (_, { getState, rejectWithValue }) => {
    try {
      const state = getState() as any
      const token = state.auth.token
      if (token) {
        await authAPI.logout(token)
      }
      removeToken()
      message.success('已安全退出')
      return null
    } catch (error: any) {
      removeToken()
      return rejectWithValue(error.message || '登出失败')
    }
  }
)

// 异步action - 修改密码
export const changePassword = createAsyncThunk(
  'auth/changePassword',
  async (passwordData: { oldPassword: string; newPassword: string }, { rejectWithValue }) => {
    try {
      await authAPI.changePassword(passwordData)
      message.success('密码修改成功，请重新登录')
      removeToken()
      return null
    } catch (error: any) {
      message.error(error.message || '密码修改失败')
      return rejectWithValue(error.message || '密码修改失败')
    }
  }
)

// 状态接口
interface AuthState {
  isAuthenticated: boolean
  token: string | null
  refreshToken: string | null
  userInfo: UserInfo | null
  loading: boolean
  error: string | null
  loginTime: number | null
  expiresIn: number | null
}

// 初始状态
const initialState: AuthState = {
  isAuthenticated: false,
  token: null,
  refreshToken: null,
  userInfo: null,
  loading: false,
  error: null,
  loginTime: null,
  expiresIn: null,
}

// 创建slice
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    // 清除错误
    clearError: (state) => {
      state.error = null
    },
    
    // 设置用户信息
    setUserInfo: (state, action: PayloadAction<UserInfo>) => {
      state.userInfo = action.payload
    },
    
    // 更新用户信息
    updateUserInfo: (state, action: PayloadAction<Partial<UserInfo>>) => {
      if (state.userInfo) {
        state.userInfo = { ...state.userInfo, ...action.payload }
      }
    },
    
    // 重置状态
    resetAuth: (state) => {
      Object.assign(state, initialState)
    },
    
    // 设置令牌（用于从localStorage恢复）
    setTokenFromStorage: (state, action: PayloadAction<string>) => {
      state.token = action.payload
      state.isAuthenticated = true
    },
  },
  extraReducers: (builder) => {
    // 登录
    builder
      .addCase(login.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(login.fulfilled, (state, action) => {
        state.loading = false
        state.isAuthenticated = true
        state.token = action.payload.access_token
        state.refreshToken = action.payload.refresh_token
        state.userInfo = action.payload.user_info
        state.loginTime = Date.now()
        state.expiresIn = action.payload.expires_in
        state.error = null
      })
      .addCase(login.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
    
    // 注册
    builder
      .addCase(register.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(register.fulfilled, (state) => {
        state.loading = false
        state.error = null
      })
      .addCase(register.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
    
    // 刷新令牌
    builder
      .addCase(refreshToken.pending, (state) => {
        state.loading = true
      })
      .addCase(refreshToken.fulfilled, (state, action) => {
        state.loading = false
        state.token = action.payload.access_token
        state.refreshToken = action.payload.refresh_token
        state.userInfo = action.payload.user_info
        state.loginTime = Date.now()
        state.expiresIn = action.payload.expires_in
        state.error = null
      })
      .addCase(refreshToken.rejected, (state, action) => {
        state.loading = false
        state.isAuthenticated = false
        state.token = null
        state.refreshToken = null
        state.userInfo = null
        state.error = action.payload as string
      })
    
    // 登出
    builder
      .addCase(logout.pending, (state) => {
        state.loading = true
      })
      .addCase(logout.fulfilled, (state) => {
        Object.assign(state, initialState)
      })
      .addCase(logout.rejected, (state) => {
        Object.assign(state, initialState)
      })
    
    // 修改密码
    builder
      .addCase(changePassword.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(changePassword.fulfilled, (state) => {
        state.loading = false
        Object.assign(state, initialState)
      })
      .addCase(changePassword.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
  },
})

// 导出actions
export const {
  clearError,
  setUserInfo,
  updateUserInfo,
  resetAuth,
  setTokenFromStorage,
} = authSlice.actions

// 选择器
export const selectAuth = (state: { auth: AuthState }) => state.auth
export const selectIsAuthenticated = (state: { auth: AuthState }) => state.auth.isAuthenticated
export const selectUserInfo = (state: { auth: AuthState }) => state.auth.userInfo
export const selectAuthLoading = (state: { auth: AuthState }) => state.auth.loading
export const selectAuthError = (state: { auth: AuthState }) => state.auth.error

// 检查令牌是否即将过期（提前5分钟刷新）
export const selectTokenNeedsRefresh = (state: { auth: AuthState }) => {
  const { loginTime, expiresIn } = state.auth
  if (!loginTime || !expiresIn) return false
  
  const now = Date.now()
  const expirationTime = loginTime + (expiresIn * 1000)
  const refreshThreshold = 5 * 60 * 1000 // 5分钟
  
  return (expirationTime - now) < refreshThreshold
}

// 导出reducer
export default authSlice.reducer
