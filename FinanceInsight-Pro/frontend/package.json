{"name": "financeinsight-pro-frontend", "version": "1.0.0", "description": "FinanceInsight Pro 前端应用 - 智能财经新闻分析与A股推荐系统", "private": true, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint src --ext ts,tsx --fix", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "analyze": "npm run build && npx vite-bundle-analyzer dist"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "@ant-design/pro-components": "^2.6.43", "@ant-design/pro-layout": "^7.17.16", "@ant-design/pro-table": "^3.12.0", "@ant-design/pro-form": "^2.25.1", "@ant-design/pro-descriptions": "^2.10.38", "@ant-design/pro-list": "^2.5.38", "@ant-design/pro-card": "^2.6.43", "@ant-design/icons": "^5.0.1", "antd": "^5.12.8", "@reduxjs/toolkit": "^2.0.1", "react-redux": "^9.0.4", "redux-persist": "^6.0.0", "axios": "^1.6.2", "dayjs": "^1.11.10", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "@types/echarts": "^4.9.22", "lodash-es": "^4.17.21", "classnames": "^2.3.2", "ahooks": "^3.7.8", "swr": "^2.2.4", "react-query": "^3.39.3", "react-helmet-async": "^2.0.4", "react-error-boundary": "^4.0.11", "nprogress": "^0.2.0", "copy-to-clipboard": "^3.3.3", "qrcode.js": "^1.0.0", "js-cookie": "^3.0.5", "crypto-js": "^4.2.0", "uuid": "^9.0.1", "mitt": "^3.0.1", "immer": "^10.0.3", "use-immer": "^0.9.0"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/node": "^20.10.4", "@types/lodash-es": "^4.17.12", "@types/nprogress": "^0.2.3", "@types/js-cookie": "^3.0.6", "@types/crypto-js": "^4.2.1", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "typescript": "^5.2.2", "vite": "^5.0.8", "vite-plugin-eslint": "^1.8.1", "vite-plugin-mock": "^3.0.0", "vite-plugin-windicss": "^1.9.3", "vite-bundle-analyzer": "^0.7.0", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "prettier": "^3.1.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "vitest": "^1.0.4", "@vitest/ui": "^1.0.4", "jsdom": "^23.0.1", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "windicss": "^3.5.6", "mockjs": "^1.1.0", "@types/mockjs": "^1.0.10"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "keywords": ["react", "typescript", "vite", "antd", "finance", "stock", "news", "ai", "recommendation"], "author": "FinanceInsight Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/financeinsight/financeinsight-pro.git"}, "bugs": {"url": "https://github.com/financeinsight/financeinsight-pro/issues"}, "homepage": "https://financeinsight.com"}