# FinanceInsight Pro - 智能财经新闻分析与A股推荐系统

## 项目简介

FinanceInsight Pro是一款基于AI技术的智能财经分析平台，通过实时抓取全球政治金融新闻，结合深度学习和自然语言处理技术，为用户提供精准的A股投资建议和市场洞察。

## 核心功能

- 🔍 **智能新闻抓取**: 7x24小时监控全球财经新闻
- 🧠 **AI情感分析**: 深度学习驱动的新闻情感识别
- 📊 **股票关联分析**: 新闻事件与股票的智能关联匹配
- 🎯 **精准推荐**: 基于多因子模型的股票推荐系统
- 📈 **数据可视化**: 专业级图表和趋势分析
- ⚡ **实时更新**: 毫秒级数据刷新和推送

## 技术架构

### 后端技术栈
- **Python 3.9+** - 主要开发语言
- **FastAPI** - 高性能Web框架
- **PyTorch** - 深度学习框架
- **MySQL 8.0** - 关系型数据库
- **Redis** - 缓存和消息队列
- **Elasticsearch** - 全文搜索引擎
- **Docker** - 容器化部署

### 前端技术栈
- **React 18** - 用户界面框架
- **TypeScript** - 类型安全的JavaScript
- **Ant Design Pro** - 企业级UI组件库
- **ECharts** - 数据可视化图表库
- **Redux Toolkit** - 状态管理

### AI/ML技术栈
- **BERT** - 中文文本理解模型
- **Transformers** - 预训练模型库
- **scikit-learn** - 机器学习工具包
- **XGBoost** - 梯度提升算法
- **spaCy** - 自然语言处理

## 项目结构

```
FinanceInsight-Pro/
├── backend/                 # 后端服务
│   ├── app/                # 应用核心代码
│   │   ├── api/           # API路由
│   │   ├── core/          # 核心配置
│   │   ├── services/      # 业务服务
│   │   ├── models/        # 数据模型
│   │   └── utils/         # 工具函数
│   ├── config/            # 配置文件
│   ├── tests/             # 测试代码
│   └── migrations/        # 数据库迁移
├── frontend/              # 前端应用
│   ├── src/              # 源代码
│   │   ├── components/   # 组件
│   │   ├── pages/        # 页面
│   │   ├── services/     # API服务
│   │   └── utils/        # 工具函数
│   ├── public/           # 静态资源
│   └── tests/            # 测试代码
├── mobile/               # 移动端应用
├── docs/                 # 项目文档
├── scripts/              # 脚本工具
├── tests/                # 集成测试
└── deployment/           # 部署配置
    ├── docker/          # Docker配置
    ├── kubernetes/      # K8s配置
    └── scripts/         # 部署脚本
```

## 快速开始

### 环境要求
- Python 3.9+
- Node.js 16+
- MySQL 8.0
- Redis 6.0+
- Docker (可选)

### 后端启动

```bash
# 进入后端目录
cd backend

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate   # Windows

# 安装依赖
pip install -r requirements.txt

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置数据库等信息

# 数据库迁移
alembic upgrade head

# 启动服务
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 前端启动

```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm start
```

### Docker启动

```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

## API文档

启动后端服务后，访问以下地址查看API文档：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 主要API端点

### 认证相关
- `POST /auth/login` - 用户登录
- `POST /auth/register` - 用户注册
- `POST /auth/refresh` - 刷新Token

### 新闻相关
- `GET /news` - 获取新闻列表
- `GET /news/{id}` - 获取新闻详情
- `GET /news/hot` - 获取热点新闻

### 股票相关
- `GET /stocks` - 获取股票列表
- `GET /stocks/{code}` - 获取股票详情
- `GET /stocks/{code}/price` - 获取实时价格

### 推荐相关
- `GET /recommendations` - 获取推荐列表
- `POST /recommendations/refresh` - 刷新推荐

## 数据源

### 新闻数据源
- 新华社、人民日报、财新网
- 路透社、彭博社、华尔街日报
- 央行、证监会等监管机构

### 股票数据源
- 东方财富网API
- 同花顺数据接口
- Wind金融终端

## 开发指南

### 代码规范
- 后端遵循PEP 8规范
- 前端使用ESLint + Prettier
- 提交信息遵循Conventional Commits

### 测试
```bash
# 后端测试
cd backend
pytest

# 前端测试
cd frontend
npm test

# 集成测试
cd tests
python -m pytest integration/
```

### 部署

#### 开发环境
```bash
docker-compose -f docker-compose.dev.yml up -d
```

#### 生产环境
```bash
# 使用Kubernetes部署
kubectl apply -f deployment/kubernetes/

# 或使用Docker Swarm
docker stack deploy -c docker-compose.prod.yml financeinsight
```

## 监控和日志

### 监控指标
- API响应时间和错误率
- 数据库性能指标
- 推荐系统准确率
- 用户行为分析

### 日志系统
- 应用日志：使用Python logging
- 访问日志：Nginx access log
- 错误追踪：Sentry集成
- 日志聚合：ELK Stack

## 性能优化

### 后端优化
- 数据库索引优化
- Redis缓存策略
- 异步任务处理
- API响应压缩

### 前端优化
- 代码分割和懒加载
- 图片压缩和CDN
- 缓存策略优化
- 性能监控

## 安全措施

- JWT Token认证
- API访问限流
- SQL注入防护
- XSS攻击防护
- HTTPS强制加密
- 敏感数据加密存储

## 贡献指南

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

- 项目主页: https://github.com/financeinsight/financeinsight-pro
- 问题反馈: https://github.com/financeinsight/financeinsight-pro/issues
- 邮箱: <EMAIL>

## 更新日志

### v1.0.0 (2024-01-15)
- 初始版本发布
- 实现核心新闻抓取功能
- 完成AI情感分析引擎
- 上线股票推荐系统
- 发布Web前端界面

---

**FinanceInsight Pro** - 让投资决策更智能 🚀
