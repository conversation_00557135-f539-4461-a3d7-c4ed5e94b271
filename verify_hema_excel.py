#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证盒马鲜生门店信息Excel表格
"""

from openpyxl import load_workbook

def verify_hema_excel():
    try:
        # 加载Excel文件
        wb = load_workbook("hema.xlsx")
        ws = wb.active
        
        print(f"工作表名称: {ws.title}")
        print(f"总行数: {ws.max_row}")
        print(f"总列数: {ws.max_column}")
        print(f"门店数量: {ws.max_row - 1}")  # 减去表头行
        
        # 显示表头
        print("\n表头信息:")
        headers = []
        for col in range(1, ws.max_column + 1):
            header = ws.cell(row=1, column=col).value
            headers.append(header)
            print(f"第{col}列: {header}")
        
        # 统计各省份门店数量
        province_count = {}
        for row in range(2, ws.max_row + 1):
            province = ws.cell(row=row, column=1).value
            if province:
                province_count[province] = province_count.get(province, 0) + 1
        
        print("\n各省份门店数量统计:")
        total_stores = 0
        for province, count in sorted(province_count.items()):
            print(f"{province}: {count}家")
            total_stores += count
        
        print(f"\n总计: {total_stores}家门店")
        
        # 显示前5家门店信息作为示例
        print("\n前5家门店信息示例:")
        for row in range(2, min(7, ws.max_row + 1)):
            store_info = []
            for col in range(1, ws.max_column + 1):
                value = ws.cell(row=row, column=col).value or ""
                store_info.append(str(value))
            print(f"第{row-1}家: {' | '.join(store_info)}")
        
        return True
        
    except Exception as e:
        print(f"验证Excel文件时出错: {e}")
        return False

if __name__ == "__main__":
    print("正在验证盒马门店信息Excel文件...")
    if verify_hema_excel():
        print("\n✅ Excel文件验证成功！")
    else:
        print("\n❌ Excel文件验证失败！")
