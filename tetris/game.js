document.addEventListener('DOMContentLoaded', () => {
    // 获取画布和上下文
    const canvas = document.getElementById('gameCanvas');
    const ctx = canvas.getContext('2d');
    const nextCanvas = document.getElementById('nextCanvas');
    const nextCtx = nextCanvas.getContext('2d');
    
    // 游戏参数
    const blockSize = 30;
    const rows = 20;
    const cols = 10;
    const scoreElement = document.getElementById('score');
    const linesElement = document.getElementById('lines');
    const levelElement = document.getElementById('level');
    const startBtn = document.getElementById('startBtn');
    
    // 游戏状态
    let score = 0;
    let lines = 0;
    let level = 1;
    let gameRunning = false;
    let gameInterval;
    let speed = 1000; // 初始下落速度（毫秒）
    
    // 当前方块和下一个方块
    let currentPiece = null;
    let nextPiece = null;
    
    // 游戏板
    let board = Array(rows).fill().map(() => Array(cols).fill(0));
    
    // 方块形状和颜色
    const shapes = [
        [], // 空位，使索引对应
        [[1, 1, 1, 1]], // I
        [[1, 1, 1], [0, 1, 0]], // T
        [[1, 1, 1], [1, 0, 0]], // L
        [[1, 1, 1], [0, 0, 1]], // J
        [[1, 1], [1, 1]], // O
        [[0, 1, 1], [1, 1, 0]], // S
        [[1, 1, 0], [0, 1, 1]]  // Z
    ];
    
    const colors = [
        'rgba(0,0,0,0)',
        '#00ffff', // cyan - 发光青色
        '#ff00ff', // magenta - 发光洋红
        '#ffaa00', // orange - 发光橙色
        '#0080ff', // blue - 发光蓝色
        '#ffff00', // yellow - 发光黄色
        '#00ff80', // green - 发光绿色
        '#ff4080'  // red - 发光红色
    ];
    
    // 初始化游戏
    function initGame() {
        board = Array(rows).fill().map(() => Array(cols).fill(0));
        score = 0;
        lines = 0;
        level = 1;
        speed = 1000;
        
        scoreElement.textContent = score.toString().padStart(6, '0');
        linesElement.textContent = lines.toString().padStart(3, '0');
        levelElement.textContent = level.toString().padStart(3, '0');
        
        createNewPiece();
        drawBoard();
    }
    
    // 创建新方块
    function createNewPiece() {
        if (!nextPiece) {
            const shapeIndex = Math.floor(Math.random() * 7) + 1;
            nextPiece = {
                shape: shapes[shapeIndex],
                color: colors[shapeIndex],
                x: Math.floor((cols - shapes[shapeIndex][0].length) / 2),
                y: 0
            };
        }
        
        currentPiece = nextPiece;
        
        const shapeIndex = Math.floor(Math.random() * 7) + 1;
        nextPiece = {
            shape: shapes[shapeIndex],
            color: colors[shapeIndex],
            x: Math.floor((cols - shapes[shapeIndex][0].length) / 2),
            y: 0
        };
        
        drawNextPiece();
        
        // 检查游戏是否结束
        if (isCollision()) {
            gameOver();
        }
    }
    
    // 绘制游戏板
    function drawBoard() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // 绘制已固定的方块
        for (let y = 0; y < rows; y++) {
            for (let x = 0; x < cols; x++) {
                if (board[y][x]) {
                    drawBlock(ctx, x, y, colors[board[y][x]]);
                }
            }
        }
        
        // 绘制当前方块
        if (currentPiece) {
            for (let y = 0; y < currentPiece.shape.length; y++) {
                for (let x = 0; x < currentPiece.shape[y].length; x++) {
                    if (currentPiece.shape[y][x]) {
                        drawBlock(ctx, currentPiece.x + x, currentPiece.y + y, currentPiece.color);
                    }
                }
            }
        }
    }
    
    // 绘制下一个方块
    function drawNextPiece() {
        nextCtx.clearRect(0, 0, nextCanvas.width, nextCanvas.height);
        
        if (nextPiece) {
            const offsetX = (nextCanvas.width / blockSize - nextPiece.shape[0].length) / 2;
            const offsetY = (nextCanvas.height / blockSize - nextPiece.shape.length) / 2;
            
            for (let y = 0; y < nextPiece.shape.length; y++) {
                for (let x = 0; x < nextPiece.shape[y].length; x++) {
                    if (nextPiece.shape[y][x]) {
                        drawBlock(nextCtx, offsetX + x, offsetY + y, nextPiece.color);
                    }
                }
            }
        }
    }
    
    // 绘制单个方块 - 科幻发光效果
    function drawBlock(context, x, y, color) {
        const blockX = x * blockSize;
        const blockY = y * blockSize;

        // 主方块颜色
        context.fillStyle = color;
        context.fillRect(blockX, blockY, blockSize, blockSize);

        // 发光效果
        context.shadowColor = color;
        context.shadowBlur = 10;
        context.fillRect(blockX + 2, blockY + 2, blockSize - 4, blockSize - 4);

        // 重置阴影
        context.shadowBlur = 0;

        // 边框效果
        context.strokeStyle = color;
        context.lineWidth = 1;
        context.strokeRect(blockX, blockY, blockSize, blockSize);

        // 内部高光
        const gradient = context.createLinearGradient(blockX, blockY, blockX + blockSize, blockY + blockSize);
        gradient.addColorStop(0, 'rgba(255, 255, 255, 0.3)');
        gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
        context.fillStyle = gradient;
        context.fillRect(blockX + 1, blockY + 1, blockSize - 2, blockSize - 2);
    }
    
    // 检查碰撞
    function isCollision(offsetX = 0, offsetY = 0, newShape = currentPiece.shape) {
        for (let y = 0; y < newShape.length; y++) {
            for (let x = 0; x < newShape[y].length; x++) {
                if (newShape[y][x]) {
                    const newX = currentPiece.x + x + offsetX;
                    const newY = currentPiece.y + y + offsetY;
                    
                    if (newX < 0 || newX >= cols || newY >= rows || (newY >= 0 && board[newY][newX])) {
                        return true;
                    }
                }
            }
        }
        return false;
    }
    
    // 旋转方块
    function rotatePiece() {
        // 创建旋转后的新形状
        const newShape = [];
        for (let x = 0; x < currentPiece.shape[0].length; x++) {
            const row = [];
            for (let y = currentPiece.shape.length - 1; y >= 0; y--) {
                row.push(currentPiece.shape[y][x]);
            }
            newShape.push(row);
        }
        
        // 检查旋转后是否会碰撞
        if (!isCollision(0, 0, newShape)) {
            currentPiece.shape = newShape;
        }
    }
    
    // 移动方块
    function movePiece(dx, dy) {
        if (!isCollision(dx, dy)) {
            currentPiece.x += dx;
            currentPiece.y += dy;
            return true;
        }
        return false;
    }
    
    // 固定方块到游戏板
    function lockPiece() {
        for (let y = 0; y < currentPiece.shape.length; y++) {
            for (let x = 0; x < currentPiece.shape[y].length; x++) {
                if (currentPiece.shape[y][x]) {
                    const boardY = currentPiece.y + y;
                    if (boardY >= 0) { // 确保不会超出游戏板顶部
                        board[boardY][currentPiece.x + x] = colors.indexOf(currentPiece.color);
                    }
                }
            }
        }
        
        // 检查并清除完整的行
        clearLines();
        
        // 创建新方块
        createNewPiece();
    }
    
    // 清除完整的行
    function clearLines() {
        let linesCleared = 0;
        
        for (let y = rows - 1; y >= 0; y--) {
            if (board[y].every(cell => cell !== 0)) {
                // 移除该行并在顶部添加新行
                board.splice(y, 1);
                board.unshift(Array(cols).fill(0));
                linesCleared++;
                y++; // 再次检查同一行（现在是新行）
            }
        }
        
        if (linesCleared > 0) {
            // 更新分数和行数
            lines += linesCleared;
            score += linesCleared * 100 * level;
            
            // 更新等级
            level = Math.floor(lines / 10) + 1;
            
            // 更新速度
            speed = Math.max(100, 1000 - (level - 1) * 100);
            
            // 更新显示
            scoreElement.textContent = score.toString().padStart(6, '0');
            linesElement.textContent = lines.toString().padStart(3, '0');
            levelElement.textContent = level.toString().padStart(3, '0');
            
            // 如果游戏正在运行，更新间隔
            if (gameRunning) {
                clearInterval(gameInterval);
                gameInterval = setInterval(gameLoop, speed);
            }
        }
    }
    
    // 游戏循环
    function gameLoop() {
        if (!movePiece(0, 1)) {
            lockPiece();
        }
        drawBoard();
    }
    
    // 开始游戏
    function startGame() {
        if (gameRunning) {
            // 如果游戏已经在运行，重新开始
            clearInterval(gameInterval);
            gameRunning = false;
        }
        
        initGame();
        gameRunning = true;
        gameInterval = setInterval(gameLoop, speed);
        startBtn.querySelector('.button-text').textContent = 'RESTART';
    }
    
    // 游戏结束
    function gameOver() {
        clearInterval(gameInterval);
        gameRunning = false;

        // 创建科幻风格的游戏结束提示
        showGameOverScreen();
        startBtn.querySelector('.button-text').textContent = 'INITIALIZE';
    }

    // 显示游戏结束屏幕
    function showGameOverScreen() {
        // 创建游戏结束覆盖层
        const gameOverDiv = document.createElement('div');
        gameOverDiv.className = 'game-status show';
        gameOverDiv.innerHTML = `
            <div style="text-align: center;">
                <h2 style="margin: 0 0 20px 0; color: #ff0080; font-size: 2rem;">GAME OVER</h2>
                <p style="margin: 10px 0; color: #00ffff;">FINAL SCORE: ${score.toString().padStart(6, '0')}</p>
                <p style="margin: 10px 0; color: #00ffff;">LINES CLEARED: ${lines.toString().padStart(3, '0')}</p>
                <p style="margin: 10px 0; color: #00ffff;">LEVEL REACHED: ${level.toString().padStart(3, '0')}</p>
                <button onclick="this.parentElement.parentElement.remove()"
                        style="margin-top: 20px; padding: 10px 20px; background: rgba(0,255,255,0.2);
                               border: 1px solid #00ffff; color: #00ffff; border-radius: 5px;
                               cursor: pointer; font-family: 'Orbitron', monospace;">
                    CLOSE
                </button>
            </div>
        `;
        document.body.appendChild(gameOverDiv);
    }
    
    // 硬降（直接下落到底部）
    function hardDrop() {
        while (movePiece(0, 1)) {
            // 继续下落直到不能再下落
        }
        lockPiece();
        drawBoard();
    }
    
    // 键盘控制
    document.addEventListener('keydown', (e) => {
        if (!gameRunning) return;
        
        switch(e.key) {
            case 'ArrowLeft':
                movePiece(-1, 0);
                break;
            case 'ArrowRight':
                movePiece(1, 0);
                break;
            case 'ArrowDown':
                movePiece(0, 1);
                break;
            case 'ArrowUp':
                rotatePiece();
                break;
            case ' ':
                hardDrop();
                break;
        }
        
        drawBoard();
        e.preventDefault(); // 防止按键滚动页面
    });
    
    // 开始按钮事件
    startBtn.addEventListener('click', startGame);
    
    // 初始绘制
    drawBoard();
    drawNextPiece();
});