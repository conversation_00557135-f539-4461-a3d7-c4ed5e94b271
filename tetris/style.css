/* 科幻风格俄罗斯方块 */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Orbitron', monospace;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    margin: 0;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    color: #00ffff;
    overflow: hidden;
    position: relative;
}

/* 星空背景 */
.stars {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: transparent;
    z-index: -1;
}

.stars::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, #eee, transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
        radial-gradient(1px 1px at 90px 40px, #fff, transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
        radial-gradient(2px 2px at 160px 30px, #fff, transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: sparkle 20s linear infinite;
}

@keyframes sparkle {
    from { transform: translateX(0); }
    to { transform: translateX(-200px); }
}

.game-container {
    text-align: center;
    z-index: 1;
    position: relative;
}

/* 标题区域 */
.title-section {
    margin-bottom: 30px;
}

.cyber-title {
    font-size: 3.5rem;
    font-weight: 900;
    margin: 0;
    background: linear-gradient(45deg, #00ffff, #ff00ff, #ffff00, #00ffff);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradient-shift 3s ease-in-out infinite;
    text-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
    letter-spacing: 0.1em;
}

.subtitle {
    font-size: 1rem;
    color: #00ffff;
    opacity: 0.8;
    letter-spacing: 0.3em;
    margin-top: 10px;
    text-transform: uppercase;
}

@keyframes gradient-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.game-area {
    display: flex;
    justify-content: center;
    gap: 30px;
    align-items: flex-start;
}

/* 主游戏面板 */
.main-game-panel {
    position: relative;
}

.canvas-container {
    position: relative;
    border: 2px solid #00ffff;
    border-radius: 10px;
    background: rgba(0, 0, 0, 0.8);
    box-shadow:
        0 0 20px rgba(0, 255, 255, 0.3),
        inset 0 0 20px rgba(0, 255, 255, 0.1);
    overflow: hidden;
}

canvas {
    display: block;
    background: linear-gradient(180deg, #000814 0%, #001d3d 100%);
}

/* 扫描线效果 */
.scan-line {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, #00ffff, transparent);
    animation: scan 2s linear infinite;
    opacity: 0.6;
}

@keyframes scan {
    0% { transform: translateY(0); }
    100% { transform: translateY(600px); }
}

/* 侧边面板 */
.side-panel {
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 250px;
}

.panel {
    background: rgba(0, 20, 40, 0.9);
    border: 1px solid #00ffff;
    border-radius: 8px;
    padding: 15px;
    box-shadow:
        0 0 15px rgba(0, 255, 255, 0.2),
        inset 0 0 15px rgba(0, 255, 255, 0.05);
    position: relative;
    overflow: hidden;
}

.panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.1), transparent);
    animation: panel-glow 3s linear infinite;
}

@keyframes panel-glow {
    0% { left: -100%; }
    100% { left: 100%; }
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    border-bottom: 1px solid rgba(0, 255, 255, 0.3);
    padding-bottom: 8px;
}

.panel-title {
    font-size: 0.9rem;
    font-weight: 700;
    color: #00ffff;
    letter-spacing: 0.1em;
    text-transform: uppercase;
}

.panel-indicator {
    width: 8px;
    height: 8px;
    background: #00ff00;
    border-radius: 50%;
    box-shadow: 0 0 10px #00ff00;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.3; }
}

/* 下一个方块区域 */
.next-canvas-container {
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 5px;
    padding: 10px;
    border: 1px solid rgba(0, 255, 255, 0.2);
}

/* 分数面板 */
.stats {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(0, 255, 255, 0.1);
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    font-size: 0.8rem;
    color: #00ffff;
    opacity: 0.8;
    letter-spacing: 0.05em;
}

.stat-value {
    font-size: 1.1rem;
    font-weight: 700;
    color: #ffff00;
    text-shadow: 0 0 10px rgba(255, 255, 0, 0.5);
    font-family: 'Orbitron', monospace;
}

/* 科幻按钮 */
.cyber-button {
    position: relative;
    background: linear-gradient(45deg, #001122, #003366);
    border: 2px solid #00ffff;
    border-radius: 8px;
    padding: 15px 30px;
    font-family: 'Orbitron', monospace;
    font-size: 1rem;
    font-weight: 700;
    color: #00ffff;
    cursor: pointer;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    transition: all 0.3s ease;
    overflow: hidden;
    margin: 10px 0;
}

.cyber-button:hover {
    background: linear-gradient(45deg, #002244, #004488);
    box-shadow:
        0 0 20px rgba(0, 255, 255, 0.4),
        inset 0 0 20px rgba(0, 255, 255, 0.1);
    transform: translateY(-2px);
}

.cyber-button:active {
    transform: translateY(0);
}

.button-text {
    position: relative;
    z-index: 2;
}

.button-glow {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.cyber-button:hover .button-glow {
    left: 100%;
}

/* 控制面板 */
.control-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.control-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 0;
    border-bottom: 1px solid rgba(0, 255, 255, 0.1);
}

.control-item:last-child {
    border-bottom: none;
}

.key {
    background: rgba(0, 255, 255, 0.1);
    border: 1px solid rgba(0, 255, 255, 0.3);
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 0.8rem;
    font-weight: 700;
    color: #00ffff;
    min-width: 60px;
    text-align: center;
    font-family: 'Orbitron', monospace;
}

.action {
    font-size: 0.8rem;
    color: #ffffff;
    opacity: 0.8;
    letter-spacing: 0.05em;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .game-area {
        flex-direction: column;
        align-items: center;
        gap: 20px;
    }

    .side-panel {
        width: 300px;
    }

    .cyber-title {
        font-size: 2.5rem;
    }

    .canvas-container {
        transform: scale(0.8);
    }
}

/* 游戏状态指示器 */
.game-status {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.9);
    border: 2px solid #ff0080;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    font-size: 1.5rem;
    color: #ff0080;
    text-shadow: 0 0 20px rgba(255, 0, 128, 0.5);
    z-index: 1000;
    display: none;
}

.game-status.show {
    display: block;
    animation: status-appear 0.5s ease-out;
}

@keyframes status-appear {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.5);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}