<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CYBER TETRIS</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
</head>
<body>
    <div class="stars"></div>
    <div class="game-container">
        <div class="title-section">
            <h1 class="cyber-title">CYBER TETRIS</h1>
            <div class="subtitle">NEURAL BLOCK MATRIX</div>
        </div>
        <div class="game-area">
            <div class="main-game-panel">
                <div class="canvas-container">
                    <canvas id="gameCanvas" width="300" height="600"></canvas>
                    <div class="scan-line"></div>
                </div>
            </div>
            <div class="side-panel">
                <div class="next-piece panel">
                    <div class="panel-header">
                        <span class="panel-title">NEXT BLOCK</span>
                        <div class="panel-indicator"></div>
                    </div>
                    <div class="next-canvas-container">
                        <canvas id="nextCanvas" width="150" height="150"></canvas>
                    </div>
                </div>
                <div class="score-board panel">
                    <div class="panel-header">
                        <span class="panel-title">SYSTEM STATUS</span>
                        <div class="panel-indicator"></div>
                    </div>
                    <div class="stats">
                        <div class="stat-item">
                            <span class="stat-label">SCORE</span>
                            <span class="stat-value" id="score">000000</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">LINES</span>
                            <span class="stat-value" id="lines">000</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">LEVEL</span>
                            <span class="stat-value" id="level">001</span>
                        </div>
                    </div>
                </div>
                <button id="startBtn" class="cyber-button">
                    <span class="button-text">INITIALIZE</span>
                    <div class="button-glow"></div>
                </button>
                <div class="controls panel">
                    <div class="panel-header">
                        <span class="panel-title">CONTROLS</span>
                        <div class="panel-indicator"></div>
                    </div>
                    <div class="control-list">
                        <div class="control-item">
                            <span class="key">← →</span>
                            <span class="action">MOVE</span>
                        </div>
                        <div class="control-item">
                            <span class="key">↑</span>
                            <span class="action">ROTATE</span>
                        </div>
                        <div class="control-item">
                            <span class="key">↓</span>
                            <span class="action">ACCELERATE</span>
                        </div>
                        <div class="control-item">
                            <span class="key">SPACE</span>
                            <span class="action">DROP</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="game.js"></script>
</body>
</html>