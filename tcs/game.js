document.addEventListener('DOMContentLoaded', () => {
    const canvas = document.getElementById('gameCanvas');
    const ctx = canvas.getContext('2d');
    const scoreElement = document.getElementById('score');
    const startBtn = document.getElementById('startBtn');
    
    const gridSize = 20;
    const tileCount = canvas.width / gridSize;
    
    let snake = [];
    let food = {};
    let dx = gridSize;
    let dy = 0;
    let score = 0;
    let gameRunning = false;
    let gameInterval;
    
    function initGame() {
        snake = [
            {x: 5 * gridSize, y: 5 * gridSize}
        ];
        createFood();
        score = 0;
        scoreElement.textContent = score;
        dx = gridSize;
        dy = 0;
    }
    
    function createFood() {
        food = {
            x: Math.floor(Math.random() * tileCount) * gridSize,
            y: Math.floor(Math.random() * tileCount) * gridSize
        };
        
        // 确保食物不会出现在蛇身上
        for (let cell of snake) {
            if (cell.x === food.x && cell.y === food.y) {
                createFood();
                break;
            }
        }
    }
    
    function drawGame() {
        // 清空画布
        ctx.fillStyle = 'white';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        
        // 绘制食物
        ctx.fillStyle = 'red';
        ctx.fillRect(food.x, food.y, gridSize, gridSize);
        
        // 绘制蛇
        ctx.fillStyle = 'green';
        for (let cell of snake) {
            ctx.fillRect(cell.x, cell.y, gridSize, gridSize);
        }
    }
    
    function moveSnake() {
        const head = {x: snake[0].x + dx, y: snake[0].y + dy};
        
        // 检查是否撞墙
        if (head.x < 0 || head.x >= canvas.width || head.y < 0 || head.y >= canvas.height) {
            gameOver();
            return;
        }
        
        // 检查是否撞到自己
        for (let i = 0; i < snake.length; i++) {
            if (head.x === snake[i].x && head.y === snake[i].y) {
                gameOver();
                return;
            }
        }
        
        snake.unshift(head);
        
        // 检查是否吃到食物
        if (head.x === food.x && head.y === food.y) {
            score++;
            scoreElement.textContent = score;
            createFood();
        } else {
            snake.pop();
        }
    }
    
    function gameLoop() {
        moveSnake();
        drawGame();
    }
    
    function startGame() {
        if (gameRunning) return;
        
        initGame();
        gameRunning = true;
        gameInterval = setInterval(gameLoop, 150);
        startBtn.textContent = '重新开始';
    }
    
    function gameOver() {
        clearInterval(gameInterval);
        gameRunning = false;
        alert(`游戏结束！你的得分是: ${score}`);
        startBtn.textContent = '开始游戏';
    }
    
    // 键盘控制
    document.addEventListener('keydown', (e) => {
        if (!gameRunning) return;
        
        switch(e.key) {
            case 'ArrowUp':
                if (dy === 0) { dx = 0; dy = -gridSize; }
                break;
            case 'ArrowDown':
                if (dy === 0) { dx = 0; dy = gridSize; }
                break;
            case 'ArrowLeft':
                if (dx === 0) { dx = -gridSize; dy = 0; }
                break;
            case 'ArrowRight':
                if (dx === 0) { dx = gridSize; dy = 0; }
                break;
        }
    });
    
    startBtn.addEventListener('click', startGame);
    
    // 初始绘制
    drawGame();
});