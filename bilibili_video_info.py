#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
B站视频信息获取工具
注意：此脚本仅用于获取视频信息，不涉及版权内容下载
"""

import requests
import json
import re
from urllib.parse import urlparse, parse_qs

def extract_bv_id(url):
    """从B站URL中提取BV号"""
    # 支持多种B站URL格式
    patterns = [
        r'BV[a-zA-Z0-9]+',
        r'av(\d+)',
    ]
    
    for pattern in patterns:
        match = re.search(pattern, url)
        if match:
            if pattern.startswith('av'):
                return f"av{match.group(1)}"
            else:
                return match.group(0)
    return None

def get_video_info(bv_id):
    """获取B站视频基本信息"""
    try:
        # B站API接口
        api_url = f"https://api.bilibili.com/x/web-interface/view?bvid={bv_id}"
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://www.bilibili.com/'
        }
        
        response = requests.get(api_url, headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            
            if data['code'] == 0:
                video_data = data['data']
                
                # 提取视频信息
                video_info = {
                    'bvid': video_data.get('bvid', ''),
                    'aid': video_data.get('aid', ''),
                    'title': video_data.get('title', ''),
                    'desc': video_data.get('desc', ''),
                    'duration': video_data.get('duration', 0),
                    'pubdate': video_data.get('pubdate', 0),
                    'view': video_data.get('stat', {}).get('view', 0),
                    'danmaku': video_data.get('stat', {}).get('danmaku', 0),
                    'reply': video_data.get('stat', {}).get('reply', 0),
                    'favorite': video_data.get('stat', {}).get('favorite', 0),
                    'coin': video_data.get('stat', {}).get('coin', 0),
                    'share': video_data.get('stat', {}).get('share', 0),
                    'like': video_data.get('stat', {}).get('like', 0),
                    'owner': {
                        'mid': video_data.get('owner', {}).get('mid', ''),
                        'name': video_data.get('owner', {}).get('name', ''),
                        'face': video_data.get('owner', {}).get('face', '')
                    },
                    'pic': video_data.get('pic', ''),
                    'pages': len(video_data.get('pages', [])),
                    'tname': video_data.get('tname', ''),
                    'tags': [tag.get('tag_name', '') for tag in video_data.get('tag', [])]
                }
                
                return video_info
            else:
                print(f"API返回错误: {data['message']}")
                return None
        else:
            print(f"请求失败，状态码: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"获取视频信息时出错: {e}")
        return None

def format_duration(seconds):
    """格式化时长"""
    hours = seconds // 3600
    minutes = (seconds % 3600) // 60
    seconds = seconds % 60
    
    if hours > 0:
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
    else:
        return f"{minutes:02d}:{seconds:02d}"

def format_number(num):
    """格式化数字显示"""
    if num >= 10000:
        return f"{num/10000:.1f}万"
    else:
        return str(num)

def display_video_info(video_info):
    """显示视频信息"""
    if not video_info:
        print("❌ 无法获取视频信息")
        return
    
    print("=" * 60)
    print("📺 B站视频信息")
    print("=" * 60)
    print(f"🎬 标题: {video_info['title']}")
    print(f"🆔 BV号: {video_info['bvid']}")
    print(f"🆔 AV号: av{video_info['aid']}")
    print(f"👤 UP主: {video_info['owner']['name']}")
    print(f"⏱️  时长: {format_duration(video_info['duration'])}")
    print(f"📊 播放量: {format_number(video_info['view'])}")
    print(f"👍 点赞数: {format_number(video_info['like'])}")
    print(f"🪙 投币数: {format_number(video_info['coin'])}")
    print(f"⭐ 收藏数: {format_number(video_info['favorite'])}")
    print(f"📤 分享数: {format_number(video_info['share'])}")
    print(f"💬 评论数: {format_number(video_info['reply'])}")
    print(f"🏷️  分区: {video_info['tname']}")
    print(f"📄 分P数: {video_info['pages']}")
    
    if video_info['tags']:
        print(f"🏷️  标签: {', '.join(video_info['tags'][:5])}")  # 只显示前5个标签
    
    if video_info['desc']:
        desc = video_info['desc'][:100] + "..." if len(video_info['desc']) > 100 else video_info['desc']
        print(f"📝 简介: {desc}")
    
    print("=" * 60)

def main():
    # 目标视频URL
    video_url = "https://www.bilibili.com/video/BV1GLfVY7E9o/?spm_id_from=333.337.search-card.all.click&vd_source=dc3434ff0cf295071bd0ed06ef5d878a"
    
    print("🔍 正在解析B站视频信息...")
    
    # 提取BV号
    bv_id = extract_bv_id(video_url)
    if not bv_id:
        print("❌ 无法从URL中提取BV号")
        return
    
    print(f"📋 提取到BV号: {bv_id}")
    
    # 获取视频信息
    video_info = get_video_info(bv_id)
    
    # 显示视频信息
    display_video_info(video_info)
    
    print("\n💡 提示:")
    print("• 此脚本仅获取视频的公开信息")
    print("• 如需下载视频，请使用专业工具如 you-get、yt-dlp 等")
    print("• 请遵守版权法律法规，仅用于个人学习研究")
    print("• 商业用途请联系版权方获得授权")

if __name__ == "__main__":
    main()
