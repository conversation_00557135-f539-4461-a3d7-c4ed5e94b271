#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证增强版盒马鲜生门店信息Excel表格
"""

from openpyxl import load_workbook

def verify_enhanced_hema_excel():
    try:
        # 加载Excel文件
        wb = load_workbook("hema_enhanced.xlsx")
        ws = wb.active
        
        print(f"📊 工作表名称: {ws.title}")
        print(f"📏 总行数: {ws.max_row}")
        print(f"📏 总列数: {ws.max_column}")
        print(f"🏪 门店数量: {ws.max_row - 1}")  # 减去表头行
        
        # 显示表头
        print("\n📋 表头信息:")
        headers = []
        for col in range(1, ws.max_column + 1):
            header = ws.cell(row=1, column=col).value
            headers.append(header)
            print(f"   第{col}列: {header}")
        
        # 统计业态类型
        store_type_count = {}
        district_count = {}
        
        for row in range(2, ws.max_row + 1):
            # 业态类型统计
            store_type = ws.cell(row=row, column=5).value  # 第5列是业态类型
            if store_type:
                store_type_count[store_type] = store_type_count.get(store_type, 0) + 1
            
            # 区县统计
            district = ws.cell(row=row, column=3).value  # 第3列是区县
            if district:
                district_count[district] = district_count.get(district, 0) + 1
        
        print("\n🏪 业态类型统计:")
        for store_type, count in sorted(store_type_count.items()):
            print(f"   {store_type}: {count}家")
        
        print("\n📍 区县分布统计:")
        for district, count in sorted(district_count.items()):
            print(f"   {district}: {count}家")
        
        # 显示前5家门店信息作为示例
        print("\n🏬 前5家门店信息示例:")
        for row in range(2, min(7, ws.max_row + 1)):
            store_info = []
            for col in range(1, ws.max_column + 1):
                value = ws.cell(row=row, column=col).value or ""
                store_info.append(str(value))
            print(f"   第{row-1}家: {' | '.join(store_info)}")
        
        # 检查数据完整性
        print("\n🔍 数据完整性检查:")
        empty_districts = 0
        empty_store_types = 0
        
        for row in range(2, ws.max_row + 1):
            district = ws.cell(row=row, column=3).value
            store_type = ws.cell(row=row, column=5).value
            
            if not district or district.strip() == '':
                empty_districts += 1
            if not store_type or store_type.strip() == '':
                empty_store_types += 1
        
        print(f"   区县信息缺失: {empty_districts}家")
        print(f"   业态类型缺失: {empty_store_types}家")
        
        if empty_districts == 0 and empty_store_types == 0:
            print("   ✅ 所有门店的区县和业态类型信息都已完整填充")
        else:
            print("   ⚠️  部分门店信息需要进一步完善")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证Excel文件时出错: {e}")
        return False

if __name__ == "__main__":
    print("🔍 正在验证增强版盒马门店信息Excel文件...")
    print("=" * 60)
    if verify_enhanced_hema_excel():
        print("=" * 60)
        print("✅ Excel文件验证完成！")
    else:
        print("=" * 60)
        print("❌ Excel文件验证失败！")
