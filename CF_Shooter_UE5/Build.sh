#!/bin/bash

echo "========================================"
echo "CF Shooter UE5 Build Script for Mac"
echo "========================================"

PROJECT_NAME="CFShooter"
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_FILE="$PROJECT_DIR/$PROJECT_NAME.uproject"

# 尝试找到虚幻引擎5的安装路径
UE5_PATHS=(
    "/Users/<USER>/Epic Games/UE_5.3/Engine/Binaries/DotNET/UnrealBuildTool/UnrealBuildTool"
    "/Applications/Epic Games/UE_5.3/Engine/Binaries/DotNET/UnrealBuildTool/UnrealBuildTool"
    "/Users/<USER>/Epic Games/UE_5.3/Engine/Binaries/DotNET/UnrealBuildTool/UnrealBuildTool"
    "/Users/<USER>/Epic Games/UE_5.4/Engine/Binaries/DotNET/UnrealBuildTool/UnrealBuildTool"
    "/Applications/Epic Games/UE_5.4/Engine/Binaries/DotNET/UnrealBuildTool/UnrealBuildTool"
)

UE5_PATH=""
for path in "${UE5_PATHS[@]}"; do
    if [ -f "$path" ]; then
        UE5_PATH="$path"
        break
    fi
done

if [ -z "$UE5_PATH" ]; then
    echo "ERROR: Unreal Engine 5 not found!"
    echo "Please install UE5 through Epic Games Launcher or update the paths in this script"
    echo "Searched paths:"
    for path in "${UE5_PATHS[@]}"; do
        echo "  - $path"
    done
    exit 1
fi

echo "Found Unreal Engine 5 at: $UE5_PATH"

# 检查项目文件
if [ ! -f "$PROJECT_FILE" ]; then
    echo "ERROR: Project file not found: $PROJECT_FILE"
    exit 1
fi

echo "Found project file: $PROJECT_FILE"

# 设置执行权限
chmod +x "$UE5_PATH"

echo ""
echo "========================================"
echo "Generating project files..."
echo "========================================"

# 生成Xcode项目文件
UE5_DIR="$(dirname "$UE5_PATH")"
UE5_ENGINE_DIR="$(dirname "$(dirname "$(dirname "$UE5_DIR")")")"
GENERATE_SCRIPT="$UE5_ENGINE_DIR/Binaries/DotNET/UnrealBuildTool/UnrealBuildTool"

if [ -f "$GENERATE_SCRIPT" ]; then
    "$GENERATE_SCRIPT" -projectfiles -project="$PROJECT_FILE" -game -rocket -progress
    
    if [ $? -ne 0 ]; then
        echo "ERROR: Failed to generate project files"
        exit 1
    fi
else
    echo "WARNING: Could not find project file generator, skipping..."
fi

echo ""
echo "========================================"
echo "Building $PROJECT_NAME for Development..."
echo "========================================"

"$UE5_PATH" $PROJECT_NAME Mac Development -project="$PROJECT_FILE" -rocket -progress

if [ $? -ne 0 ]; then
    echo ""
    echo "========================================"
    echo "BUILD FAILED!"
    echo "========================================"
    exit 1
fi

echo ""
echo "========================================"
echo "Building $PROJECT_NAME Editor..."
echo "========================================"

"$UE5_PATH" ${PROJECT_NAME}Editor Mac Development -project="$PROJECT_FILE" -rocket -progress

if [ $? -ne 0 ]; then
    echo ""
    echo "========================================"
    echo "EDITOR BUILD FAILED!"
    echo "========================================"
    exit 1
fi

echo ""
echo "========================================"
echo "ALL BUILDS SUCCESSFUL!"
echo "========================================"
echo ""
echo "You can now open the project in Unreal Engine 5"
echo "Project file: $PROJECT_FILE"
echo ""
echo "To open the project:"
echo "1. Open Epic Games Launcher"
echo "2. Go to Unreal Engine tab"
echo "3. Click 'Launch' on UE 5.3+"
echo "4. Browse and select: $PROJECT_FILE"
echo ""
