#!/bin/bash

echo "🎮 CF Shooter - 快速启动脚本"
echo "================================"

PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_FILE="$PROJECT_DIR/CF_Shooter.uproject"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查系统
echo -e "${BLUE}检查系统环境...${NC}"

# 检查macOS版本
MACOS_VERSION=$(sw_vers -productVersion)
echo "macOS版本: $MACOS_VERSION"

# 检查Xcode
if command -v xcode-select &> /dev/null; then
    XCODE_PATH=$(xcode-select -p)
    echo -e "${GREEN}✓ Xcode已安装: $XCODE_PATH${NC}"
else
    echo -e "${RED}✗ Xcode未安装${NC}"
    echo "请运行: xcode-select --install"
    exit 1
fi

# 查找虚幻引擎5
echo -e "${BLUE}查找虚幻引擎5...${NC}"

UE5_PATHS=(
    "/Users/<USER>/Epic Games/UE_5.3/Engine/Binaries/Mac/UnrealEditor.app"
    "/Applications/Epic Games/UE_5.3/Engine/Binaries/Mac/UnrealEditor.app"
    "/Users/<USER>/Epic Games/UE_5.3/Engine/Binaries/Mac/UnrealEditor.app"
    "/Users/<USER>/Epic Games/UE_5.4/Engine/Binaries/Mac/UnrealEditor.app"
    "/Applications/Epic Games/UE_5.4/Engine/Binaries/Mac/UnrealEditor.app"
    "/Users/<USER>/Epic Games/UE_5.4/Engine/Binaries/Mac/UnrealEditor.app"
)

UE5_EDITOR=""
for path in "${UE5_PATHS[@]}"; do
    if [ -d "$path" ]; then
        UE5_EDITOR="$path/Contents/MacOS/UnrealEditor"
        echo -e "${GREEN}✓ 找到虚幻引擎5: $path${NC}"
        break
    fi
done

if [ -z "$UE5_EDITOR" ]; then
    echo -e "${RED}✗ 未找到虚幻引擎5${NC}"
    echo "请通过Epic Games Launcher安装虚幻引擎5.3或更高版本"
    echo ""
    echo "安装步骤："
    echo "1. 下载Epic Games Launcher: https://www.epicgames.com/store/download"
    echo "2. 登录并安装虚幻引擎5.3+"
    exit 1
fi

# 检查项目文件
if [ ! -f "$PROJECT_FILE" ]; then
    echo -e "${RED}✗ 项目文件未找到: $PROJECT_FILE${NC}"
    exit 1
fi

echo -e "${GREEN}✓ 项目文件已找到${NC}"

# 显示菜单
echo ""
echo "请选择操作："
echo "1) 🔨 构建项目"
echo "2) 🚀 启动编辑器"
echo "3) 🔧 构建并启动"
echo "4) 📋 检查项目状态"
echo "5) 🧹 清理项目"
echo "6) ❌ 退出"
echo ""

read -p "请输入选择 (1-6): " choice

case $choice in
    1)
        echo -e "${YELLOW}开始构建项目...${NC}"
        if [ -f "$PROJECT_DIR/Build.sh" ]; then
            chmod +x "$PROJECT_DIR/Build.sh"
            "$PROJECT_DIR/Build.sh"
        else
            echo -e "${RED}构建脚本未找到${NC}"
            exit 1
        fi
        ;;
    2)
        echo -e "${YELLOW}启动虚幻编辑器...${NC}"
        "$UE5_EDITOR" "$PROJECT_FILE" &
        echo -e "${GREEN}编辑器已启动${NC}"
        ;;
    3)
        echo -e "${YELLOW}构建并启动项目...${NC}"
        if [ -f "$PROJECT_DIR/Build.sh" ]; then
            chmod +x "$PROJECT_DIR/Build.sh"
            if "$PROJECT_DIR/Build.sh"; then
                echo -e "${GREEN}构建成功，启动编辑器...${NC}"
                "$UE5_EDITOR" "$PROJECT_FILE" &
            else
                echo -e "${RED}构建失败${NC}"
                exit 1
            fi
        else
            echo -e "${RED}构建脚本未找到${NC}"
            exit 1
        fi
        ;;
    4)
        echo -e "${YELLOW}检查项目状态...${NC}"
        echo ""
        echo "项目信息："
        echo "- 项目文件: $PROJECT_FILE"
        echo "- 虚幻引擎: $UE5_EDITOR"
        echo ""
        
        if [ -d "$PROJECT_DIR/Binaries" ]; then
            echo -e "${GREEN}✓ Binaries目录存在${NC}"
            ls -la "$PROJECT_DIR/Binaries/"
        else
            echo -e "${YELLOW}⚠ Binaries目录不存在（项目可能未构建）${NC}"
        fi
        
        if [ -d "$PROJECT_DIR/Intermediate" ]; then
            echo -e "${GREEN}✓ Intermediate目录存在${NC}"
        else
            echo -e "${YELLOW}⚠ Intermediate目录不存在${NC}"
        fi
        
        echo ""
        echo "源代码文件："
        find "$PROJECT_DIR/Source" -name "*.h" -o -name "*.cpp" | head -10
        ;;
    5)
        echo -e "${YELLOW}清理项目...${NC}"
        read -p "确定要清理项目吗？这将删除编译文件 (y/N): " confirm
        if [[ $confirm == [yY] || $confirm == [yY][eE][sS] ]]; then
            rm -rf "$PROJECT_DIR/Binaries"
            rm -rf "$PROJECT_DIR/Intermediate"
            rm -rf "$PROJECT_DIR/.vs"
            rm -f "$PROJECT_DIR"/*.xcworkspace
            echo -e "${GREEN}项目已清理${NC}"
        else
            echo "取消清理"
        fi
        ;;
    6)
        echo "再见！"
        exit 0
        ;;
    *)
        echo -e "${RED}无效选择${NC}"
        exit 1
        ;;
esac

echo ""
echo -e "${GREEN}操作完成！${NC}"

# 显示有用的信息
echo ""
echo "💡 有用的提示："
echo "- 项目文档: README.md"
echo "- Mac设置指南: MAC_SETUP_GUIDE.md"
echo "- 开发指南: DEVELOPMENT_GUIDE.md"
echo "- 如需帮助，请查看日志: ~/Library/Logs/Unreal Engine/"
