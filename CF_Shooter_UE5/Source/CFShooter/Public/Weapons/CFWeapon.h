#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Components/SkeletalMeshComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Engine/DataTable.h"
#include "CFWeapon.generated.h"

class UAnimMontage;
class USoundBase;
class UNiagaraSystem;
class UNiagaraComponent;
class <PERSON><PERSON><PERSON>erCharacter;

UENUM(BlueprintType)
enum class EWeaponType : uint8
{
	AssaultRifle,
	SniperRifle,
	Shotgun,
	SMG,
	Pistol,
	LMG,
	Grenade
};

UENUM(BlueprintType)
enum class EFireMode : uint8
{
	Single,
	Burst,
	FullAuto
};

UENUM(BlueprintType)
enum class EWeaponState : uint8
{
	Idle,
	Firing,
	Reloading,
	Equipping,
	Unequipping
};

USTRUCT(BlueprintType)
struct FWeaponStats : public FTableRowBase
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString WeaponName;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EWeaponType WeaponType;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EFireMode FireMode;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float Damage;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float FireRate; // Rounds per minute

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int32 MagazineSize;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int32 MaxAmmo;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float ReloadTime;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float Range;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float Accuracy;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float RecoilVertical;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float RecoilHorizontal;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int32 BulletsPerShot; // For shotguns

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float SpreadAngle;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	USkeletalMesh* WeaponMesh;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	UStaticMesh* WorldMesh;

	FWeaponStats()
	{
		WeaponName = TEXT("Default Weapon");
		WeaponType = EWeaponType::AssaultRifle;
		FireMode = EFireMode::FullAuto;
		Damage = 30.0f;
		FireRate = 600.0f;
		MagazineSize = 30;
		MaxAmmo = 120;
		ReloadTime = 2.5f;
		Range = 1000.0f;
		Accuracy = 0.95f;
		RecoilVertical = 2.0f;
		RecoilHorizontal = 1.0f;
		BulletsPerShot = 1;
		SpreadAngle = 1.0f;
		WeaponMesh = nullptr;
		WorldMesh = nullptr;
	}
};

UCLASS()
class CFSHOOTER_API ACFWeapon : public AActor
{
	GENERATED_BODY()

public:
	ACFWeapon();

protected:
	virtual void BeginPlay() override;

	// Components
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	USkeletalMeshComponent* WeaponMesh;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	UStaticMeshComponent* WorldMesh;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	UNiagaraComponent* MuzzleFlashComponent;

	// Weapon Data
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon")
	FWeaponStats WeaponStats;

	UPROPERTY(BlueprintReadOnly, Category = "Weapon")
	EWeaponState CurrentState;

	UPROPERTY(BlueprintReadOnly, Category = "Weapon")
	int32 CurrentAmmo;

	UPROPERTY(BlueprintReadOnly, Category = "Weapon")
	int32 TotalAmmo;

	UPROPERTY(BlueprintReadOnly, Category = "Weapon")
	bool bCanFire;

	UPROPERTY(BlueprintReadOnly, Category = "Weapon")
	ACFShooterCharacter* OwnerCharacter;

	// Effects and Sounds
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
	UNiagaraSystem* MuzzleFlashEffect;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
	UNiagaraSystem* BulletTrailEffect;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
	UNiagaraSystem* ImpactEffect;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
	USoundBase* FireSound;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
	USoundBase* ReloadSound;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
	USoundBase* EmptySound;

	// Animations
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
	UAnimMontage* FireAnimation;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
	UAnimMontage* ReloadAnimation;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
	UAnimMontage* EquipAnimation;

public:
	virtual void Tick(float DeltaTime) override;

	// Weapon Functions
	UFUNCTION(BlueprintCallable, Category = "Weapon")
	void Fire();

	UFUNCTION(BlueprintCallable, Category = "Weapon")
	void StartFire();

	UFUNCTION(BlueprintCallable, Category = "Weapon")
	void StopFire();

	UFUNCTION(BlueprintCallable, Category = "Weapon")
	void Reload();

	UFUNCTION(BlueprintCallable, Category = "Weapon")
	void Equip(ACFShooterCharacter* NewOwner);

	UFUNCTION(BlueprintCallable, Category = "Weapon")
	void Unequip();

	// State Functions
	UFUNCTION(BlueprintCallable, Category = "Weapon")
	void SetWeaponState(EWeaponState NewState);

	UFUNCTION(BlueprintPure, Category = "Weapon")
	EWeaponState GetWeaponState() const { return CurrentState; }

	UFUNCTION(BlueprintPure, Category = "Weapon")
	bool CanFire() const;

	UFUNCTION(BlueprintPure, Category = "Weapon")
	bool NeedsReload() const;

	UFUNCTION(BlueprintPure, Category = "Weapon")
	float GetAmmoPercentage() const;

	// Getters
	UFUNCTION(BlueprintPure, Category = "Weapon")
	FWeaponStats GetWeaponStats() const { return WeaponStats; }

	UFUNCTION(BlueprintPure, Category = "Weapon")
	int32 GetCurrentAmmo() const { return CurrentAmmo; }

	UFUNCTION(BlueprintPure, Category = "Weapon")
	int32 GetTotalAmmo() const { return TotalAmmo; }

	// Setters
	UFUNCTION(BlueprintCallable, Category = "Weapon")
	void SetWeaponStats(const FWeaponStats& NewStats);

	UFUNCTION(BlueprintCallable, Category = "Weapon")
	void AddAmmo(int32 Amount);

private:
	FTimerHandle FireTimerHandle;
	FTimerHandle ReloadTimerHandle;
	int32 BurstShotsFired;
	
	void PerformFire();
	void FinishReload();
	void PlayFireEffects();
	void PlayReloadEffects();
	void PerformLineTrace();
	void ApplyRecoil();
	FVector CalculateFireDirection();
	void HandleBurstFire();
};
