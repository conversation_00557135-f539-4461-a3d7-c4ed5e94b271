#pragma once

#include "CoreMinimal.h"
#include "GameFramework/GameModeBase.h"
#include "Engine/DataTable.h"
#include "CFShooterGameMode.generated.h"

class ACFShooter<PERSON>haracter;

UENUM(BlueprintType)
enum class EGameState : uint8
{
	WaitingToStart,
	InProgress,
	WaitingPostMatch,
	LeavingMap
};

UENUM(BlueprintType)
enum class ETeam : uint8
{
	None,
	TeamA,
	TeamB
};

USTRUCT(BlueprintType)
struct FPlayerStats
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadWrite)
	int32 Kills;

	UPROPERTY(BlueprintReadWrite)
	int32 Deaths;

	UPROPERTY(BlueprintReadWrite)
	int32 Assists;

	UPROPERTY(BlueprintReadWrite)
	int32 Score;

	FPlayerStats()
	{
		Kills = 0;
		Deaths = 0;
		Assists = 0;
		Score = 0;
	}
};

USTRUCT(BlueprintType)
struct FTeamStats
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadWrite)
	int32 TeamScore;

	UPROPERTY(BlueprintReadWrite)
	int32 RoundsWon;

	UPROPERTY(BlueprintReadWrite)
	TArray<APlayerController*> TeamMembers;

	FTeamStats()
	{
		TeamScore = 0;
		RoundsWon = 0;
	}
};

UCLASS()
class CFSHOOTER_API ACFShooterGameMode : public AGameModeBase
{
	GENERATED_BODY()

public:
	ACFShooterGameMode();

protected:
	virtual void BeginPlay() override;
	virtual void PostLogin(APlayerController* NewPlayer) override;
	virtual void Logout(AController* Exiting) override;

	// Game State
	UPROPERTY(BlueprintReadOnly, Category = "Game State")
	EGameState CurrentGameState;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Game Settings")
	float MatchDuration;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Game Settings")
	int32 MaxPlayers;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Game Settings")
	int32 MinPlayersToStart;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Game Settings")
	int32 ScoreToWin;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Game Settings")
	float RespawnDelay;

	// Team Management
	UPROPERTY(BlueprintReadOnly, Category = "Teams")
	FTeamStats TeamAStats;

	UPROPERTY(BlueprintReadOnly, Category = "Teams")
	FTeamStats TeamBStats;

	// Player Management
	UPROPERTY(BlueprintReadOnly, Category = "Players")
	TMap<APlayerController*, FPlayerStats> PlayerStatsMap;

	UPROPERTY(BlueprintReadOnly, Category = "Players")
	TMap<APlayerController*, ETeam> PlayerTeamMap;

	// Spawn Points
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Points")
	TArray<AActor*> TeamASpawnPoints;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Points")
	TArray<AActor*> TeamBSpawnPoints;

public:
	virtual void Tick(float DeltaTime) override;

	// Game Flow Functions
	UFUNCTION(BlueprintCallable, Category = "Game Flow")
	void StartMatch();

	UFUNCTION(BlueprintCallable, Category = "Game Flow")
	void EndMatch();

	UFUNCTION(BlueprintCallable, Category = "Game Flow")
	void RestartMatch();

	// Player Management
	UFUNCTION(BlueprintCallable, Category = "Player Management")
	void AssignPlayerToTeam(APlayerController* Player, ETeam Team);

	UFUNCTION(BlueprintCallable, Category = "Player Management")
	ETeam GetPlayerTeam(APlayerController* Player);

	UFUNCTION(BlueprintCallable, Category = "Player Management")
	void RespawnPlayer(APlayerController* Player);

	// Scoring System
	UFUNCTION(BlueprintCallable, Category = "Scoring")
	void OnPlayerKilled(APlayerController* Killer, APlayerController* Victim);

	UFUNCTION(BlueprintCallable, Category = "Scoring")
	void AddScore(APlayerController* Player, int32 Points);

	UFUNCTION(BlueprintCallable, Category = "Scoring")
	FPlayerStats GetPlayerStats(APlayerController* Player);

	// Team Functions
	UFUNCTION(BlueprintCallable, Category = "Teams")
	int32 GetTeamPlayerCount(ETeam Team);

	UFUNCTION(BlueprintCallable, Category = "Teams")
	ETeam GetTeamWithFewerPlayers();

	// Spawn Functions
	UFUNCTION(BlueprintCallable, Category = "Spawning")
	FTransform GetSpawnTransform(ETeam Team);

	// Getters
	UFUNCTION(BlueprintPure, Category = "Game State")
	EGameState GetCurrentGameState() const { return CurrentGameState; }

	UFUNCTION(BlueprintPure, Category = "Game State")
	float GetMatchTimeRemaining() const;

	UFUNCTION(BlueprintPure, Category = "Teams")
	FTeamStats GetTeamStats(ETeam Team) const;

private:
	float MatchStartTime;
	FTimerHandle MatchTimerHandle;
	FTimerHandle RespawnTimerHandle;

	void CheckMatchEnd();
	void HandleMatchTimeout();
	ETeam DetermineWinningTeam();
	void BalanceTeams();
};
