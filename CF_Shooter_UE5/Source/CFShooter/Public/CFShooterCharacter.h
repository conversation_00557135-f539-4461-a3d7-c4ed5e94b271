#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Character.h"
#include "InputActionValue.h"
#include "Components/TimelineComponent.h"
#include "Engine/DataTable.h"
#include "CFShooterCharacter.generated.h"

class UInputMappingContext;
class UInputAction;
class UCameraComponent;
class USpringArmComponent;
class UStaticMeshComponent;
class USkeletalMeshComponent;
class UAnimMontage;
class USoundBase;
class UParticleSystem;
class UNiagaraSystem;
class UNiagaraComponent;

USTRUCT(BlueprintType)
struct FWeaponData : public FTableRowBase
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString WeaponName;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int32 Damage;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float FireRate;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int32 MagazineSize;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float ReloadTime;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float Range;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float Accuracy;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	USkeletalMesh* WeaponMesh;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	UAnimMontage* FireAnimation;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	UAnimMontage* ReloadAnimation;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	USoundBase* FireSound;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	USoundBase* ReloadSound;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	UNiagaraSystem* MuzzleFlash;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	UNiagaraSystem* BulletTrail;
};

UENUM(BlueprintType)
enum class EMovementState : uint8
{
	Idle,
	Walking,
	Running,
	Crouching,
	Prone,
	Jumping,
	Aiming
};

UCLASS()
class CFSHOOTER_API ACFShooterCharacter : public ACharacter
{
	GENERATED_BODY()

public:
	ACFShooterCharacter();

protected:
	virtual void BeginPlay() override;

	// Input
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = Input)
	UInputMappingContext* DefaultMappingContext;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = Input)
	UInputAction* JumpAction;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = Input)
	UInputAction* MoveAction;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = Input)
	UInputAction* LookAction;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = Input)
	UInputAction* FireAction;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = Input)
	UInputAction* AimAction;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = Input)
	UInputAction* ReloadAction;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = Input)
	UInputAction* CrouchAction;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = Input)
	UInputAction* SprintAction;

	// Camera
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = Camera)
	UCameraComponent* FirstPersonCamera;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = Camera)
	USpringArmComponent* ThirdPersonSpringArm;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = Camera)
	UCameraComponent* ThirdPersonCamera;

	// Weapon System
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = Weapon)
	USkeletalMeshComponent* WeaponMesh;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = Weapon)
	UDataTable* WeaponDataTable;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = Weapon)
	FName CurrentWeaponID;

	UPROPERTY(BlueprintReadOnly, Category = Weapon)
	FWeaponData CurrentWeaponData;

	UPROPERTY(BlueprintReadOnly, Category = Weapon)
	int32 CurrentAmmo;

	UPROPERTY(BlueprintReadOnly, Category = Weapon)
	int32 TotalAmmo;

	UPROPERTY(BlueprintReadOnly, Category = Weapon)
	bool bIsReloading;

	UPROPERTY(BlueprintReadOnly, Category = Weapon)
	bool bCanFire;

	// Movement States
	UPROPERTY(BlueprintReadOnly, Category = Movement)
	EMovementState CurrentMovementState;

	UPROPERTY(BlueprintReadOnly, Category = Movement)
	bool bIsAiming;

	UPROPERTY(BlueprintReadOnly, Category = Movement)
	bool bIsSprinting;

	// Health System
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = Health)
	float MaxHealth;

	UPROPERTY(BlueprintReadOnly, Category = Health)
	float CurrentHealth;

	UPROPERTY(BlueprintReadOnly, Category = Health)
	bool bIsDead;

	// Effects
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = Effects)
	UNiagaraComponent* MuzzleFlashComponent;

public:
	virtual void Tick(float DeltaTime) override;
	virtual void SetupPlayerInputComponent(class UInputComponent* PlayerInputComponent) override;

	// Input Functions
	void Move(const FInputActionValue& Value);
	void Look(const FInputActionValue& Value);
	void StartFire();
	void StopFire();
	void StartAim();
	void StopAim();
	void Reload();
	void StartCrouch();
	void StopCrouch();
	void StartSprint();
	void StopSprint();

	// Weapon Functions
	UFUNCTION(BlueprintCallable, Category = Weapon)
	void Fire();

	UFUNCTION(BlueprintCallable, Category = Weapon)
	void PerformReload();

	UFUNCTION(BlueprintCallable, Category = Weapon)
	void SwitchWeapon(FName WeaponID);

	// Health Functions
	UFUNCTION(BlueprintCallable, Category = Health)
	void TakeDamage(float DamageAmount);

	UFUNCTION(BlueprintCallable, Category = Health)
	void Die();

	// Getters
	UFUNCTION(BlueprintPure, Category = Weapon)
	float GetCurrentAmmoPercentage() const;

	UFUNCTION(BlueprintPure, Category = Health)
	float GetHealthPercentage() const;

private:
	FTimerHandle FireTimerHandle;
	FTimerHandle ReloadTimerHandle;
	
	void UpdateMovementState();
	void HandleRecoil();
	void PlayFireEffects();
	void PerformLineTrace();
};
