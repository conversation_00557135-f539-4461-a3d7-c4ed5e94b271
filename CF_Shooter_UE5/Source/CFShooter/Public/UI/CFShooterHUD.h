#pragma once

#include "CoreMinimal.h"
#include "GameFramework/HUD.h"
#include "Engine/Canvas.h"
#include "CFShooterHUD.generated.h"

class UUserWidget;
class <PERSON><PERSON>hooter<PERSON><PERSON>cter;
class AC<PERSON>hooter<PERSON>ameMode;

UCLASS()
class CFSHOOTER_API ACFShooterHUD : public AHUD
{
	GENERATED_BODY()

public:
	ACFShooterHUD();

protected:
	virtual void BeginPlay() override;
	virtual void DrawHUD() override;

	// Widget Classes
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI")
	TSubclassOf<UUserWidget> MainHUDWidgetClass;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI")
	TSubclassOf<UUserWidget> ScoreboardWidgetClass;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI")
	TSubclassOf<UUserWidget> PauseMenuWidgetClass;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI")
	TSubclassOf<UUserWidget> DeathScreenWidgetClass;

	// Widget Instances
	UPROPERTY(BlueprintReadOnly, Category = "UI")
	UUserWidget* MainHUDWidget;

	UPROPERTY(BlueprintReadOnly, Category = "UI")
	UUserWidget* ScoreboardWidget;

	UPROPERTY(BlueprintReadOnly, Category = "UI")
	UUserWidget* PauseMenuWidget;

	UPROPERTY(BlueprintReadOnly, Category = "UI")
	UUserWidget* DeathScreenWidget;

	// Crosshair Settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Crosshair")
	UTexture2D* CrosshairTexture;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Crosshair")
	float CrosshairSize;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Crosshair")
	FLinearColor CrosshairColor;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Crosshair")
	bool bShowCrosshair;

	// Hit Marker Settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hit Marker")
	UTexture2D* HitMarkerTexture;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hit Marker")
	float HitMarkerSize;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hit Marker")
	float HitMarkerDuration;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hit Marker")
	FLinearColor HitMarkerColor;

public:
	// Widget Management
	UFUNCTION(BlueprintCallable, Category = "UI")
	void ShowMainHUD();

	UFUNCTION(BlueprintCallable, Category = "UI")
	void HideMainHUD();

	UFUNCTION(BlueprintCallable, Category = "UI")
	void ShowScoreboard();

	UFUNCTION(BlueprintCallable, Category = "UI")
	void HideScoreboard();

	UFUNCTION(BlueprintCallable, Category = "UI")
	void ShowPauseMenu();

	UFUNCTION(BlueprintCallable, Category = "UI")
	void HidePauseMenu();

	UFUNCTION(BlueprintCallable, Category = "UI")
	void ShowDeathScreen();

	UFUNCTION(BlueprintCallable, Category = "UI")
	void HideDeathScreen();

	// HUD Elements
	UFUNCTION(BlueprintCallable, Category = "UI")
	void ShowHitMarker();

	UFUNCTION(BlueprintCallable, Category = "UI")
	void SetCrosshairVisible(bool bVisible);

	// Getters
	UFUNCTION(BlueprintPure, Category = "UI")
	UUserWidget* GetMainHUDWidget() const { return MainHUDWidget; }

	UFUNCTION(BlueprintPure, Category = "UI")
	bool IsScoreboardVisible() const;

	UFUNCTION(BlueprintPure, Category = "UI")
	bool IsPauseMenuVisible() const;

private:
	float HitMarkerStartTime;
	bool bShowHitMarker;

	void DrawCrosshair();
	void DrawHitMarker();
	void CreateWidgets();
};
