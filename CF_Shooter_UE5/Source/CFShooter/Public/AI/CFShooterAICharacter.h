#pragma once

#include "CoreMinimal.h"
#include "CFShooterCharacter.h"
#include "AIController.h"
#include "BehaviorTree/BehaviorTreeComponent.h"
#include "BehaviorTree/BlackboardComponent.h"
#include "Perception/AIPerceptionComponent.h"
#include "Perception/AISightConfig.h"
#include "Perception/AIHearingConfig.h"
#include "CFShooterAICharacter.generated.h"

class UBehaviorTree;
class UBlackboardComponent;
class UAIPerceptionComponent;

UENUM(BlueprintType)
enum class EAIState : uint8
{
	Idle,
	Patrolling,
	Investigating,
	Chasing,
	Attacking,
	Searching,
	Dead
};

UCLASS()
class CFSHOOTER_API ACFShooterAICharacter : public ACFShooter<PERSON>haracter
{
	GENERATED_BODY()

public:
	ACFShooterAICharacter();

protected:
	virtual void BeginPlay() override;

	// AI Behavior
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI")
	UBehaviorTree* BehaviorTree;

	UPROPERTY(BlueprintReadOnly, Category = "AI")
	EAIState CurrentAIState;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI")
	float PatrolRadius;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI")
	float AttackRange;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI")
	float SightRange;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI")
	float HearingRange;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI")
	float AlertTime;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI")
	TArray<FVector> PatrolPoints;

	// Combat
	UPROPERTY(BlueprintReadOnly, Category = "Combat")
	AActor* CurrentTarget;

	UPROPERTY(BlueprintReadOnly, Category = "Combat")
	FVector LastKnownTargetLocation;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
	float AccuracyModifier;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
	float ReactionTime;

public:
	virtual void Tick(float DeltaTime) override;

	// AI Functions
	UFUNCTION(BlueprintCallable, Category = "AI")
	void SetAIState(EAIState NewState);

	UFUNCTION(BlueprintPure, Category = "AI")
	EAIState GetAIState() const { return CurrentAIState; }

	UFUNCTION(BlueprintCallable, Category = "AI")
	void SetTarget(AActor* NewTarget);

	UFUNCTION(BlueprintPure, Category = "AI")
	AActor* GetCurrentTarget() const { return CurrentTarget; }

	UFUNCTION(BlueprintCallable, Category = "AI")
	bool CanSeeTarget() const;

	UFUNCTION(BlueprintCallable, Category = "AI")
	bool IsInAttackRange() const;

	UFUNCTION(BlueprintCallable, Category = "AI")
	void StartPatrol();

	UFUNCTION(BlueprintCallable, Category = "AI")
	void StopPatrol();

	UFUNCTION(BlueprintCallable, Category = "AI")
	FVector GetNextPatrolPoint();

	// Combat Functions
	UFUNCTION(BlueprintCallable, Category = "Combat")
	void StartAttacking();

	UFUNCTION(BlueprintCallable, Category = "Combat")
	void StopAttacking();

	UFUNCTION(BlueprintCallable, Category = "Combat")
	void AIFire();

	// Override from parent
	virtual void TakeDamage(float DamageAmount) override;
	virtual void Die() override;

private:
	int32 CurrentPatrolIndex;
	FTimerHandle AttackTimerHandle;
	FTimerHandle AlertTimerHandle;
	
	void UpdateAIBehavior();
	void HandleCombat();
	void HandlePatrol();
	void HandleInvestigation();
	void HandleSearch();
	
	bool LineOfSightCheck(AActor* Target) const;
	float CalculateAccuracy() const;
};

UCLASS()
class CFSHOOTER_API ACFShooterAIController : public AAIController
{
	GENERATED_BODY()

public:
	ACFShooterAIController();

protected:
	virtual void BeginPlay() override;
	virtual void OnPossess(APawn* InPawn) override;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AI")
	UBehaviorTreeComponent* BehaviorTreeComponent;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AI")
	UBlackboardComponent* BlackboardComponent;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AI")
	UAIPerceptionComponent* AIPerceptionComponent;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AI")
	UAISightConfig* SightConfig;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AI")
	UAIHearingConfig* HearingConfig;

public:
	// Blackboard Keys
	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AI")
	FName TargetActorKey;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AI")
	FName LastKnownLocationKey;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AI")
	FName AIStateKey;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AI")
	FName PatrolPointKey;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AI")
	FName CanSeeTargetKey;

	// Perception Functions
	UFUNCTION()
	void OnPerceptionUpdated(const TArray<AActor*>& UpdatedActors);

	UFUNCTION()
	void OnTargetPerceptionUpdated(AActor* Actor, FAIStimulus Stimulus);

	// Utility Functions
	UFUNCTION(BlueprintCallable, Category = "AI")
	void SetBlackboardTarget(AActor* Target);

	UFUNCTION(BlueprintCallable, Category = "AI")
	void SetBlackboardLocation(FVector Location);

	UFUNCTION(BlueprintCallable, Category = "AI")
	void SetBlackboardAIState(EAIState State);

private:
	void SetupPerception();
	void HandleSightStimulus(AActor* Actor, FAIStimulus Stimulus);
	void HandleHearingStimulus(AActor* Actor, FAIStimulus Stimulus);
};
