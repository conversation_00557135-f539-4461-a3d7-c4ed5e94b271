using UnrealBuildTool;

public class CFShooter : ModuleRules
{
	public CFShooter(ReadOnlyTargetRules Target) : base(Target)
	{
		PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;

		PublicDependencyModuleNames.AddRange(new string[] { 
			"Core", 
			"CoreUObject", 
			"Engine", 
			"InputCore",
			"UMG",
			"Slate",
			"SlateCore",
			"AIModule",
			"GameplayTasks",
			"NavigationSystem",
			"PhysicsCore",
			"Niagara",
			"AudioMixer",
			"OnlineSubsystem",
			"OnlineSubsystemUtils",
			"GameplayAbilities",
			"GameplayTags",
			"GameplayTasks",
			"MotionWarping",
			"ControlRig"
		});

		PrivateDependencyModuleNames.AddRange(new string[] {
			"EnhancedInput",
			"NetCore",
			"Networking",
			"Sockets"
		});

		// Uncomment if you are using online features
		DynamicallyLoadedModuleNames.Add("OnlineSubsystemSteam");
	}
}
