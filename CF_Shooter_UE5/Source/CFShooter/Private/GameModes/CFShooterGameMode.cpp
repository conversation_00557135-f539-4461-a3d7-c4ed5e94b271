#include "GameModes/CFShooterGameMode.h"
#include "CFShooterCharacter.h"
#include "GameFramework/PlayerController.h"
#include "GameFramework/PlayerStart.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/World.h"
#include "TimerManager.h"

ACFShooterGameMode::ACFShooterGameMode()
{
	// Set default pawn class to our character
	DefaultPawnClass = ACFShooterCharacter::StaticClass();

	// Initialize game settings
	MatchDuration = 600.0f; // 10 minutes
	MaxPlayers = 16;
	MinPlayersToStart = 2;
	ScoreToWin = 50;
	RespawnDelay = 5.0f;
	
	CurrentGameState = EGameState::WaitingToStart;
	MatchStartTime = 0.0f;

	// Enable tick
	PrimaryActorTick.bCanEverTick = true;
}

void ACFShooterGameMode::BeginPlay()
{
	Super::BeginPlay();

	// Find spawn points
	TArray<AActor*> FoundActors;
	UGameplayStatics::GetAllActorsOfClass(GetWorld(), APlayerStart::StaticClass(), FoundActors);

	for (AActor* Actor : FoundActors)
	{
		APlayerStart* SpawnPoint = Cast<APlayerStart>(Actor);
		if (SpawnPoint)
		{
			FString SpawnTag = SpawnPoint->PlayerStartTag.ToString();
			if (SpawnTag.Contains(TEXT("TeamA")))
			{
				TeamASpawnPoints.Add(SpawnPoint);
			}
			else if (SpawnTag.Contains(TEXT("TeamB")))
			{
				TeamBSpawnPoints.Add(SpawnPoint);
			}
		}
	}
}

void ACFShooterGameMode::PostLogin(APlayerController* NewPlayer)
{
	Super::PostLogin(NewPlayer);

	if (NewPlayer)
	{
		// Assign player to team with fewer players
		ETeam AssignedTeam = GetTeamWithFewerPlayers();
		AssignPlayerToTeam(NewPlayer, AssignedTeam);

		// Initialize player stats
		FPlayerStats NewStats;
		PlayerStatsMap.Add(NewPlayer, NewStats);

		// Check if we can start the match
		if (CurrentGameState == EGameState::WaitingToStart)
		{
			int32 TotalPlayers = GetNumPlayers();
			if (TotalPlayers >= MinPlayersToStart)
			{
				StartMatch();
			}
		}
	}
}

void ACFShooterGameMode::Logout(AController* Exiting)
{
	if (APlayerController* PC = Cast<APlayerController>(Exiting))
	{
		// Remove player from team
		if (PlayerTeamMap.Contains(PC))
		{
			ETeam PlayerTeam = PlayerTeamMap[PC];
			if (PlayerTeam == ETeam::TeamA)
			{
				TeamAStats.TeamMembers.Remove(PC);
			}
			else if (PlayerTeam == ETeam::TeamB)
			{
				TeamBStats.TeamMembers.Remove(PC);
			}
			PlayerTeamMap.Remove(PC);
		}

		// Remove player stats
		PlayerStatsMap.Remove(PC);
	}

	Super::Logout(Exiting);
}

void ACFShooterGameMode::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

	if (CurrentGameState == EGameState::InProgress)
	{
		CheckMatchEnd();
	}
}

void ACFShooterGameMode::StartMatch()
{
	if (CurrentGameState != EGameState::WaitingToStart) return;

	CurrentGameState = EGameState::InProgress;
	MatchStartTime = GetWorld()->GetTimeSeconds();

	// Set match timer
	GetWorldTimerManager().SetTimer(MatchTimerHandle, this, &ACFShooterGameMode::HandleMatchTimeout, MatchDuration, false);

	// Respawn all players
	for (auto& PlayerPair : PlayerStatsMap)
	{
		RespawnPlayer(PlayerPair.Key);
	}

	// Broadcast match start event
	// You can add delegate broadcasts here for UI updates
}

void ACFShooterGameMode::EndMatch()
{
	if (CurrentGameState != EGameState::InProgress) return;

	CurrentGameState = EGameState::WaitingPostMatch;
	GetWorldTimerManager().ClearTimer(MatchTimerHandle);

	// Determine winner
	ETeam WinningTeam = DetermineWinningTeam();

	// Handle post-match logic
	// You can add victory screens, statistics display, etc.
}

void ACFShooterGameMode::RestartMatch()
{
	// Reset all stats
	for (auto& PlayerPair : PlayerStatsMap)
	{
		PlayerPair.Value = FPlayerStats();
	}

	TeamAStats.TeamScore = 0;
	TeamAStats.RoundsWon = 0;
	TeamBStats.TeamScore = 0;
	TeamBStats.RoundsWon = 0;

	CurrentGameState = EGameState::WaitingToStart;
	
	// Check if we can start immediately
	if (GetNumPlayers() >= MinPlayersToStart)
	{
		StartMatch();
	}
}

void ACFShooterGameMode::AssignPlayerToTeam(APlayerController* Player, ETeam Team)
{
	if (!Player) return;

	// Remove from previous team if assigned
	if (PlayerTeamMap.Contains(Player))
	{
		ETeam OldTeam = PlayerTeamMap[Player];
		if (OldTeam == ETeam::TeamA)
		{
			TeamAStats.TeamMembers.Remove(Player);
		}
		else if (OldTeam == ETeam::TeamB)
		{
			TeamBStats.TeamMembers.Remove(Player);
		}
	}

	// Assign to new team
	PlayerTeamMap.Add(Player, Team);
	if (Team == ETeam::TeamA)
	{
		TeamAStats.TeamMembers.Add(Player);
	}
	else if (Team == ETeam::TeamB)
	{
		TeamBStats.TeamMembers.Add(Player);
	}
}

ETeam ACFShooterGameMode::GetPlayerTeam(APlayerController* Player)
{
	if (PlayerTeamMap.Contains(Player))
	{
		return PlayerTeamMap[Player];
	}
	return ETeam::None;
}

void ACFShooterGameMode::RespawnPlayer(APlayerController* Player)
{
	if (!Player) return;

	ETeam PlayerTeam = GetPlayerTeam(Player);
	FTransform SpawnTransform = GetSpawnTransform(PlayerTeam);

	// Respawn the player
	RestartPlayerAtTransform(Player, SpawnTransform);
}

void ACFShooterGameMode::OnPlayerKilled(APlayerController* Killer, APlayerController* Victim)
{
	if (Victim && PlayerStatsMap.Contains(Victim))
	{
		PlayerStatsMap[Victim].Deaths++;
	}

	if (Killer && PlayerStatsMap.Contains(Killer) && Killer != Victim)
	{
		PlayerStatsMap[Killer].Kills++;
		AddScore(Killer, 10); // 10 points per kill

		// Add team score
		ETeam KillerTeam = GetPlayerTeam(Killer);
		if (KillerTeam == ETeam::TeamA)
		{
			TeamAStats.TeamScore++;
		}
		else if (KillerTeam == ETeam::TeamB)
		{
			TeamBStats.TeamScore++;
		}
	}

	// Schedule respawn
	FTimerDelegate RespawnDelegate;
	RespawnDelegate.BindUFunction(this, FName("RespawnPlayer"), Victim);
	GetWorldTimerManager().SetTimer(RespawnTimerHandle, RespawnDelegate, RespawnDelay, false);
}

void ACFShooterGameMode::AddScore(APlayerController* Player, int32 Points)
{
	if (Player && PlayerStatsMap.Contains(Player))
	{
		PlayerStatsMap[Player].Score += Points;
	}
}

FPlayerStats ACFShooterGameMode::GetPlayerStats(APlayerController* Player)
{
	if (Player && PlayerStatsMap.Contains(Player))
	{
		return PlayerStatsMap[Player];
	}
	return FPlayerStats();
}

int32 ACFShooterGameMode::GetTeamPlayerCount(ETeam Team)
{
	if (Team == ETeam::TeamA)
	{
		return TeamAStats.TeamMembers.Num();
	}
	else if (Team == ETeam::TeamB)
	{
		return TeamBStats.TeamMembers.Num();
	}
	return 0;
}

ETeam ACFShooterGameMode::GetTeamWithFewerPlayers()
{
	int32 TeamACount = GetTeamPlayerCount(ETeam::TeamA);
	int32 TeamBCount = GetTeamPlayerCount(ETeam::TeamB);

	if (TeamACount <= TeamBCount)
	{
		return ETeam::TeamA;
	}
	return ETeam::TeamB;
}

FTransform ACFShooterGameMode::GetSpawnTransform(ETeam Team)
{
	TArray<AActor*>* SpawnPoints = nullptr;
	
	if (Team == ETeam::TeamA && TeamASpawnPoints.Num() > 0)
	{
		SpawnPoints = &TeamASpawnPoints;
	}
	else if (Team == ETeam::TeamB && TeamBSpawnPoints.Num() > 0)
	{
		SpawnPoints = &TeamBSpawnPoints;
	}

	if (SpawnPoints && SpawnPoints->Num() > 0)
	{
		int32 RandomIndex = FMath::RandRange(0, SpawnPoints->Num() - 1);
		return (*SpawnPoints)[RandomIndex]->GetActorTransform();
	}

	// Fallback to default spawn
	return FTransform::Identity;
}

float ACFShooterGameMode::GetMatchTimeRemaining() const
{
	if (CurrentGameState != EGameState::InProgress) return 0.0f;
	
	float ElapsedTime = GetWorld()->GetTimeSeconds() - MatchStartTime;
	return FMath::Max(0.0f, MatchDuration - ElapsedTime);
}

FTeamStats ACFShooterGameMode::GetTeamStats(ETeam Team) const
{
	if (Team == ETeam::TeamA)
	{
		return TeamAStats;
	}
	else if (Team == ETeam::TeamB)
	{
		return TeamBStats;
	}
	return FTeamStats();
}

void ACFShooterGameMode::CheckMatchEnd()
{
	// Check if any team reached the score limit
	if (TeamAStats.TeamScore >= ScoreToWin || TeamBStats.TeamScore >= ScoreToWin)
	{
		EndMatch();
	}
}

void ACFShooterGameMode::HandleMatchTimeout()
{
	EndMatch();
}

ETeam ACFShooterGameMode::DetermineWinningTeam()
{
	if (TeamAStats.TeamScore > TeamBStats.TeamScore)
	{
		return ETeam::TeamA;
	}
	else if (TeamBStats.TeamScore > TeamAStats.TeamScore)
	{
		return ETeam::TeamB;
	}
	return ETeam::None; // Draw
}
