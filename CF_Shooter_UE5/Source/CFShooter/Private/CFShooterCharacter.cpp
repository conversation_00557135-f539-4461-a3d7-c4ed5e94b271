#include "CFShooterCharacter.h"
#include "Camera/CameraComponent.h"
#include "GameFramework/SpringArmComponent.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "Components/SkeletalMeshComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Components/InputComponent.h"
#include "EnhancedInputComponent.h"
#include "EnhancedInputSubsystems.h"
#include "InputActionValue.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "Kismet/GameplayStatics.h"
#include "Kismet/KismetMathLibrary.h"
#include "DrawDebugHelpers.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "Sound/SoundBase.h"
#include "Animation/AnimMontage.h"

ACFShooterCharacter::ACFShooterCharacter()
{
	PrimaryActorTick.bCanEverTick = true;

	// Set size for collision capsule
	GetCapsuleComponent()->SetCapsuleSize(42.f, 96.0f);

	// Configure character movement
	GetCharacterMovement()->bOrientRotationToMovement = true;
	GetCharacterMovement()->RotationRate = FRotator(0.0f, 500.0f, 0.0f);
	GetCharacterMovement()->JumpZVelocity = 700.f;
	GetCharacterMovement()->AirControl = 0.35f;
	GetCharacterMovement()->MaxWalkSpeed = 500.f;
	GetCharacterMovement()->MinAnalogWalkSpeed = 20.f;
	GetCharacterMovement()->BrakingDecelerationWalking = 2000.f;

	// Don't rotate when the controller rotates
	bUseControllerRotationPitch = false;
	bUseControllerRotationYaw = false;
	bUseControllerRotationRoll = false;

	// Create Third Person Spring Arm
	ThirdPersonSpringArm = CreateDefaultSubobject<USpringArmComponent>(TEXT("ThirdPersonSpringArm"));
	ThirdPersonSpringArm->SetupAttachment(RootComponent);
	ThirdPersonSpringArm->TargetArmLength = 400.0f;
	ThirdPersonSpringArm->bUsePawnControlRotation = true;

	// Create Third Person Camera
	ThirdPersonCamera = CreateDefaultSubobject<UCameraComponent>(TEXT("ThirdPersonCamera"));
	ThirdPersonCamera->SetupAttachment(ThirdPersonSpringArm, USpringArmComponent::SocketName);
	ThirdPersonCamera->bUsePawnControlRotation = false;

	// Create First Person Camera
	FirstPersonCamera = CreateDefaultSubobject<UCameraComponent>(TEXT("FirstPersonCamera"));
	FirstPersonCamera->SetupAttachment(GetMesh(), TEXT("head"));
	FirstPersonCamera->bUsePawnControlRotation = true;
	FirstPersonCamera->SetActive(false);

	// Create Weapon Mesh
	WeaponMesh = CreateDefaultSubobject<USkeletalMeshComponent>(TEXT("WeaponMesh"));
	WeaponMesh->SetupAttachment(GetMesh(), TEXT("hand_r"));

	// Create Muzzle Flash Component
	MuzzleFlashComponent = CreateDefaultSubobject<UNiagaraComponent>(TEXT("MuzzleFlashComponent"));
	MuzzleFlashComponent->SetupAttachment(WeaponMesh, TEXT("MuzzleSocket"));
	MuzzleFlashComponent->SetAutoActivate(false);

	// Initialize variables
	MaxHealth = 100.0f;
	CurrentHealth = MaxHealth;
	CurrentAmmo = 30;
	TotalAmmo = 120;
	bIsReloading = false;
	bCanFire = true;
	bIsAiming = false;
	bIsSprinting = false;
	bIsDead = false;
	CurrentMovementState = EMovementState::Idle;
	CurrentWeaponID = TEXT("AK47");
}

void ACFShooterCharacter::BeginPlay()
{
	Super::BeginPlay();

	// Add Input Mapping Context
	if (APlayerController* PlayerController = Cast<APlayerController>(Controller))
	{
		if (UEnhancedInputLocalPlayerSubsystem* Subsystem = ULocalPlayer::GetSubsystem<UEnhancedInputLocalPlayerSubsystem>(PlayerController->GetLocalPlayer()))
		{
			Subsystem->AddMappingContext(DefaultMappingContext, 0);
		}
	}

	// Initialize weapon data
	if (WeaponDataTable)
	{
		FWeaponData* WeaponData = WeaponDataTable->FindRow<FWeaponData>(CurrentWeaponID, TEXT(""));
		if (WeaponData)
		{
			CurrentWeaponData = *WeaponData;
			CurrentAmmo = CurrentWeaponData.MagazineSize;
			
			if (WeaponMesh && CurrentWeaponData.WeaponMesh)
			{
				WeaponMesh->SetSkeletalMesh(CurrentWeaponData.WeaponMesh);
			}
		}
	}

	CurrentHealth = MaxHealth;
}

void ACFShooterCharacter::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
	
	UpdateMovementState();
}

void ACFShooterCharacter::SetupPlayerInputComponent(UInputComponent* PlayerInputComponent)
{
	Super::SetupPlayerInputComponent(PlayerInputComponent);

	if (UEnhancedInputComponent* EnhancedInputComponent = CastChecked<UEnhancedInputComponent>(PlayerInputComponent))
	{
		// Jumping
		EnhancedInputComponent->BindAction(JumpAction, ETriggerEvent::Triggered, this, &ACharacter::Jump);
		EnhancedInputComponent->BindAction(JumpAction, ETriggerEvent::Completed, this, &ACharacter::StopJumping);

		// Moving
		EnhancedInputComponent->BindAction(MoveAction, ETriggerEvent::Triggered, this, &ACFShooterCharacter::Move);

		// Looking
		EnhancedInputComponent->BindAction(LookAction, ETriggerEvent::Triggered, this, &ACFShooterCharacter::Look);

		// Firing
		EnhancedInputComponent->BindAction(FireAction, ETriggerEvent::Started, this, &ACFShooterCharacter::StartFire);
		EnhancedInputComponent->BindAction(FireAction, ETriggerEvent::Completed, this, &ACFShooterCharacter::StopFire);

		// Aiming
		EnhancedInputComponent->BindAction(AimAction, ETriggerEvent::Started, this, &ACFShooterCharacter::StartAim);
		EnhancedInputComponent->BindAction(AimAction, ETriggerEvent::Completed, this, &ACFShooterCharacter::StopAim);

		// Reloading
		EnhancedInputComponent->BindAction(ReloadAction, ETriggerEvent::Triggered, this, &ACFShooterCharacter::Reload);

		// Crouching
		EnhancedInputComponent->BindAction(CrouchAction, ETriggerEvent::Started, this, &ACFShooterCharacter::StartCrouch);
		EnhancedInputComponent->BindAction(CrouchAction, ETriggerEvent::Completed, this, &ACFShooterCharacter::StopCrouch);

		// Sprinting
		EnhancedInputComponent->BindAction(SprintAction, ETriggerEvent::Started, this, &ACFShooterCharacter::StartSprint);
		EnhancedInputComponent->BindAction(SprintAction, ETriggerEvent::Completed, this, &ACFShooterCharacter::StopSprint);
	}
}

void ACFShooterCharacter::Move(const FInputActionValue& Value)
{
	if (bIsDead) return;

	FVector2D MovementVector = Value.Get<FVector2D>();

	if (Controller != nullptr)
	{
		// Find out which way is forward
		const FRotator Rotation = Controller->GetControlRotation();
		const FRotator YawRotation(0, Rotation.Yaw, 0);

		// Get forward vector
		const FVector ForwardDirection = FRotationMatrix(YawRotation).GetUnitAxis(EAxis::X);
		
		// Get right vector 
		const FVector RightDirection = FRotationMatrix(YawRotation).GetUnitAxis(EAxis::Y);

		// Add movement 
		AddMovementInput(ForwardDirection, MovementVector.Y);
		AddMovementInput(RightDirection, MovementVector.X);
	}
}

void ACFShooterCharacter::Look(const FInputActionValue& Value)
{
	if (bIsDead) return;

	FVector2D LookAxisVector = Value.Get<FVector2D>();

	if (Controller != nullptr)
	{
		// Add yaw and pitch input to controller
		AddControllerYawInput(LookAxisVector.X);
		AddControllerPitchInput(LookAxisVector.Y);
	}
}

void ACFShooterCharacter::StartFire()
{
	if (bIsDead || bIsReloading || !bCanFire) return;

	Fire();
	
	// Set up automatic fire
	GetWorldTimerManager().SetTimer(FireTimerHandle, this, &ACFShooterCharacter::Fire, 
		60.0f / CurrentWeaponData.FireRate, true);
}

void ACFShooterCharacter::StopFire()
{
	GetWorldTimerManager().ClearTimer(FireTimerHandle);
}

void ACFShooterCharacter::Fire()
{
	if (bIsDead || bIsReloading || !bCanFire || CurrentAmmo <= 0) return;

	// Decrease ammo
	CurrentAmmo--;

	// Play fire effects
	PlayFireEffects();

	// Perform line trace for hit detection
	PerformLineTrace();

	// Handle recoil
	HandleRecoil();

	// Auto reload if out of ammo
	if (CurrentAmmo <= 0 && TotalAmmo > 0)
	{
		Reload();
	}
}

void ACFShooterCharacter::PlayFireEffects()
{
	// Play muzzle flash
	if (MuzzleFlashComponent && CurrentWeaponData.MuzzleFlash)
	{
		MuzzleFlashComponent->SetAsset(CurrentWeaponData.MuzzleFlash);
		MuzzleFlashComponent->Activate(true);
	}

	// Play fire sound
	if (CurrentWeaponData.FireSound)
	{
		UGameplayStatics::PlaySoundAtLocation(this, CurrentWeaponData.FireSound, GetActorLocation());
	}

	// Play fire animation
	if (CurrentWeaponData.FireAnimation)
	{
		PlayAnimMontage(CurrentWeaponData.FireAnimation);
	}
}

void ACFShooterCharacter::PerformLineTrace()
{
	FVector Start = FirstPersonCamera->GetComponentLocation();
	FVector ForwardVector = FirstPersonCamera->GetForwardVector();
	FVector End = Start + (ForwardVector * CurrentWeaponData.Range);

	FHitResult HitResult;
	FCollisionQueryParams QueryParams;
	QueryParams.AddIgnoredActor(this);

	bool bHit = GetWorld()->LineTraceSingleByChannel(HitResult, Start, End, ECC_Visibility, QueryParams);

	if (bHit)
	{
		// Handle hit logic here
		AActor* HitActor = HitResult.GetActor();
		if (HitActor)
		{
			// Apply damage if it's another character
			if (ACFShooterCharacter* HitCharacter = Cast<ACFShooterCharacter>(HitActor))
			{
				HitCharacter->TakeDamage(CurrentWeaponData.Damage);
			}
		}

		// Spawn bullet trail effect
		if (CurrentWeaponData.BulletTrail)
		{
			UNiagaraFunctionLibrary::SpawnSystemAtLocation(GetWorld(), CurrentWeaponData.BulletTrail, 
				WeaponMesh->GetSocketLocation(TEXT("MuzzleSocket")));
		}
	}

	// Debug line trace
	DrawDebugLine(GetWorld(), Start, End, FColor::Red, false, 1.0f);
}

void ACFShooterCharacter::HandleRecoil()
{
	if (Controller)
	{
		// Add random recoil to controller
		float RecoilPitch = FMath::RandRange(-2.0f, -0.5f);
		float RecoilYaw = FMath::RandRange(-1.0f, 1.0f);

		AddControllerPitchInput(RecoilPitch);
		AddControllerYawInput(RecoilYaw);
	}
}

void ACFShooterCharacter::StartAim()
{
	if (bIsDead) return;

	bIsAiming = true;

	// Switch to first person camera
	FirstPersonCamera->SetActive(true);
	ThirdPersonCamera->SetActive(false);

	// Reduce movement speed while aiming
	GetCharacterMovement()->MaxWalkSpeed = 200.0f;
}

void ACFShooterCharacter::StopAim()
{
	bIsAiming = false;

	// Switch back to third person camera
	FirstPersonCamera->SetActive(false);
	ThirdPersonCamera->SetActive(true);

	// Restore normal movement speed
	GetCharacterMovement()->MaxWalkSpeed = bIsSprinting ? 800.0f : 500.0f;
}

void ACFShooterCharacter::Reload()
{
	if (bIsDead || bIsReloading || TotalAmmo <= 0 || CurrentAmmo >= CurrentWeaponData.MagazineSize) return;

	bIsReloading = true;
	bCanFire = false;

	// Play reload animation
	if (CurrentWeaponData.ReloadAnimation)
	{
		PlayAnimMontage(CurrentWeaponData.ReloadAnimation);
	}

	// Play reload sound
	if (CurrentWeaponData.ReloadSound)
	{
		UGameplayStatics::PlaySoundAtLocation(this, CurrentWeaponData.ReloadSound, GetActorLocation());
	}

	// Set timer for reload completion
	GetWorldTimerManager().SetTimer(ReloadTimerHandle, this, &ACFShooterCharacter::PerformReload,
		CurrentWeaponData.ReloadTime, false);
}

void ACFShooterCharacter::PerformReload()
{
	int32 AmmoNeeded = CurrentWeaponData.MagazineSize - CurrentAmmo;
	int32 AmmoToReload = FMath::Min(AmmoNeeded, TotalAmmo);

	CurrentAmmo += AmmoToReload;
	TotalAmmo -= AmmoToReload;

	bIsReloading = false;
	bCanFire = true;
}

void ACFShooterCharacter::StartCrouch()
{
	if (bIsDead) return;

	Crouch();
	GetCharacterMovement()->MaxWalkSpeed = 250.0f;
}

void ACFShooterCharacter::StopCrouch()
{
	UnCrouch();
	GetCharacterMovement()->MaxWalkSpeed = bIsSprinting ? 800.0f : 500.0f;
}

void ACFShooterCharacter::StartSprint()
{
	if (bIsDead || bIsAiming) return;

	bIsSprinting = true;
	GetCharacterMovement()->MaxWalkSpeed = 800.0f;
}

void ACFShooterCharacter::StopSprint()
{
	bIsSprinting = false;
	GetCharacterMovement()->MaxWalkSpeed = 500.0f;
}

void ACFShooterCharacter::SwitchWeapon(FName WeaponID)
{
	if (bIsDead || bIsReloading) return;

	if (WeaponDataTable)
	{
		FWeaponData* WeaponData = WeaponDataTable->FindRow<FWeaponData>(WeaponID, TEXT(""));
		if (WeaponData)
		{
			CurrentWeaponID = WeaponID;
			CurrentWeaponData = *WeaponData;
			CurrentAmmo = CurrentWeaponData.MagazineSize;

			if (WeaponMesh && CurrentWeaponData.WeaponMesh)
			{
				WeaponMesh->SetSkeletalMesh(CurrentWeaponData.WeaponMesh);
			}
		}
	}
}

void ACFShooterCharacter::TakeDamage(float DamageAmount)
{
	if (bIsDead) return;

	CurrentHealth = FMath::Clamp(CurrentHealth - DamageAmount, 0.0f, MaxHealth);

	if (CurrentHealth <= 0.0f)
	{
		Die();
	}
}

void ACFShooterCharacter::Die()
{
	bIsDead = true;
	bCanFire = false;

	// Stop all timers
	GetWorldTimerManager().ClearTimer(FireTimerHandle);
	GetWorldTimerManager().ClearTimer(ReloadTimerHandle);

	// Disable movement
	GetCharacterMovement()->DisableMovement();

	// Play death animation or effects here
}

void ACFShooterCharacter::UpdateMovementState()
{
	if (bIsDead)
	{
		CurrentMovementState = EMovementState::Idle;
		return;
	}

	FVector Velocity = GetVelocity();
	float Speed = Velocity.Size();

	if (bIsAiming)
	{
		CurrentMovementState = EMovementState::Aiming;
	}
	else if (GetCharacterMovement()->IsFalling())
	{
		CurrentMovementState = EMovementState::Jumping;
	}
	else if (bIsCrouched)
	{
		CurrentMovementState = EMovementState::Crouching;
	}
	else if (Speed > 600.0f)
	{
		CurrentMovementState = EMovementState::Running;
	}
	else if (Speed > 10.0f)
	{
		CurrentMovementState = EMovementState::Walking;
	}
	else
	{
		CurrentMovementState = EMovementState::Idle;
	}
}

float ACFShooterCharacter::GetCurrentAmmoPercentage() const
{
	if (CurrentWeaponData.MagazineSize <= 0) return 0.0f;
	return static_cast<float>(CurrentAmmo) / static_cast<float>(CurrentWeaponData.MagazineSize);
}

float ACFShooterCharacter::GetHealthPercentage() const
{
	if (MaxHealth <= 0.0f) return 0.0f;
	return CurrentHealth / MaxHealth;
}
