# CF Shooter 开发指南

## 蓝图设置指南

### 1. 角色蓝图 (BP_CFShooterCharacter)

#### 基础设置
```
Parent Class: CFShooterCharacter (C++)
Location: Content/Blueprints/Characters/BP_CFShooterCharacter
```

#### 组件配置
- **Skeletal Mesh**: 使用MetaHuman或自定义角色模型
- **Camera Components**: 已在C++中设置
- **Weapon Mesh**: 武器骨骼网格组件
- **Audio Components**: 脚步声、呼吸声等

#### 动画蓝图
```
Animation Blueprint: ABP_CFShooterCharacter
State Machine States:
- Idle/Walk/Run Blendspace
- Jump Start/Loop/End
- Crouch Idle/Walk
- Aim Offset
- Fire Additive
- Reload Montages
```

#### 输入映射
```
Enhanced Input Mapping Context: IMC_CFShooter
Input Actions:
- IA_Move (Vector2D)
- IA_Look (Vector2D)  
- IA_Jump (Boolean)
- IA_Fire (Boolean)
- IA_Aim (Boolean)
- IA_Reload (Boolean)
- <PERSON><PERSON>_<PERSON>rouch (Boolean)
- IA_Sprint (Boolean)
```

### 2. 游戏模式蓝图 (BP_CFShooterGameMode)

#### 基础设置
```
Parent Class: CFShooterGameMode (C++)
Default Pawn Class: BP_CFShooterCharacter
HUD Class: BP_CFShooterHUD
Player Controller Class: BP_CFShooterPlayerController
Game State Class: BP_CFShooterGameState
Player State Class: BP_CFShooterPlayerState
```

#### 团队设置
```
Team A Spawn Points: PlayerStart with Tag "TeamA"
Team B Spawn Points: PlayerStart with Tag "TeamB"
Match Settings:
- Match Duration: 600 seconds
- Score to Win: 50
- Max Players: 16
- Respawn Delay: 5 seconds
```

### 3. AI角色蓝图 (BP_CFShooterAI)

#### 基础设置
```
Parent Class: CFShooterAICharacter (C++)
AI Controller Class: BP_CFShooterAIController
Behavior Tree: BT_CFShooterAI
Blackboard: BB_CFShooterAI
```

#### 感知设置
```
AI Perception Component:
- Sight Config:
  - Sight Radius: 1500.0
  - Lose Sight Radius: 1600.0
  - Peripheral Vision Angle: 90.0
  - Detection by Affiliation: Neutrals and Enemies
  
- Hearing Config:
  - Hearing Range: 1000.0
  - Detection by Affiliation: Neutrals and Enemies
```

#### 行为树节点
```
Root Sequence:
├── Service: Update Player Location
├── Selector: Main Behavior
│   ├── Sequence: Combat Behavior
│   │   ├── Decorator: Has Target
│   │   ├── Task: Move To Target
│   │   └── Task: Attack Target
│   ├── Sequence: Investigate
│   │   ├── Decorator: Has Noise Location
│   │   ├── Task: Move To Location
│   │   └── Task: Wait
│   └── Sequence: Patrol
│       ├── Task: Get Patrol Point
│       ├── Task: Move To Location
│       └── Task: Wait
```

### 4. 武器系统蓝图

#### 武器基类 (BP_CFWeapon)
```
Parent Class: CFWeapon (C++)
Components:
- Skeletal Mesh Component
- Audio Component
- Niagara Component (Muzzle Flash)
```

#### 具体武器蓝图
```
BP_AK47 (Assault Rifle):
- Damage: 30
- Fire Rate: 600 RPM
- Magazine Size: 30
- Range: 800m
- Accuracy: 0.85

BP_AWP (Sniper Rifle):
- Damage: 100
- Fire Rate: 40 RPM
- Magazine Size: 10
- Range: 2000m
- Accuracy: 0.98

BP_MP5 (SMG):
- Damage: 20
- Fire Rate: 800 RPM
- Magazine Size: 30
- Range: 400m
- Accuracy: 0.75
```

#### 武器数据表 (DT_WeaponData)
```
Row Structure: WeaponData (C++ Struct)
Rows:
- AK47, AWP, MP5, M4A1, Glock, etc.
```

### 5. UI蓝图系统

#### 主HUD (WBP_MainHUD)
```
Components:
- Health Bar (Progress Bar)
- Ammo Counter (Text)
- Crosshair (Image)
- Mini Map (User Widget)
- Kill Feed (Vertical Box)
```

#### 计分板 (WBP_Scoreboard)
```
Components:
- Team A Panel (Vertical Box)
- Team B Panel (Vertical Box)
- Player Entry Widget (WBP_PlayerEntry)
- Match Timer (Text)
- Team Scores (Text)
```

#### 暂停菜单 (WBP_PauseMenu)
```
Components:
- Resume Button
- Settings Button
- Quit Button
- Background Blur
```

## 地图制作指南

### 1. 地图结构
```
World Partition: Enabled
Streaming: Distance-based
Grid Size: 1600x1600
```

### 2. 重生点设置
```
PlayerStart Actors:
- Team A: Tag "TeamA", Blue color
- Team B: Tag "TeamB", Red color
- Spacing: Minimum 200 units apart
```

### 3. 导航网格
```
NavMesh Bounds Volume: Cover entire playable area
RecastNavMesh Settings:
- Agent Radius: 34
- Agent Height: 144
- Max Slope: 44 degrees
```

### 4. 光照设置
```
Directional Light: Sun light
Sky Light: HDRI-based
Post Process Volume: Global settings
Lumen Global Illumination: Enabled
```

## 材质和效果

### 1. 角色材质
```
Master Material: M_Character_Master
Parameters:
- Base Color
- Normal Map
- Roughness
- Metallic
- Subsurface Color (for skin)
```

### 2. 武器材质
```
Master Material: M_Weapon_Master
Parameters:
- Base Color
- Normal Map
- Roughness
- Metallic
- Wear Mask
```

### 3. 粒子效果
```
Muzzle Flash: NS_MuzzleFlash
Bullet Trail: NS_BulletTrail
Blood Effect: NS_BloodSplatter
Explosion: NS_Explosion
```

## 音频设置

### 1. 音频分类
```
Sound Classes:
- Master
  ├── SFX
  │   ├── Weapons
  │   ├── Footsteps
  │   └── UI
  ├── Music
  └── Voice
```

### 2. 音频衰减
```
Weapon Sounds: 
- Inner Radius: 500
- Falloff Distance: 2000
- Attenuation Function: Natural Sound

Footsteps:
- Inner Radius: 100
- Falloff Distance: 500
- Attenuation Function: Linear
```

## 网络设置

### 1. 复制设置
```
Character Replication:
- Movement: Replicated
- Health: Replicated
- Weapon State: Replicated
- Animation State: Replicated

Weapon Replication:
- Fire Events: Multicast
- Ammo Count: Replicated
- Reload State: Replicated
```

### 2. 服务器权威
```
Hit Detection: Server-side
Damage Application: Server-side
Score Updates: Server-side
Match State: Server-side
```

## 性能优化建议

### 1. 渲染优化
```
LOD Settings:
- Characters: 4 LOD levels
- Weapons: 3 LOD levels
- Environment: Auto-generated LODs

Culling:
- Frustum Culling: Enabled
- Occlusion Culling: Enabled
- Distance Culling: Per-object settings
```

### 2. 动画优化
```
Animation LOD:
- LOD 0: Full skeleton (0-500 units)
- LOD 1: Reduced bones (500-1000 units)  
- LOD 2: Major bones only (1000+ units)

Update Rate Optimization:
- Visible: 60 FPS
- Not Visible: 15 FPS
```

### 3. AI优化
```
Behavior Tree Optimization:
- Service Update Intervals: 0.1-0.5 seconds
- Perception Update Rate: 10 Hz
- Blackboard Key Updates: Event-driven

Pathfinding:
- NavMesh Generation: Runtime
- Path Smoothing: Enabled
- Crowd Simulation: Enabled for groups
```

## 调试工具

### 1. 控制台命令
```
showdebug ai - 显示AI调试信息
showdebug weapon - 显示武器调试信息
showdebug net - 显示网络调试信息
stat fps - 显示帧率
stat memory - 显示内存使用
```

### 2. 可视化调试
```
AI Debugging:
- Behavior Tree Debugger
- Blackboard Debugger
- Perception Debugger

Network Debugging:
- Network Profiler
- Replication Graph
- Packet Loss Visualization
```

## 测试流程

### 1. 单人测试
```
1. 角色移动和动画
2. 武器射击和重装
3. AI行为和反应
4. UI功能和响应
```

### 2. 多人测试
```
1. 网络同步
2. 延迟补偿
3. 服务器性能
4. 客户端预测
```

### 3. 性能测试
```
1. 帧率稳定性
2. 内存使用
3. 网络带宽
4. 服务器负载
```

这个开发指南提供了完整的蓝图设置和开发流程，帮助开发者快速上手CF Shooter项目的开发工作。
