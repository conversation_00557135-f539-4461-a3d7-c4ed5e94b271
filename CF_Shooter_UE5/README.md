# CF Shooter - 虚幻引擎5 战术射击游戏

## 项目概述

CF Shooter 是一款基于虚幻引擎5开发的现代战术射击游戏，灵感来源于经典的CF（穿越火线）游戏。游戏具有逼真的人物动作、精细的枪械系统、智能AI敌人和沉浸式的战斗体验。

## 主要特性

### 🎮 核心游戏功能
- **多人对战模式**: 支持16人同时在线对战
- **团队竞技**: 红蓝两队对抗，目标导向的游戏玩法
- **多种游戏模式**: 团队死斗、爆破模式、占点模式
- **实时排行榜**: 击杀/死亡/助攻统计系统

### 🔫 武器系统
- **多样化武器**: 突击步枪、狙击步枪、冲锋枪、霰弹枪、手枪、轻机枪
- **真实弹道**: 物理弹道计算，考虑重力和空气阻力
- **武器定制**: 配件系统，瞄准镜、消音器、握把等
- **后坐力系统**: 每种武器独特的后坐力模式
- **弹药管理**: 有限弹药，需要战术性重装

### 👤 角色系统
- **流畅动作**: 基于动画蓝图的角色动画系统
- **多种姿态**: 站立、蹲伏、匍匐、跳跃、瞄准
- **生命系统**: 100点生命值，受伤恢复机制
- **移动系统**: 走路、跑步、冲刺，不同速度影响精准度

### 🤖 AI系统
- **智能敌人**: 基于行为树的AI系统
- **多种AI状态**: 巡逻、调查、追击、攻击、搜索
- **感知系统**: 视觉和听觉感知，动态反应
- **战术行为**: 掩护、包抄、团队协作

### 🎨 视觉效果
- **现代渲染**: 虚幻引擎5 Lumen全局光照
- **粒子效果**: Niagara粒子系统，枪口火焰、弹道轨迹
- **后处理**: 动态模糊、景深、色彩分级
- **材质系统**: PBR材质，真实的表面反射

### 🔊 音频系统
- **3D音效**: 空间音频，方向性枪声和脚步声
- **动态音乐**: 根据游戏状态变化的背景音乐
- **武器音效**: 每种武器独特的射击、装弹、空膛音效
- **环境音效**: 风声、脚步声、装备声音

## 技术架构

### 引擎版本
- **虚幻引擎 5.3+**
- **C++ 核心逻辑**
- **蓝图 UI和游戏逻辑**

### 核心模块

#### 1. 角色系统 (CFShooterCharacter)
```cpp
// 主要功能
- 第一人称/第三人称视角切换
- 增强输入系统 (Enhanced Input)
- 武器装备和切换
- 生命值和伤害系统
- 移动状态管理
```

#### 2. 游戏模式 (CFShooterGameMode)
```cpp
// 主要功能
- 玩家匹配和团队分配
- 比赛计时和得分系统
- 重生机制
- 胜负判定
```

#### 3. AI系统 (CFShooterAICharacter)
```cpp
// 主要功能
- 行为树AI控制
- 感知系统 (视觉/听觉)
- 巡逻和战斗行为
- 动态难度调整
```

#### 4. 武器系统 (CFWeapon)
```cpp
// 主要功能
- 武器数据表驱动
- 射击模式 (单发/连发/点射)
- 弹道计算和命中检测
- 后坐力和精准度系统
```

#### 5. UI系统 (CFShooterHUD)
```cpp
// 主要功能
- 游戏内HUD显示
- 准星和命中标记
- 计分板和暂停菜单
- 死亡界面
```

## 开发环境设置

### 系统要求
- **操作系统**: Windows 10/11 64位, macOS 12+, Ubuntu 20.04+
- **内存**: 16GB RAM (推荐32GB)
- **显卡**: DirectX 12兼容显卡，8GB显存
- **存储**: 50GB可用空间 (SSD推荐)

### 🍎 Mac用户快速启动

1. **安装必要软件**
   - 从App Store安装Xcode
   - 下载并安装[Epic Games Launcher](https://www.epicgames.com/store/download)
   - 通过Epic Games Launcher安装虚幻引擎5.3+

2. **启动项目**
   ```bash
   # 进入项目目录
   cd CF_Shooter_UE5

   # 运行快速启动脚本
   ./QuickStart.sh
   ```

3. **详细设置指南**
   - 查看 [MAC_SETUP_GUIDE.md](MAC_SETUP_GUIDE.md) 获取完整的Mac设置说明

### 🪟 Windows用户

1. **安装虚幻引擎5**
   ```bash
   # 从Epic Games Launcher安装UE5.3+
   ```

2. **编译项目**
   ```bash
   # 运行构建脚本
   Build.bat
   ```

### 🐧 Linux用户

1. **生成项目文件**
   ```bash
   ./GenerateProjectFiles.sh
   ```

2. **编译项目**
   ```bash
   make CFShooterEditor
   ```

## 游戏控制

### 键盘鼠标控制
- **WASD**: 移动
- **鼠标**: 视角控制
- **左键**: 射击
- **右键**: 瞄准
- **R**: 重装弹药
- **Shift**: 冲刺
- **Ctrl**: 蹲伏
- **空格**: 跳跃
- **1/2/3**: 切换武器
- **Tab**: 计分板
- **Esc**: 暂停菜单

### 手柄控制
- **左摇杆**: 移动
- **右摇杆**: 视角控制
- **RT**: 射击
- **LT**: 瞄准
- **X**: 重装弹药
- **A**: 跳跃
- **左摇杆按下**: 冲刺
- **右摇杆按下**: 蹲伏

## 开发指南

### 添加新武器
1. 在武器数据表中添加新条目
2. 创建武器网格和材质
3. 设置动画蒙太奇
4. 配置音效和粒子效果

### 创建新地图
1. 使用World Partition系统
2. 设置导航网格
3. 放置重生点 (PlayerStart)
4. 添加环境音效和光照

### AI行为定制
1. 修改行为树资产
2. 创建自定义任务节点
3. 调整感知参数
4. 测试AI响应

## 性能优化

### 渲染优化
- 使用LOD系统减少多边形数量
- 启用遮挡剔除
- 优化材质复杂度
- 使用实例化渲染

### 网络优化
- 压缩网络数据包
- 预测性移动
- 服务器权威验证
- 延迟补偿

## 故障排除

### 常见问题
1. **编译错误**: 检查UE5版本兼容性
2. **性能问题**: 降低图形设置
3. **网络延迟**: 检查服务器连接
4. **AI不响应**: 验证行为树设置

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 许可证

本项目采用MIT许可证 - 详见 [LICENSE](LICENSE) 文件

## 联系方式

- **开发团队**: CF Shooter Dev Team
- **邮箱**: <EMAIL>
- **Discord**: [CF Shooter Community](https://discord.gg/cfshooter)

---

**注意**: 这是一个演示项目，用于展示虚幻引擎5游戏开发技术。实际部署需要额外的优化和测试。
