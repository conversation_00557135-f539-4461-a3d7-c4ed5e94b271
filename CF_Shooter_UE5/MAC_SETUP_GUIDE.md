# CF Shooter Mac 设置指南

## 🍎 在Mac上启动CF Shooter项目

### 前置要求

#### 系统要求
```bash
# 检查你的Mac系统
system_profiler SPSoftwareDataType SPHardwareDataType

# 最低要求：
# - macOS 12.0 (Monterey) 或更高
# - Apple Silicon (M1/M2) 或 Intel Mac
# - 16GB RAM (推荐32GB)
# - 50GB 可用存储空间
# - Metal 兼容显卡
```

#### 必需软件
- **Xcode** (从App Store安装)
- **Epic Games Launcher**
- **虚幻引擎 5.3+**

### 步骤1: 安装开发环境

#### 1.1 安装Xcode
```bash
# 从App Store安装Xcode，或者只安装命令行工具
xcode-select --install

# 验证安装
xcode-select -p
# 应该显示: /Applications/Xcode.app/Contents/Developer
```

#### 1.2 安装Epic Games Launcher
1. 访问 [Epic Games官网](https://www.epicgames.com/store/download)
2. 下载Mac版Epic Games Launcher
3. 安装并登录你的Epic账户

#### 1.3 安装虚幻引擎5
1. 打开Epic Games Launcher
2. 点击"虚幻引擎"标签
3. 点击"+"添加引擎版本
4. 选择 **5.3** 或更高版本
5. 点击"安装"

> **注意**: 虚幻引擎5下载大小约15-20GB，安装后占用约40GB空间

### 步骤2: 准备项目

#### 2.1 检查项目文件
```bash
# 进入项目目录
cd /path/to/CF_Shooter_UE5

# 检查关键文件
ls -la
# 应该看到:
# - CF_Shooter.uproject
# - Source/ 目录
# - Content/ 目录
# - Config/ 目录
```

#### 2.2 设置构建脚本权限
```bash
# 给构建脚本执行权限
chmod +x Build.sh

# 验证权限
ls -la Build.sh
# 应该显示: -rwxr-xr-x
```

### 步骤3: 构建项目

#### 3.1 使用构建脚本（推荐）
```bash
# 运行构建脚本
./Build.sh

# 脚本会自动：
# 1. 查找虚幻引擎5安装路径
# 2. 生成Xcode项目文件
# 3. 编译游戏代码
# 4. 编译编辑器代码
```

#### 3.2 手动构建（如果脚本失败）
```bash
# 找到虚幻引擎安装路径
find /Users -name "UnrealBuildTool" 2>/dev/null | grep "5\."

# 设置环境变量（替换为你的实际路径）
export UE5_PATH="/Users/<USER>/Epic Games/UE_5.3/Engine/Binaries/DotNET/UnrealBuildTool/UnrealBuildTool"

# 生成项目文件
"$UE5_PATH" -projectfiles -project="$(pwd)/CF_Shooter.uproject" -game -rocket -progress

# 编译项目
"$UE5_PATH" CFShooter Mac Development -project="$(pwd)/CF_Shooter.uproject" -rocket -progress

# 编译编辑器
"$UE5_PATH" CFShooterEditor Mac Development -project="$(pwd)/CF_Shooter.uproject" -rocket -progress
```

### 步骤4: 启动项目

#### 4.1 通过Epic Games Launcher启动
1. 打开Epic Games Launcher
2. 点击"虚幻引擎"标签
3. 在已安装的引擎版本旁点击"启动"
4. 在项目浏览器中点击"浏览"
5. 选择 `CF_Shooter.uproject` 文件
6. 点击"打开"

#### 4.2 通过命令行启动
```bash
# 找到虚幻编辑器路径
UE5_EDITOR="/Users/<USER>/Epic Games/UE_5.3/Engine/Binaries/Mac/UnrealEditor.app/Contents/MacOS/UnrealEditor"

# 启动编辑器
"$UE5_EDITOR" "$(pwd)/CF_Shooter.uproject"
```

#### 4.3 通过Finder启动
1. 在Finder中导航到项目文件夹
2. 双击 `CF_Shooter.uproject` 文件
3. 选择要使用的虚幻引擎版本

### 步骤5: 验证安装

#### 5.1 检查编译结果
```bash
# 检查编译输出
ls -la Binaries/Mac/
# 应该看到编译后的可执行文件

# 检查中间文件
ls -la Intermediate/Build/Mac/
# 应该看到编译的中间文件
```

#### 5.2 在编辑器中测试
1. 项目加载后，点击"播放"按钮
2. 测试角色移动（WASD键）
3. 测试鼠标视角控制
4. 测试射击功能（左键）

### 常见问题解决

#### 问题1: "找不到虚幻引擎"
```bash
# 手动查找虚幻引擎
find /Users -name "UnrealEditor.app" 2>/dev/null
find /Applications -name "*Epic*" 2>/dev/null

# 常见安装路径：
# /Users/<USER>/Epic Games/UE_5.3/
# /Applications/Epic Games/UE_5.3/
# /Users/<USER>/Epic Games/UE_5.3/
```

#### 问题2: "编译失败"
```bash
# 清理项目
rm -rf Binaries/ Intermediate/

# 重新生成项目文件
# 然后重新运行构建脚本
./Build.sh
```

#### 问题3: "权限被拒绝"
```bash
# 给所有脚本执行权限
find . -name "*.sh" -exec chmod +x {} \;

# 检查Xcode命令行工具
xcode-select --install
```

#### 问题4: "Metal着色器编译错误"
1. 确保你的Mac支持Metal
2. 更新macOS到最新版本
3. 在项目设置中检查渲染API设置

### 性能优化建议

#### 对于Apple Silicon Mac (M1/M2)
```bash
# 检查是否为原生Apple Silicon版本
file /path/to/UnrealEditor
# 应该显示: arm64

# 如果是Intel版本，考虑重新安装原生版本
```

#### 内存设置
- 关闭不必要的应用程序
- 在虚幻编辑器中降低预览质量
- 使用"开发"构建配置而非"调试"

#### 存储优化
```bash
# 清理派生数据缓存
rm -rf ~/Library/Developer/Xcode/DerivedData/*

# 清理虚幻引擎缓存
rm -rf ~/Library/Application\ Support/Epic/UnrealEngine/Common/DerivedDataCache/*
```

### 开发工作流程

#### 日常开发
1. 打开虚幻编辑器
2. 修改C++代码或蓝图
3. 使用"编译"按钮重新编译
4. 测试更改

#### 版本控制
```bash
# 添加.gitignore文件
echo "Binaries/" >> .gitignore
echo "Intermediate/" >> .gitignore
echo "DerivedDataCache/" >> .gitignore
echo ".vs/" >> .gitignore
echo "*.tmp" >> .gitignore
```

### 部署到其他平台

#### 构建iOS版本
1. 在项目设置中启用iOS平台
2. 配置iOS开发证书
3. 使用Package功能构建

#### 构建Windows版本
1. 安装Windows交叉编译工具链
2. 在项目设置中启用Windows平台
3. 使用Package功能构建

---

**提示**: 如果遇到任何问题，请检查虚幻引擎的输出日志，通常包含详细的错误信息。日志位置：`~/Library/Logs/Unreal Engine/`
