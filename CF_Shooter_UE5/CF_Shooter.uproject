{"FileVersion": 3, "EngineAssociation": "5.3", "Category": "", "Description": "CF Style Tactical Shooter Game", "Modules": [{"Name": "CFShooter", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "AdditionalDependencies": ["Engine", "CoreUObject", "UMG", "AIModule", "GameplayTasks", "NavigationSystem", "PhysicsCore", "Niagara", "AudioMixer"]}], "Plugins": [{"Name": "ModelingToolsEditorMode", "Enabled": true}, {"Name": "GeometryScript", "Enabled": true}, {"Name": "Niagara", "Enabled": true}, {"Name": "ChaosVehiclesPlugin", "Enabled": true}, {"Name": "MotionWarping", "Enabled": true}, {"Name": "GameplayAbilities", "Enabled": true}, {"Name": "OnlineSubsystem", "Enabled": true}, {"Name": "OnlineSubsystemSteam", "Enabled": true}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Enabled": true}, {"Name": "ControlRig", "Enabled": true}], "TargetPlatforms": ["Windows", "<PERSON>", "Linux"], "EpicSampleNameHash": "0"}