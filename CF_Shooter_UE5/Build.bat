@echo off
echo ========================================
echo CF Shooter UE5 Build Script
echo ========================================

set PROJECT_NAME=CFShooter
set PROJECT_FILE=%~dp0%PROJECT_NAME%.uproject
set UE5_PATH=C:\Program Files\Epic Games\UE_5.3\Engine\Binaries\DotNET\UnrealBuildTool\UnrealBuildTool.exe

echo Checking for Unreal Engine 5...
if not exist "%UE5_PATH%" (
    echo ERROR: Unreal Engine 5 not found at %UE5_PATH%
    echo Please update the UE5_PATH variable in this script
    pause
    exit /b 1
)

echo Found Unreal Engine 5 at: %UE5_PATH%

echo.
echo Checking for project file...
if not exist "%PROJECT_FILE%" (
    echo ERROR: Project file not found: %PROJECT_FILE%
    pause
    exit /b 1
)

echo Found project file: %PROJECT_FILE%

echo.
echo ========================================
echo Building %PROJECT_NAME% for Development...
echo ========================================

"%UE5_PATH%" %PROJECT_NAME% Win64 Development -project="%PROJECT_FILE%" -rocket -progress

if %ERRORLEVEL% neq 0 (
    echo.
    echo ========================================
    echo BUILD FAILED!
    echo ========================================
    pause
    exit /b %ERRORLEVEL%
)

echo.
echo ========================================
echo BUILD SUCCESSFUL!
echo ========================================

echo.
echo Building %PROJECT_NAME% Editor...
"%UE5_PATH%" %PROJECT_NAME%Editor Win64 Development -project="%PROJECT_FILE%" -rocket -progress

if %ERRORLEVEL% neq 0 (
    echo.
    echo ========================================
    echo EDITOR BUILD FAILED!
    echo ========================================
    pause
    exit /b %ERRORLEVEL%
)

echo.
echo ========================================
echo ALL BUILDS SUCCESSFUL!
echo ========================================
echo.
echo You can now open the project in Unreal Engine 5
echo Project file: %PROJECT_FILE%
echo.
pause
