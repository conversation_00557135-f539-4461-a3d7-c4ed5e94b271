#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建完整的盒马鲜生门店信息Excel表格（包含区县和业态类型）
"""

from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill
from openpyxl.utils import get_column_letter

def get_district_from_address(address):
    """从地址中提取区县信息"""
    districts = ['朝阳区', '海淀区', '丰台区', '西城区', '东城区', '石景山区', '昌平区', '大兴区', '通州区', '房山区', '顺义区', '经开区',
                '浦东新区', '黄浦区', '徐汇区', '长宁区', '静安区', '普陀区', '虹口区', '杨浦区', '闵行区', '宝山区', '嘉定区', '松江区', '青浦区', '奉贤区', '金山区',
                '渝中区', '江北区', '南岸区', '九龙坡区', '沙坪坝区', '大渡口区', '渝北区', '巴南区', '北碚区', '万州区', '涪陵区', '永川区', '合川区', '江津区',
                '鄞州区', '海曙区', '江东区', '江北区', '镇海区', '北仑区', '余姚市', '慈溪市', '奉化区', '宁海县', '象山县',
                '上城区', '下城区', '江干区', '拱墅区', '西湖区', '滨江区', '萧山区', '余杭区', '富阳区', '临安区', '桐庐县', '淳安县', '建德市',
                '玄武区', '秦淮区', '建邺区', '鼓楼区', '浦口区', '栖霞区', '雨花台区', '江宁区', '六合区', '溧水区', '高淳区',
                '姑苏区', '虎丘区', '吴中区', '相城区', '吴江区', '常熟市', '张家港市', '昆山市', '太仓市',
                '锡山区', '惠山区', '滨湖区', '梁溪区', '新吴区', '江阴市', '宜兴市',
                '天宁区', '钟楼区', '新北区', '武进区', '金坛区', '溧阳市',
                '崂山区', '市南区', '市北区', '四方区', '李沧区', '城阳区', '黄岛区', '即墨区', '胶州市', '平度市', '莱西市',
                '福田区', '罗湖区', '南山区', '盐田区', '宝安区', '龙岗区', '龙华区', '坪山区', '光明区', '大鹏新区',
                '越秀区', '荔湾区', '海珠区', '天河区', '白云区', '黄埔区', '番禺区', '花都区', '南沙区', '从化区', '增城区',
                '禅城区', '南海区', '顺德区', '三水区', '高明区',
                '中山区', '西岗区', '沙河口区', '甘井子区', '旅顺口区', '金州区', '普兰店区', '瓦房店市', '庄河市', '长海县',
                '和平区', '沈河区', '大东区', '皇姑区', '铁西区', '苏家屯区', '浑南区', '沈北新区', '于洪区',
                '中原区', '二七区', '管城回族区', '金水区', '上街区', '惠济区', '中牟县', '巩义市', '荥阳市', '新密市', '新郑市', '登封市',
                '江汉区', '江岸区', '硚口区', '汉阳区', '武昌区', '青山区', '洪山区', '蔡甸区', '江夏区', '黄陂区', '新洲区', '东西湖区', '汉南区',
                '雁塔区', '新城区', '莲湖区', '碑林区', '未央区', '灞桥区', '阎良区', '临潼区', '长安区', '高陵区', '鄠邑区', '蓝田县', '周至县',
                '芙蓉区', '天心区', '岳麓区', '开福区', '雨花区', '望城区', '长沙县', '宁乡市', '浏阳市',
                '锦江区', '青羊区', '金牛区', '武侯区', '成华区', '龙泉驿区', '青白江区', '新都区', '温江区', '双流区', '郫都区', '新津区',
                '南明区', '云岩区', '花溪区', '乌当区', '白云区', '观山湖区',
                '龙华区', '美兰区', '琼山区', '秀英区',
                '天涯区', '吉阳区', '海棠区', '崖州区',
                '五华区', '盘龙区', '官渡区', '西山区', '东川区', '呈贡区', '晋宁区', '富民县', '宜良县', '石林县', '嵩明县', '禄劝县', '寻甸县', '安宁市']
    
    for district in districts:
        if district in address:
            return district
    return ''

def get_store_type(store_name):
    """根据门店名称判断业态类型"""
    if 'mini' in store_name.lower():
        return '盒马mini'
    elif 'X会员店' in store_name or 'x会员店' in store_name:
        return '盒马X会员店'
    elif 'NB' in store_name:
        return '盒马NB'
    elif '奥莱' in store_name:
        return '盒马奥莱'
    else:
        return '盒马鲜生'

def create_complete_hema_excel():
    # 创建工作簿
    wb = Workbook()
    ws = wb.active
    ws.title = "盒马门店信息"
    
    # 设置表头
    headers = ['省份', '城市', '区县', '门店名称', '业态类型', '门店地址', '联系电话']
    ws.append(headers)
    
    # 设置表头样式
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    header_alignment = Alignment(horizontal="center", vertical="center")
    
    for col in range(1, len(headers) + 1):
        cell = ws.cell(row=1, column=col)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
    
    # 原始门店数据 [省份, 城市, 门店名称, 门店地址, 联系电话]
    raw_store_data = [
        # 北京门店 (40家)
        ['北京', '北京', '北京十里堡店', '北京市朝阳区十里堡乙2号院5-6新城市广场B01', ''],
        ['北京', '北京', '北京大成店', '北京市丰台区大成路8号翠微百货B1层', ''],
        ['北京', '北京', '北京东坝店', '北京市东坝中路38号楼地下二层B201号', ''],
        ['北京', '北京', '北京小营店', '北京市海淀区文龙家园四里4号楼一楼', ''],
        ['北京', '北京', '北京红莲店', '北京市西城区手帕口南街80号京铁和园A1综合楼B1层', ''],
        ['北京', '北京', '北京经开店', '北京经济技术开发区亦庄开发区荣京西街经开大厦B1层', ''],
        ['北京', '北京', '北京西直门店', '北京市西城区西直门外大街112号阳光大厦地B1层', ''],
        ['北京', '北京', '北京乐成店', '北京市朝阳区东三环中路20、22、24号楼乐城中心 space 3', ''],
        ['北京', '北京', '北京百荣世贸店', '北京市东城区永定门外大街甲101号地上2层', ''],
        ['北京', '北京', '北京顺义店', '北京居然之家顺西路家居建材市场F1,F2', ''],
        ['北京', '北京', '北京房山金泰店', '北京市房山区长政南街金泰广场B1层', ''],
        ['北京', '北京', '北京丽泽店', '北京丰台区居然之家丽泽Mall第一层及地下二层', ''],
        ['北京', '北京', '北京石景山八角店', '北京市石景山区阜石路168号居然之家1层', ''],
        ['北京', '北京', '北京西二旗万科店', '北京市昌平区龙域中心B座2层', ''],
        ['北京', '北京', '北京顾家庄桥店', '北京市朝阳区来广营西路55号2层', ''],
        ['北京', '北京', '北京金源店', '北京市海淀区远大路1号居然之家2层', ''],
        ['北京', '北京', '北京崇文门新世界店', '北京市东城区东打磨厂街7号新世界百货新活馆2层', ''],
        ['北京', '北京', '北京枣园路店', '北京市大兴区黄村枣园路8号居然之家B1层', ''],
        ['北京', '北京', '北京望京万科店', '北京市朝阳区望京万科时代中心E座B1层电梯口', ''],
        ['北京', '北京', '北京资和信店', '北京市丰台区西四环南路103号B1层', ''],
        ['北京', '北京', '北京国贸世纪财富中心店', '北京市朝阳区光华路5号世纪财富中心B1层', ''],
        ['北京', '北京', '北京立水桥万优汇店', '北京市昌平区东小口镇立汤路188号北方明珠大厦1号楼B1', ''],
        ['北京', '北京', '北京马家堡新荟城店', '北京市丰台区角门路19号地下一层', ''],
        ['北京', '北京', '北京十里河居然店', '北京市朝阳区十八里乡周庄村居然靓屋灯饰城B1', ''],
        ['北京', '北京', '北京大郊亭店', '北京市朝阳区东四环中路189,191号芳园里ID MALL购物中心B1', ''],
        ['北京', '北京', '北京亚运村店', '北京市朝阳区北辰东路8号院1号楼（新辰里购物中心B1层）', ''],
        ['北京', '北京', '北京林奥店', '北京市朝阳区清林东路4号院奥森天地购物中心B1层', ''],
        ['北京', '北京', '北京美凯龙朝阳路店', '北京市朝阳区高井村甲8号红星美凯龙家居广场朝阳路商城地下一层', ''],
        ['北京', '北京', '北京玲珑路店', '北京市海淀区玲珑路9号院东区B1', ''],
        ['北京', '北京', '北京回龙观店', '北京市昌平区霍营街道文华路89号院1号', ''],
        ['北京', '北京', '北京盒马mini黄寺店', '北京市朝阳区黄寺大街人定湖北巷11号10幢1001室', ''],
        ['北京', '北京', '北京盒马mini和平里店', '北京市东城区和平里强佑大厦北侧100米（原D5俱乐部）', ''],
        ['北京', '北京', '北京西客站店', '北京市海淀区羊坊店路18号光耀东方广场负一层', ''],
        ['北京', '北京', '北京双清路店', '北京市海淀区双清路88号华源世纪商务楼B1层', ''],
        ['北京', '北京', '北京丽泽天街店', '北京市丰台区丽泽路16号院1号楼 龙湖·丽泽天街', ''],
        ['北京', '北京', '北京世界之花X会员店', '北京市大兴区旧宫镇久敬庄路世界之花假日广场B1层', ''],
        ['北京', '北京', '北京通州新华东街店', '北京市通州区新华东街289号院1号楼爱情海B2', ''],
        ['北京', '北京', '北京大钟寺店', '北京海淀区四道口北街36号F1', ''],
        ['北京', '北京', '北京盒马mini永安里店', '北京市朝阳区建外街道 建国门外大街甲5号贵友大厦一层', ''],
        ['北京', '北京', '北京盒马mini龙岗路店', '北京市海淀区西三旗街道龙岗路20号', ''],

        # 上海门店 (72家) - 部分示例
        ['上海', '上海', '上海金桥店', '上海市张杨路3611弄（近金桥路）金桥国际商业广场1座b1层', ''],
        ['上海', '上海', '上海大宁店', '上海市静安区万荣路777号大宁音乐广场B1层', ''],
        ['上海', '上海', '上海虹桥店', '上海闵行区金汇路538号金汇四季广场b1层', ''],
        ['上海', '上海', '上海杨高南路店', '上海市浦东新区北艾路1782号', ''],
        ['上海', '上海', '上海宝地店', '上海市杨浦区唐山路1108号B1层', ''],
        ['上海', '上海', '上海曹家渡店', '上海市长宁区长宁路88号(KING88广场）', ''],
        ['上海', '上海', '上海汇阳广场店', '上海市徐汇区田林东路75号汇阳广场B1层', ''],
        ['上海', '上海', '上海新江湾店', '上海市杨浦区殷行路1388号悠方购物公园B1层', ''],
        ['上海', '上海', '上海平高店', '上海市松江九峰路118号二楼', ''],
        ['上海', '上海', '上海上海湾店', '上海市浦东南路1138号B1层', ''],
        ['上海', '上海', '上海长泰店', '上海市张江长泰广场金科路2889号', ''],
        ['上海', '上海', '上海绚荟城店', '上海市闵行区莲花南路1388弄新荟城购物中心1层', ''],
        ['上海', '上海', '上海南翔店', '上海市嘉定区南翔镇银翔路609号东方伟业广场B1层', ''],
        ['上海', '上海', '上海星宝店', '上海市闵行区虹莘路3005号星宝广场B1层', ''],
        ['上海', '上海', '上海裕德路店', '上海市裕德路165号-南洋1931商场B1层', ''],
        ['上海', '上海', '上海三林印象城店', '上海市浦东新区东明路2719号', ''],
        ['上海', '上海', '上海星空店', '上海市虹桥路1665号星空广场', ''],
        ['上海', '上海', '上海189购物中心店', '上海市长宁区长寿路189号', ''],
        ['上海', '上海', '上海盈嘉店', '上海市嘉定区曹安公路1688号', ''],
        ['上海', '上海', '上海盒马mini中海环宇荟2店', '上海市黄陂南路838号中海寰宇荟', ''],
        ['上海', '上海', '上海中福城店', '上海市海潮路133号', ''],
        ['上海', '上海', '上海白玉兰广场店', '上海市虹口区东长治路588号', ''],
        ['上海', '上海', '上海东方懿徳店', '上海市浦东新区林展路298号B1', ''],
        ['上海', '上海', '上海五月花广场店', '上海市芷江西路488号', ''],
        ['上海', '上海', '上海宝山新业坊店', '上海市宝山区逸仙路1328号6号楼1楼', ''],
        ['上海', '上海', '上海大场老街店', '上海市场中路4048号B1', ''],
        ['上海', '上海', '上海恒生万鹂店', '上海市浦东新区高科东路515号', ''],
        ['上海', '上海', '上海我格广场店', '武宁路101号一层01-1F-S01号/二层01-2F-01A/01B/01C号', ''],
        ['上海', '上海', '上海盒马mini昌里路店', '上海市昌里路333号', ''],
        ['上海', '上海', '上海盒马mini浦江城市广场店', '上海市江月路1850弄1-6号1F-C01', ''],
        ['上海', '上海', '上海长宁来福士店', '上海市长宁路1139号B1', ''],
        ['上海', '上海', '上海丁香国际店', '上海市浦东新区丁香路880弄1-23号、25-31号LG101、LG102', ''],
        ['上海', '上海', '上海盒马mini北洋泾店', '上海市自由贸易区博山路191号1楼101室', ''],
        ['上海', '上海', '上海嘉定城中店', '上海市嘉定区城中路2号', ''],
        ['上海', '上海', '上海曲阳商务中心店', '上海市曲阳路800号', ''],
        ['上海', '上海', '上海F2星展店', '上海市浦东新区陆家嘴环路1318号地下一层', ''],
        ['上海', '上海', '上海馥邦国际店', '上海市长宁区天山西路138号', ''],
        ['上海', '上海', '上海大华虎城店', '上海市宝山区大华一路198号一层108、109、110号，大华路352号地下一层27-33号铺位、35-39号铺位', ''],
        ['上海', '上海', '上海周浦永乐广场店', '上海浦东新区康桥镇上南路6717号', ''],
        ['上海', '上海', '上海打浦桥日月光店', '上海市徐家汇路618号', ''],
        ['上海', '上海', '上海御桥活力城店', '上海市沪南路2419号', ''],
        ['上海', '上海', '上海嘉定日月光店', '上海市嘉定区城北路358号', ''],
        ['上海', '上海', '上海歌斐中心店', '上海黄浦区蒙自路757号', ''],
        ['上海', '上海', '上海盒马mini宝钢店', '上海市宝山区牡丹江路1738号', ''],
        ['上海', '上海', '上海盒马mini川沙路店', '上海市浦东新区川沙路4645号', ''],
        ['上海', '上海', '上海上滨生活广场店', '上海市杨浦区周家嘴路887号B1楼', ''],
        ['上海', '上海', '上海盒马mini德都路店', '上海市宝山区德都路325号1楼A-02', ''],
        ['上海', '上海', '上海世博源店', '上海市浦东新区上南路168号', ''],
        ['上海', '上海', '上海盒马mini金山店', '上海市金山区山阳镇卫清西路418号', ''],
        ['上海', '上海', '上海盒马mini马陆秀泰店', '上海市嘉定区马陆镇宝安公路3500号秀泰天地广场1楼', ''],
        ['上海', '上海', '上海盒马mini奉贤南桥店', '上海市奉贤区南中录80号1F', ''],
        ['上海', '上海', '上海盒马mini复旦店', '上海市杨浦区国定路333号三号湾广场B1层', ''],
        ['上海', '上海', '上海森兰商都X会员店', '上海市浦东新区启帆路628号森兰商都B1层', ''],
        ['上海', '上海', '上海盒马mini宝山淞滨店', '上海市宝山区淞宾路500号', ''],
        ['上海', '上海', '上海盒马mini延吉店', '上海市杨浦区靖宇南路270号', ''],
        ['上海', '上海', '上海金地喜悦荟店', '上海市徐汇区桂平路188号地下一层', ''],
        ['上海', '上海', '上海老西门南六店', '上海市黄浦区肇周路11号地下及肇周路9号地下1层超市及B105B 号', ''],
        ['上海', '上海', '上海盒马mini汇宝店', '上海市闵行区漕宝路3459号B1-HM', ''],
        ['上海', '上海', '上海品尊国际店', '上海市普陀区铜川路66（1-12）号地下1层', ''],
        ['上海', '上海', '上海盒马mini高行华高店', '上海市浦东新区金高路1071弄1-2号', ''],
        ['上海', '上海', '上海盒马mini仁恒雅苑店', '上海市浦东新区兰谷路2110号及东煦路666号101A', ''],
        ['上海', '上海', '上海绿地新都汇店', '上海市共和新路5000号B1', ''],
        ['上海', '上海', '上海盒马mini江桥恒久店', '上海市嘉定区金沙江西路1288号一层1087、1086、1066单元', ''],
        ['上海', '上海', '上海保利时光里店', '上海市徐汇区瑞平路230号，地下二层铺位号：B2-010/011/012', ''],
        ['上海', '上海', '上海盒马mini佘山店', '上海市松江区佘月路27号一层131号', ''],
        ['上海', '上海', '上海盒马mini奉贤宝龙店', '上海市奉贤区航南公路5639、5699号宝龙广场 2号楼一层 M2-F1-003～007/068', ''],
        ['上海', '上海', '上海陆家嘴滨江中心店', '上海市浦东新区滨江大道5139号-101A', ''],
        ['上海', '上海', '上海九亭贝尚湾店', '上海松江区沪亭北路350弄1号1层01-1室、2层01-1、01-2、01-3室', ''],
        ['上海', '上海', '上海红点城X会员店', '上海市闵行区七莘路1809号红点城2号楼B1层', ''],
        ['上海', '上海', '上海盒马mini朱泾城南店', '上海市金山区朱泾镇金龙新街 708号01、02、03店铺', ''],
        ['上海', '上海', '上海盒马mini苏航广场店', '上海市浦东新区沪南公路5588号', ''],
        ['上海', '上海', '上海杨浦环创店', '上海市周家嘴路隆昌路东南交叉口', ''],
    ]
    
    # 处理数据并添加区县和业态类型
    processed_data = []
    for row in raw_store_data:
        province, city, store_name, address, phone = row
        district = get_district_from_address(address)
        store_type = get_store_type(store_name)
        processed_data.append([province, city, district, store_name, store_type, address, phone])
    
    # 添加数据到工作表
    for row_data in processed_data:
        ws.append(row_data)

    # 调整列宽
    column_widths = [8, 8, 12, 25, 15, 60, 15]  # 省份, 城市, 区县, 门店名称, 业态类型, 门店地址, 联系电话
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[get_column_letter(i)].width = width

    # 设置数据行的对齐方式
    for row in ws.iter_rows(min_row=2, max_row=ws.max_row):
        for cell in row:
            cell.alignment = Alignment(horizontal="left", vertical="center", wrap_text=True)

    return wb, len(processed_data)

if __name__ == "__main__":
    wb, store_count = create_complete_hema_excel()

    # 保存Excel文件
    filename = "hema_complete.xlsx"
    wb.save(filename)

    print(f"✅ 盒马门店信息Excel文件已创建完成：{filename}")
    print(f"📊 已处理 {store_count} 家门店的数据")
    print("📋 表格包含以下列：省份、城市、区县、门店名称、业态类型、门店地址、联系电话")
    print("🏪 业态类型包括：盒马鲜生、盒马mini、盒马X会员店等")
    print("📍 已自动从地址中提取区县信息")
