<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 严谨MACD计算验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
            margin: 0;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
        }
        .test-section {
            background: #333;
            margin: 15px 0;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #4caf50;
        }
        .test-section h3 {
            margin: 0 0 10px 0;
            color: #4caf50;
        }
        .data-display {
            background: #000;
            color: #0f0;
            padding: 15px;
            font-family: monospace;
            font-size: 11px;
            border-radius: 4px;
            margin: 10px 0;
            max-height: 400px;
            overflow-y: auto;
        }
        .btn {
            background: #4caf50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 12px;
        }
        .btn:hover { background: #45a049; }
        .btn.primary { background: #2196f3; }
        .btn.warning { background: #ff9800; }
        .btn.danger { background: #f44336; }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
            background: #333;
        }
        .comparison-table th,
        .comparison-table td {
            border: 1px solid #555;
            padding: 8px;
            text-align: center;
            font-size: 11px;
        }
        .comparison-table th {
            background: #444;
            color: #ffc107;
        }
        .valid-data { color: #4caf50; }
        .invalid-data { color: #f44336; }
        .calculation-info {
            background: #444;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #2196f3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 严谨MACD计算验证工具</h1>
        <p style="color: #ffc107;">验证基于足够历史数据计算的MACD指标准确性</p>
        
        <div class="test-section">
            <h3>📊 数据获取策略验证</h3>
            <button class="btn" onclick="testDataStrategy()">🔍 测试数据策略</button>
            <button class="btn primary" onclick="compareCalculations()">⚖️ 对比计算方法</button>
            <button class="btn warning" onclick="validateMACDFormula()">🧮 验证MACD公式</button>
            <div id="strategy-results" class="data-display">
                等待测试数据获取策略...
            </div>
        </div>

        <div class="test-section">
            <h3>📈 MACD数据质量分析</h3>
            <div id="quality-analysis">
                <!-- 质量分析结果将在这里显示 -->
            </div>
        </div>

        <div class="test-section">
            <h3>🎯 索引对应关系验证</h3>
            <button class="btn" onclick="testIndexAlignment()">🎯 测试索引对齐</button>
            <button class="btn primary" onclick="testSpecificIndex(29)">测试索引29</button>
            <button class="btn primary" onclick="testSpecificIndex(50)">测试索引50</button>
            <div id="alignment-results" class="data-display">
                等待索引对齐测试...
            </div>
        </div>

        <div class="test-section">
            <h3>📋 详细数据对比表</h3>
            <div id="comparison-table-container">
                <!-- 对比表将在这里显示 -->
            </div>
        </div>

        <div class="test-section">
            <h3>✅ 严谨性验证结果</h3>
            <button class="btn" onclick="runFullRigorousTest()">🚀 运行完整严谨测试</button>
            <button class="btn danger" onclick="clearAllResults()">清除所有结果</button>
            <div id="rigorous-results">
                <!-- 严谨性验证结果将在这里显示 -->
            </div>
        </div>
    </div>

    <script>
        let testData = {};

        function addLog(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const timestamp = new Date().toLocaleTimeString();
            const typeIcon = {
                'success': '✅',
                'error': '❌', 
                'warning': '⚠️',
                'info': 'ℹ️'
            };
            container.innerHTML += `[${timestamp}] ${typeIcon[type]} ${message}<br>`;
            container.scrollTop = container.scrollHeight;
        }

        function clearAllResults() {
            ['strategy-results', 'alignment-results'].forEach(id => {
                document.getElementById(id).innerHTML = '已清除...<br>';
            });
            document.getElementById('quality-analysis').innerHTML = '';
            document.getElementById('comparison-table-container').innerHTML = '';
            document.getElementById('rigorous-results').innerHTML = '';
        }

        async function testDataStrategy() {
            addLog('strategy-results', '🔍 开始测试数据获取策略...', 'info');
            
            try {
                // 测试不同数量的数据获取
                const tests = [
                    { count: 30, desc: '前端显示30个' },
                    { count: 50, desc: '前端显示50个' },
                    { count: 100, desc: '前端显示100个' }
                ];

                for (const test of tests) {
                    addLog('strategy-results', `测试${test.desc}数据点...`, 'info');
                    
                    const response = await fetch(`http://localhost:5001/api/indicators/000001?period=1d&indicators=macd&count=${test.count}`);
                    const result = await response.json();
                    
                    if (result.success) {
                        const macdData = result.data.indicators.macd;
                        const calcInfo = result.data.calculation_info;
                        
                        addLog('strategy-results', `${test.desc}: 返回${macdData.dif.length}个MACD值`, 'success');
                        addLog('strategy-results', `  计算基础: ${calcInfo.total_data_points}个历史数据点`, 'info');
                        addLog('strategy-results', `  显示数据: ${calcInfo.display_data_points}个数据点`, 'info');
                        
                        // 检查数据质量
                        const validMacd = macdData.macd.filter(v => v !== null).length;
                        const validityRate = (validMacd / macdData.macd.length * 100).toFixed(1);
                        addLog('strategy-results', `  数据有效率: ${validityRate}%`, validityRate > 80 ? 'success' : 'warning');
                        
                        testData[test.count] = { macdData, calcInfo };
                    } else {
                        addLog('strategy-results', `${test.desc}: 获取失败 - ${result.error}`, 'error');
                    }
                }
                
            } catch (error) {
                addLog('strategy-results', `测试失败: ${error.message}`, 'error');
            }
        }

        async function compareCalculations() {
            if (Object.keys(testData).length === 0) {
                addLog('strategy-results', '请先运行数据策略测试', 'warning');
                return;
            }

            addLog('strategy-results', '⚖️ 对比不同数据量的计算结果...', 'info');
            
            // 对比最后几个MACD值是否一致
            const counts = Object.keys(testData).map(Number).sort((a, b) => a - b);
            const lastIndex = Math.min(...counts.map(count => testData[count].macdData.macd.length)) - 1;
            
            addLog('strategy-results', `对比索引${lastIndex}的MACD值:`, 'info');
            
            let allSame = true;
            let referenceValue = null;
            
            for (const count of counts) {
                const macdValue = testData[count].macdData.macd[lastIndex];
                if (referenceValue === null) {
                    referenceValue = macdValue;
                }
                
                const isSame = Math.abs(macdValue - referenceValue) < 0.0001;
                if (!isSame) allSame = false;
                
                addLog('strategy-results', `  ${count}个数据点: ${macdValue?.toFixed(4) || 'null'}`, isSame ? 'success' : 'error');
            }
            
            if (allSame) {
                addLog('strategy-results', '✅ 所有计算结果一致，验证通过！', 'success');
            } else {
                addLog('strategy-results', '❌ 计算结果不一致，需要检查算法', 'error');
            }
        }

        async function validateMACDFormula() {
            if (Object.keys(testData).length === 0) {
                addLog('strategy-results', '请先运行数据策略测试', 'warning');
                return;
            }

            addLog('strategy-results', '🧮 验证MACD计算公式...', 'info');
            
            // 使用100个数据点的结果进行验证
            const macdData = testData[100]?.macdData;
            if (!macdData) {
                addLog('strategy-results', '缺少100个数据点的测试结果', 'error');
                return;
            }

            let formulaErrors = 0;
            let totalTests = 0;
            
            // 验证最后20个数据点的公式
            const startIndex = Math.max(0, macdData.dif.length - 20);
            
            for (let i = startIndex; i < macdData.dif.length; i++) {
                const dif = macdData.dif[i];
                const dea = macdData.dea[i];
                const macd = macdData.macd[i];
                
                if (dif !== null && dea !== null && macd !== null) {
                    totalTests++;
                    const expectedMacd = (dif - dea) * 2;
                    const diff = Math.abs(macd - expectedMacd);
                    
                    if (diff > 0.0001) {
                        formulaErrors++;
                        addLog('strategy-results', `公式错误[${i}]: 期望${expectedMacd.toFixed(4)}, 实际${macd.toFixed(4)}`, 'error');
                    }
                }
            }
            
            const accuracy = ((totalTests - formulaErrors) / totalTests * 100).toFixed(1);
            addLog('strategy-results', `公式验证: ${totalTests - formulaErrors}/${totalTests} 正确 (${accuracy}%)`, formulaErrors === 0 ? 'success' : 'warning');
        }

        async function testIndexAlignment() {
            addLog('alignment-results', '🎯 测试索引对齐关系...', 'info');
            
            try {
                // 获取K线数据和指标数据
                const [klineResponse, indicatorResponse] = await Promise.all([
                    fetch('http://localhost:5001/api/kline/000001?period=1d&count=100'),
                    fetch('http://localhost:5001/api/indicators/000001?period=1d&indicators=macd&count=100')
                ]);

                const klineResult = await klineResponse.json();
                const indicatorResult = await indicatorResponse.json();

                if (klineResult.success && indicatorResult.success) {
                    const klines = klineResult.data.klines;
                    const macdData = indicatorResult.data.indicators.macd;
                    
                    addLog('alignment-results', `K线数据: ${klines.length}个`, 'info');
                    addLog('alignment-results', `MACD数据: ${macdData.dif.length}个`, 'info');
                    
                    if (klines.length === macdData.dif.length) {
                        addLog('alignment-results', '✅ 数据长度完全对齐', 'success');
                        
                        // 测试几个关键索引的对应关系
                        const testIndices = [29, 50, 70, 90];
                        for (const index of testIndices) {
                            if (index < klines.length) {
                                const kline = klines[index];
                                const dif = macdData.dif[index];
                                const dea = macdData.dea[index];
                                const macd = macdData.macd[index];
                                
                                addLog('alignment-results', `索引${index}: 日期=${kline.date}, 收盘=${kline.close}`, 'info');
                                addLog('alignment-results', `  MACD: DIF=${dif?.toFixed(4) || 'null'}, DEA=${dea?.toFixed(4) || 'null'}, MACD=${macd?.toFixed(4) || 'null'}`, 'info');
                            }
                        }
                    } else {
                        addLog('alignment-results', '❌ 数据长度不对齐', 'error');
                    }
                } else {
                    addLog('alignment-results', '数据获取失败', 'error');
                }
                
            } catch (error) {
                addLog('alignment-results', `测试失败: ${error.message}`, 'error');
            }
        }

        async function testSpecificIndex(index) {
            addLog('alignment-results', `🎯 测试特定索引 ${index}...`, 'info');
            
            try {
                const response = await fetch(`http://localhost:5001/api/indicators/000001?period=1d&indicators=macd&count=100`);
                const result = await response.json();
                
                if (result.success) {
                    const macdData = result.data.indicators.macd;
                    
                    if (index < macdData.dif.length) {
                        const dif = macdData.dif[index];
                        const dea = macdData.dea[index];
                        const macd = macdData.macd[index];
                        
                        addLog('alignment-results', `索引${index}数据:`, 'info');
                        addLog('alignment-results', `  DIF: ${dif !== null ? dif.toFixed(4) : 'null'}`, dif !== null ? 'success' : 'warning');
                        addLog('alignment-results', `  DEA: ${dea !== null ? dea.toFixed(4) : 'null'}`, dea !== null ? 'success' : 'warning');
                        addLog('alignment-results', `  MACD: ${macd !== null ? macd.toFixed(4) : 'null'}`, macd !== null ? 'success' : 'warning');
                        
                        if (dif !== null && dea !== null && macd !== null) {
                            const expected = (dif - dea) * 2;
                            const diff = Math.abs(macd - expected);
                            addLog('alignment-results', `  公式验证: 期望${expected.toFixed(4)}, 差异${diff.toFixed(6)}`, diff < 0.0001 ? 'success' : 'error');
                        }
                    } else {
                        addLog('alignment-results', `索引${index}超出数据范围`, 'error');
                    }
                } else {
                    addLog('alignment-results', `获取数据失败: ${result.error}`, 'error');
                }
                
            } catch (error) {
                addLog('alignment-results', `测试失败: ${error.message}`, 'error');
            }
        }

        async function runFullRigorousTest() {
            const resultsContainer = document.getElementById('rigorous-results');
            resultsContainer.innerHTML = '<h4>🚀 严谨性验证报告</h4>';
            
            try {
                // 1. 数据获取策略测试
                await testDataStrategy();
                
                // 2. 计算一致性验证
                await compareCalculations();
                
                // 3. 公式准确性验证
                await validateMACDFormula();
                
                // 4. 索引对齐验证
                await testIndexAlignment();
                
                resultsContainer.innerHTML += '<div style="color: #4caf50; font-weight: bold; margin-top: 20px;">🎉 严谨性验证完成！请查看各项测试结果。</div>';
                
            } catch (error) {
                resultsContainer.innerHTML += `<div style="color: #f44336;">❌ 验证过程出错: ${error.message}</div>`;
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            addLog('strategy-results', '🎯 严谨MACD计算验证工具已加载', 'info');
            addLog('strategy-results', '点击"测试数据策略"开始验证...', 'info');
        });
    </script>
</body>
</html>
