<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 索引29专项测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
            margin: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
        }
        .result {
            background: #333;
            padding: 15px;
            margin: 10px 0;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
        }
        .success { border-left: 4px solid #4caf50; }
        .error { border-left: 4px solid #f44336; }
        .warning { border-left: 4px solid #ff9800; }
        .info { border-left: 4px solid #2196f3; }
        .btn {
            background: #4caf50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #45a049; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 索引29专项测试</h1>
        <p>专门测试索引29位置的MACD数据是否正确显示</p>
        
        <button class="btn" onclick="testIndex29()">🚀 测试索引29</button>
        <button class="btn" onclick="testSurroundingIndices()">🔍 测试周围索引</button>
        
        <div id="results"></div>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const container = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            container.appendChild(div);
        }

        async function testIndex29() {
            addResult('🎯 开始测试索引29...', 'info');
            
            try {
                const response = await fetch('http://localhost:5001/api/indicators/000001?period=1d&indicators=macd&count=100');
                const result = await response.json();
                
                if (result.success) {
                    const macdData = result.data.indicators.macd;
                    const index = 29;
                    
                    if (index < macdData.dif.length) {
                        const dif = macdData.dif[index];
                        const dea = macdData.dea[index];
                        const macd = macdData.macd[index];
                        
                        addResult(`索引29数据:`, 'info');
                        addResult(`  DIF: ${dif !== null ? dif.toFixed(4) : 'null'}`, dif !== null ? 'success' : 'warning');
                        addResult(`  DEA: ${dea !== null ? dea.toFixed(4) : 'null'}`, dea !== null ? 'success' : 'warning');
                        addResult(`  MACD: ${macd !== null ? macd.toFixed(4) : 'null'}`, macd !== null ? 'success' : 'warning');
                        
                        // 验证公式
                        if (dif !== null && dea !== null && macd !== null) {
                            const expected = (dif - dea) * 2;
                            const diff = Math.abs(macd - expected);
                            addResult(`  公式验证: 期望${expected.toFixed(4)}, 实际${macd.toFixed(4)}, 差异${diff.toFixed(6)}`, 
                                     diff < 0.0001 ? 'success' : 'error');
                        }
                        
                        // 检查数据状态
                        if (dif !== null && dea !== null && macd !== null) {
                            addResult('✅ 索引29数据完整，应该正常显示', 'success');
                        } else {
                            addResult('⚠️ 索引29数据不完整，可能显示为"--"', 'warning');
                        }
                        
                    } else {
                        addResult(`❌ 索引29超出数据范围 (总长度: ${macdData.dif.length})`, 'error');
                    }
                    
                    // 显示计算信息
                    if (result.data.calculation_info) {
                        const info = result.data.calculation_info;
                        addResult(`计算信息: 基于${info.total_data_points}个数据点计算，返回${info.display_data_points}个`, 'info');
                    }
                    
                } else {
                    addResult(`❌ 获取数据失败: ${result.error}`, 'error');
                }
                
            } catch (error) {
                addResult(`❌ 测试失败: ${error.message}`, 'error');
            }
        }

        async function testSurroundingIndices() {
            addResult('🔍 测试索引29周围的数据...', 'info');
            
            try {
                const response = await fetch('http://localhost:5001/api/indicators/000001?period=1d&indicators=macd&count=100');
                const result = await response.json();
                
                if (result.success) {
                    const macdData = result.data.indicators.macd;
                    
                    // 测试索引25-35
                    for (let i = 25; i <= 35; i++) {
                        if (i < macdData.dif.length) {
                            const dif = macdData.dif[i];
                            const dea = macdData.dea[i];
                            const macd = macdData.macd[i];
                            
                            const difStr = dif !== null ? dif.toFixed(4) : 'null';
                            const deaStr = dea !== null ? dea.toFixed(4) : 'null';
                            const macdStr = macd !== null ? macd.toFixed(4) : 'null';
                            
                            const hasData = dif !== null && dea !== null && macd !== null;
                            addResult(`[${i}] DIF:${difStr} DEA:${deaStr} MACD:${macdStr}`, hasData ? 'success' : 'warning');
                        }
                    }
                    
                    // 统计有效数据的起始位置
                    let firstValidIndex = -1;
                    for (let i = 0; i < macdData.macd.length; i++) {
                        if (macdData.macd[i] !== null) {
                            firstValidIndex = i;
                            break;
                        }
                    }
                    
                    if (firstValidIndex >= 0) {
                        addResult(`第一个有效MACD值出现在索引: ${firstValidIndex}`, 'info');
                        if (firstValidIndex <= 29) {
                            addResult('✅ 索引29应该有有效的MACD值', 'success');
                        } else {
                            addResult('⚠️ 索引29可能没有有效的MACD值', 'warning');
                        }
                    }
                    
                } else {
                    addResult(`❌ 获取数据失败: ${result.error}`, 'error');
                }
                
            } catch (error) {
                addResult(`❌ 测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后自动测试
        document.addEventListener('DOMContentLoaded', () => {
            addResult('🎯 索引29专项测试工具已加载', 'info');
            setTimeout(testIndex29, 1000);
        });
    </script>
</body>
</html>
