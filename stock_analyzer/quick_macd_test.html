<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>⚡ MACD快速验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
            margin: 0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
        }
        .test-result {
            background: #333;
            padding: 15px;
            margin: 10px 0;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
        }
        .success { border-left: 4px solid #4caf50; }
        .error { border-left: 4px solid #f44336; }
        .warning { border-left: 4px solid #ff9800; }
        .info { border-left: 4px solid #2196f3; }
        .btn {
            background: #4caf50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #45a049; }
        .btn.primary { background: #2196f3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>⚡ MACD快速验证工具</h1>
        
        <button class="btn" onclick="quickTest()">🚀 快速测试</button>
        <button class="btn primary" onclick="detailedTest()">🔍 详细测试</button>
        <button class="btn" onclick="clearResults()">清除结果</button>
        
        <div id="results"></div>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const container = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            container.appendChild(div);
            container.scrollTop = container.scrollHeight;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function quickTest() {
            addResult('🚀 开始快速MACD测试...', 'info');
            
            try {
                // 测试不同数据量
                const tests = [30, 50, 100];
                const results = {};
                
                for (const count of tests) {
                    addResult(`测试${count}个数据点...`, 'info');
                    
                    const response = await fetch(`http://localhost:5001/api/indicators/000001?period=1d&indicators=macd&count=${count}`);
                    const result = await response.json();
                    
                    if (result.success) {
                        const macdData = result.data.indicators.macd;
                        const lastMacd = macdData.macd[macdData.macd.length - 1];
                        
                        results[count] = lastMacd;
                        addResult(`${count}个数据点: 最新MACD = ${lastMacd?.toFixed(4) || 'null'}`, lastMacd ? 'success' : 'warning');
                    } else {
                        addResult(`${count}个数据点: 获取失败 - ${result.error}`, 'error');
                    }
                }
                
                // 检查一致性
                const values = Object.values(results).filter(v => v !== null);
                if (values.length >= 2) {
                    const maxDiff = Math.max(...values) - Math.min(...values);
                    if (maxDiff < 0.001) {
                        addResult(`✅ 一致性检查通过: 最大差异 ${maxDiff.toFixed(6)}`, 'success');
                    } else {
                        addResult(`❌ 一致性检查失败: 最大差异 ${maxDiff.toFixed(6)}`, 'error');
                    }
                }
                
            } catch (error) {
                addResult(`❌ 测试失败: ${error.message}`, 'error');
            }
        }

        async function detailedTest() {
            addResult('🔍 开始详细MACD测试...', 'info');
            
            try {
                const response = await fetch('http://localhost:5001/api/indicators/000001?period=1d&indicators=macd&count=100');
                const result = await response.json();
                
                if (result.success) {
                    const macdData = result.data.indicators.macd;
                    
                    // 统计有效数据
                    const validMacd = macdData.macd.filter(v => v !== null);
                    const validDif = macdData.dif.filter(v => v !== null);
                    const validDea = macdData.dea.filter(v => v !== null);
                    
                    addResult(`数据统计: MACD有效${validMacd.length}个, DIF有效${validDif.length}个, DEA有效${validDea.length}个`, 'info');
                    
                    // 公式验证
                    let formulaErrors = 0;
                    let totalTests = 0;
                    
                    for (let i = Math.max(0, macdData.dif.length - 10); i < macdData.dif.length; i++) {
                        const dif = macdData.dif[i];
                        const dea = macdData.dea[i];
                        const macd = macdData.macd[i];
                        
                        if (dif !== null && dea !== null && macd !== null) {
                            totalTests++;
                            const expected = (dif - dea) * 2;
                            const diff = Math.abs(macd - expected);
                            
                            if (diff > 0.0001) {
                                formulaErrors++;
                                addResult(`公式错误[${i}]: 期望${expected.toFixed(4)}, 实际${macd.toFixed(4)}`, 'error');
                            }
                        }
                    }
                    
                    const accuracy = totalTests > 0 ? ((totalTests - formulaErrors) / totalTests * 100) : 0;
                    addResult(`公式验证: ${totalTests - formulaErrors}/${totalTests} 正确 (${accuracy.toFixed(1)}%)`, accuracy >= 95 ? 'success' : 'error');
                    
                    // 显示最新几个值
                    addResult('最新5个MACD值:', 'info');
                    for (let i = Math.max(0, macdData.macd.length - 5); i < macdData.macd.length; i++) {
                        const dif = macdData.dif[i];
                        const dea = macdData.dea[i];
                        const macd = macdData.macd[i];
                        
                        const difStr = dif !== null ? dif.toFixed(4) : 'null';
                        const deaStr = dea !== null ? dea.toFixed(4) : 'null';
                        const macdStr = macd !== null ? macd.toFixed(4) : 'null';
                        
                        addResult(`[${i}] DIF:${difStr} DEA:${deaStr} MACD:${macdStr}`, 'info');
                    }
                    
                    // 测试特定索引29
                    if (macdData.macd.length > 29) {
                        const index = 29;
                        const dif = macdData.dif[index];
                        const dea = macdData.dea[index];
                        const macd = macdData.macd[index];
                        
                        addResult(`索引29测试: DIF=${dif?.toFixed(4) || 'null'}, DEA=${dea?.toFixed(4) || 'null'}, MACD=${macd?.toFixed(4) || 'null'}`, 
                                 (dif && dea && macd) ? 'success' : 'warning');
                    }
                    
                } else {
                    addResult(`❌ 获取数据失败: ${result.error}`, 'error');
                }
                
            } catch (error) {
                addResult(`❌ 详细测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后自动运行快速测试
        document.addEventListener('DOMContentLoaded', () => {
            addResult('⚡ MACD快速验证工具已加载', 'info');
            setTimeout(quickTest, 1000); // 1秒后自动运行快速测试
        });
    </script>
</body>
</html>
