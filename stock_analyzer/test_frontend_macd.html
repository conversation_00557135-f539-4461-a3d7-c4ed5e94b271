<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 前端MACD数据测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
            margin: 0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
        }
        .test-section {
            background: #333;
            padding: 15px;
            margin: 15px 0;
            border-radius: 6px;
            border-left: 4px solid #2196f3;
        }
        .result {
            background: #444;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .success { border-left: 4px solid #4caf50; }
        .error { border-left: 4px solid #f44336; }
        .warning { border-left: 4px solid #ff9800; }
        .btn {
            background: #4caf50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #45a049; }
        .data-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin: 10px 0;
        }
        .data-item {
            background: #555;
            padding: 8px;
            border-radius: 4px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 前端MACD数据测试工具</h1>
        <p>测试前端是否能正确接收和显示MACD数据</p>
        
        <button class="btn" onclick="testApiDirectly()">🌐 直接测试API</button>
        <button class="btn" onclick="testFrontendFlow()">🔄 测试前端数据流</button>
        <button class="btn" onclick="testDataPanel()">📊 测试数据面板</button>
        
        <div class="test-section">
            <h3>📡 API直接测试</h3>
            <div id="apiResults"></div>
        </div>
        
        <div class="test-section">
            <h3>🔄 前端数据流测试</h3>
            <div id="frontendResults"></div>
        </div>
        
        <div class="test-section">
            <h3>📊 数据面板测试</h3>
            <div id="panelResults"></div>
        </div>
    </div>

    <script>
        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            container.appendChild(div);
        }

        async function testApiDirectly() {
            addResult('apiResults', '🌐 开始直接测试API...', 'info');
            
            try {
                const response = await fetch('http://localhost:5001/api/indicators/000001?period=1d&indicators=macd&count=30');
                const result = await response.json();
                
                if (result.success) {
                    const macdData = result.data.indicators.macd;
                    
                    addResult('apiResults', `✅ API调用成功`, 'success');
                    addResult('apiResults', `📊 MACD数据结构: ${Object.keys(macdData).join(', ')}`, 'info');
                    addResult('apiResults', `📊 数据长度: DIF=${macdData.dif.length}, DEA=${macdData.dea.length}, MACD=${macdData.macd.length}`, 'info');
                    
                    // 显示最新几个值
                    const lastIndex = macdData.dif.length - 1;
                    const last3 = [];
                    for (let i = Math.max(0, lastIndex - 2); i <= lastIndex; i++) {
                        last3.push({
                            index: i,
                            dif: macdData.dif[i],
                            dea: macdData.dea[i],
                            macd: macdData.macd[i]
                        });
                    }
                    
                    addResult('apiResults', '📊 最新3个MACD值:', 'info');
                    last3.forEach(item => {
                        const difStr = item.dif !== null ? item.dif.toFixed(4) : 'null';
                        const deaStr = item.dea !== null ? item.dea.toFixed(4) : 'null';
                        const macdStr = item.macd !== null ? item.macd.toFixed(4) : 'null';
                        addResult('apiResults', `[${item.index}] DIF:${difStr} DEA:${deaStr} MACD:${macdStr}`, 'info');
                    });
                    
                } else {
                    addResult('apiResults', `❌ API调用失败: ${result.error}`, 'error');
                }
                
            } catch (error) {
                addResult('apiResults', `❌ 网络错误: ${error.message}`, 'error');
            }
        }

        async function testFrontendFlow() {
            addResult('frontendResults', '🔄 开始测试前端数据流...', 'info');
            
            try {
                // 模拟前端的数据获取流程
                const queryCode = '000001';
                const period = '1d';
                const activeIndicators = ['volume', 'macd', 'kdj'];
                const activeMAs = [5, 10, 20];
                
                addResult('frontendResults', `📡 获取指标数据: ${queryCode}`, 'info');
                
                const indicatorResponse = await fetch(`http://localhost:5001/api/indicators/${queryCode}?period=${period}&mas=${activeMAs.join(',')}&indicators=${activeIndicators.join(',')}`);
                
                if (!indicatorResponse.ok) {
                    throw new Error(`指标数据API错误: ${indicatorResponse.status}`);
                }
                
                const indicatorResult = await indicatorResponse.json();
                
                if (indicatorResult.success) {
                    addResult('frontendResults', '✅ 前端数据流测试成功', 'success');
                    
                    const indicators = indicatorResult.data.indicators;
                    addResult('frontendResults', `📊 获取到的指标: ${Object.keys(indicators).join(', ')}`, 'info');
                    
                    if (indicators.macd) {
                        const macdData = indicators.macd;
                        addResult('frontendResults', `📊 MACD数据完整性检查:`, 'info');
                        addResult('frontendResults', `  - DIF数组: ${macdData.dif ? '✅' : '❌'} (长度: ${macdData.dif?.length || 0})`, macdData.dif ? 'success' : 'error');
                        addResult('frontendResults', `  - DEA数组: ${macdData.dea ? '✅' : '❌'} (长度: ${macdData.dea?.length || 0})`, macdData.dea ? 'success' : 'error');
                        addResult('frontendResults', `  - MACD数组: ${macdData.macd ? '✅' : '❌'} (长度: ${macdData.macd?.length || 0})`, macdData.macd ? 'success' : 'error');
                        
                        // 测试索引29的数据
                        const index29 = 29;
                        if (index29 < macdData.dif.length) {
                            const dif29 = macdData.dif[index29];
                            const dea29 = macdData.dea[index29];
                            const macd29 = macdData.macd[index29];
                            
                            addResult('frontendResults', `🎯 索引29数据测试:`, 'info');
                            addResult('frontendResults', `  DIF[29]: ${dif29 !== null ? dif29.toFixed(4) : 'null'}`, dif29 !== null ? 'success' : 'warning');
                            addResult('frontendResults', `  DEA[29]: ${dea29 !== null ? dea29.toFixed(4) : 'null'}`, dea29 !== null ? 'success' : 'warning');
                            addResult('frontendResults', `  MACD[29]: ${macd29 !== null ? macd29.toFixed(4) : 'null'}`, macd29 !== null ? 'success' : 'warning');
                            
                            if (dif29 !== null && dea29 !== null && macd29 !== null) {
                                addResult('frontendResults', '✅ 索引29数据完整，应该能在数据面板中正常显示', 'success');
                            } else {
                                addResult('frontendResults', '⚠️ 索引29数据不完整，可能显示为"--"', 'warning');
                            }
                        } else {
                            addResult('frontendResults', `❌ 索引29超出数据范围 (总长度: ${macdData.dif.length})`, 'error');
                        }
                        
                    } else {
                        addResult('frontendResults', '❌ 未获取到MACD数据', 'error');
                    }
                    
                } else {
                    addResult('frontendResults', `❌ 前端数据流失败: ${indicatorResult.error}`, 'error');
                }
                
            } catch (error) {
                addResult('frontendResults', `❌ 前端数据流错误: ${error.message}`, 'error');
            }
        }

        function testDataPanel() {
            addResult('panelResults', '📊 开始测试数据面板功能...', 'info');
            
            // 模拟前端的getIndicatorValueAtIndex函数
            function getIndicatorValueAtIndex(values, index) {
                if (!values || !Array.isArray(values)) {
                    return '--';
                }
                
                if (index < 0 || index >= values.length) {
                    return '--';
                }
                
                const value = values[index];
                
                if (value === null || value === undefined) {
                    return '--';
                }
                
                if (isNaN(value)) {
                    return '--';
                }
                
                return typeof value === 'number' ? value.toFixed(4) : value;
            }
            
            // 测试数据
            const testData = {
                dif: [null, null, null, 0.0123, 0.0234, 0.0345],
                dea: [null, null, null, null, 0.0111, 0.0222],
                macd: [null, null, null, null, 0.0246, 0.0246]
            };
            
            addResult('panelResults', '🧪 测试getIndicatorValueAtIndex函数:', 'info');
            
            for (let i = 0; i < 6; i++) {
                const difValue = getIndicatorValueAtIndex(testData.dif, i);
                const deaValue = getIndicatorValueAtIndex(testData.dea, i);
                const macdValue = getIndicatorValueAtIndex(testData.macd, i);
                
                addResult('panelResults', `索引${i}: DIF=${difValue}, DEA=${deaValue}, MACD=${macdValue}`, 'info');
            }
            
            addResult('panelResults', '✅ 数据面板函数测试完成', 'success');
            
            // 测试实际的专业界面
            addResult('panelResults', '🔗 测试专业界面连接...', 'info');
            
            // 检查专业界面是否可访问
            fetch('http://localhost:5001/professional.html')
                .then(response => {
                    if (response.ok) {
                        addResult('panelResults', '✅ 专业界面可访问', 'success');
                        addResult('panelResults', '💡 建议打开专业界面并移动鼠标到K线图上测试数据面板', 'info');
                    } else {
                        addResult('panelResults', '❌ 专业界面访问失败', 'error');
                    }
                })
                .catch(error => {
                    addResult('panelResults', `❌ 专业界面连接错误: ${error.message}`, 'error');
                });
        }

        // 页面加载完成后自动运行测试
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                testApiDirectly();
                setTimeout(() => {
                    testFrontendFlow();
                    setTimeout(() => {
                        testDataPanel();
                    }, 1000);
                }, 1000);
            }, 500);
        });
    </script>
</body>
</html>
