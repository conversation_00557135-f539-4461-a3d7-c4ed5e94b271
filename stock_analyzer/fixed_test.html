<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复版股票分析器</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .fix-status {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 15px;
            color: var(--text-primary);
            font-size: 12px;
            z-index: 10000;
            max-width: 250px;
        }
        .status-item {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
        }
        .status-ok { color: var(--color-up); }
        .status-error { color: var(--color-down); }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 顶部导航 -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-chart-line"></i>
                    <span>股票分析器</span>
                </div>
                <nav class="nav-menu">
                    <a href="#" class="nav-item active">实时行情</a>
                    <a href="#" class="nav-item">技术分析</a>
                    <a href="#" class="nav-item">资讯中心</a>
                </nav>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 市场概览 -->
            <section class="market-overview" id="market-overview">
                <h3>市场概览</h3>
                <div class="market-indices">
                    <div class="index-item">
                        <div class="index-name">上证指数</div>
                        <div class="index-value" id="sh-index">3,245.67</div>
                        <div class="index-change positive" id="sh-change">+1.23%</div>
                    </div>
                    <div class="index-item">
                        <div class="index-name">深证成指</div>
                        <div class="index-value" id="sz-index">12,456.78</div>
                        <div class="index-change negative" id="sz-change">-0.45%</div>
                    </div>
                    <div class="index-item">
                        <div class="index-name">创业板指</div>
                        <div class="index-value" id="cy-index">2,789.34</div>
                        <div class="index-change positive" id="cy-change">+0.89%</div>
                    </div>
                </div>
            </section>

            <!-- 搜索区域 -->
            <section class="search-section">
                <div class="search-container">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="stock-search" placeholder="输入股票代码或名称，如：000001 或 平安银行">
                        <button class="search-btn" id="search-btn">搜索</button>
                    </div>
                    <div class="quick-search">
                        <span class="quick-label">热门股票：</span>
                        <button class="quick-btn" data-code="000001">平安银行</button>
                        <button class="quick-btn" data-code="000002">万科A</button>
                        <button class="quick-btn" data-code="600036">招商银行</button>
                        <button class="quick-btn" data-code="600519">贵州茅台</button>
                        <button class="quick-btn" data-code="000858">五粮液</button>
                    </div>
                </div>
            </section>

            <!-- 股票信息面板 -->
            <section class="stock-panel" id="stock-panel">
                <div id="stock-info">股票信息容器</div>
                <div class="stock-header">
                    <div class="stock-basic-info">
                        <h2 class="stock-name" id="stock-name">平安银行</h2>
                        <span class="stock-code" id="stock-code">000001</span>
                    </div>
                    <div class="stock-price-info">
                        <div class="current-price" id="current-price">13.45</div>
                        <div class="price-change">
                            <span class="change-amount" id="change-amount">+0.23</span>
                            <span class="change-percent" id="change-percent">+1.74%</span>
                        </div>
                    </div>
                </div>

                <!-- 图表和分析区域 -->
                <div class="analysis-container">
                    <div class="chart-section">
                        <div class="chart-header">
                            <h3>价格走势</h3>
                            <div class="chart-controls">
                                <div class="chart-type-controls">
                                    <button class="chart-type-btn active" data-type="kline">K线</button>
                                    <button class="chart-type-btn" data-type="timeline">分时</button>
                                </div>
                                <div class="chart-period-controls">
                                    <button class="chart-btn active" data-period="1d">日K</button>
                                    <button class="chart-btn" data-period="1w">周K</button>
                                    <button class="chart-btn" data-period="1m">月K</button>
                                    <button class="chart-btn" data-period="5m">5分</button>
                                    <button class="chart-btn" data-period="15m">15分</button>
                                    <button class="chart-btn" data-period="60m">60分</button>
                                </div>
                            </div>
                        </div>
                        <div class="chart-container">
                            <div id="price-chart"></div>
                        </div>
                        <div class="indicator-controls">
                            <h4>技术指标</h4>
                            <div class="indicator-buttons">
                                <button class="indicator-btn active" data-indicator="ma">均线</button>
                                <button class="indicator-btn" data-indicator="boll">布林带</button>
                                <button class="indicator-btn" data-indicator="macd">MACD</button>
                                <button class="indicator-btn" data-indicator="kdj">KDJ</button>
                                <button class="indicator-btn" data-indicator="rsi">RSI</button>
                                <button class="indicator-btn" data-indicator="volume">成交量</button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 热门股票列表 -->
            <section class="hot-stocks-section">
                <div class="section-header">
                    <h3>热门股票</h3>
                    <div class="section-tabs">
                        <button class="tab-btn active" data-tab="gainers">涨幅榜</button>
                        <button class="tab-btn" data-tab="losers">跌幅榜</button>
                        <button class="tab-btn" data-tab="volume">成交量榜</button>
                    </div>
                </div>
                <div class="stocks-list" id="hot-stocks-list">
                    <!-- 股票列表将在这里动态生成 -->
                </div>
            </section>
        </main>
    </div>

    <!-- 加载指示器 -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <!-- 修复状态显示 -->
    <div class="fix-status" id="fix-status">
        <div style="font-weight: bold; margin-bottom: 10px;">🔧 系统状态</div>
        <div id="status-content">正在检查...</div>
    </div>

    <!-- 脚本文件 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://unpkg.com/lightweight-charts@4.1.3/dist/lightweight-charts.standalone.production.js"></script>
    
    <script>
        // 检查系统状态
        function checkSystemStatus() {
            const elements = [
                'loading-overlay',
                'price-chart',
                'hot-stocks-list',
                'sh-index',
                'sz-index',
                'cy-index',
                'stock-info',
                'market-overview'
            ];
            
            let found = 0;
            let statusHtml = '';
            
            elements.forEach(id => {
                const exists = document.getElementById(id) !== null;
                if (exists) found++;
                statusHtml += `<div class="status-item"><span>${id}</span><span class="${exists ? 'status-ok' : 'status-error'}">${exists ? '✅' : '❌'}</span></div>`;
            });
            
            const buttons = {
                'chart-type-btn': document.querySelectorAll('.chart-type-btn').length,
                'chart-btn': document.querySelectorAll('.chart-btn').length,
                'indicator-btn': document.querySelectorAll('.indicator-btn').length
            };
            
            Object.entries(buttons).forEach(([type, count]) => {
                statusHtml += `<div class="status-item"><span>${type}</span><span class="status-ok">${count}</span></div>`;
            });
            
            document.getElementById('status-content').innerHTML = statusHtml;
            
            // 5秒后隐藏状态面板
            setTimeout(() => {
                const statusPanel = document.getElementById('fix-status');
                if (statusPanel) {
                    statusPanel.style.display = 'none';
                }
            }, 5000);
            
            return found === elements.length;
        }
        
        // 简化的股票分析器
        class SimpleStockAnalyzer {
            constructor() {
                this.currentStock = null;
                this.currentPeriod = '1d';
                this.activeIndicators = ['ma'];
                this.init();
            }
            
            init() {
                console.log('初始化简化版股票分析器');
                this.bindEvents();
                this.hideLoading();
                this.showDefaultChart();
            }
            
            bindEvents() {
                // 搜索功能
                document.getElementById('search-btn').addEventListener('click', () => {
                    this.searchStock();
                });
                
                document.getElementById('stock-search').addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.searchStock();
                    }
                });
                
                // 快速搜索按钮
                document.querySelectorAll('.quick-btn').forEach(btn => {
                    btn.addEventListener('click', () => {
                        document.getElementById('stock-search').value = btn.dataset.code;
                        this.searchStock();
                    });
                });
                
                // 图表控制按钮
                this.bindChartEvents();
            }
            
            bindChartEvents() {
                // 图表类型切换
                document.querySelectorAll('.chart-type-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        document.querySelectorAll('.chart-type-btn').forEach(b => b.classList.remove('active'));
                        e.target.classList.add('active');
                        console.log('图表类型切换:', e.target.dataset.type);
                    });
                });

                // 图表时间周期切换
                document.querySelectorAll('.chart-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        document.querySelectorAll('.chart-btn').forEach(b => b.classList.remove('active'));
                        e.target.classList.add('active');
                        this.currentPeriod = e.target.dataset.period;
                        console.log('周期切换:', this.currentPeriod);
                        if (this.currentStock) {
                            this.loadStockData(this.currentStock);
                        }
                    });
                });

                // 技术指标切换
                document.querySelectorAll('.indicator-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const indicator = e.target.dataset.indicator;
                        e.target.classList.toggle('active');
                        console.log('指标切换:', indicator);
                    });
                });
            }
            
            async searchStock() {
                const query = document.getElementById('stock-search').value.trim();
                if (!query) return;
                
                console.log('搜索股票:', query);
                this.showLoading();
                
                try {
                    await this.loadStockData(query);
                } catch (error) {
                    console.error('搜索失败:', error);
                } finally {
                    this.hideLoading();
                }
            }
            
            async loadStockData(stockCode) {
                this.currentStock = stockCode;
                
                try {
                    const response = await fetch(`http://localhost:5001/api/stock/${stockCode}`);
                    const result = await response.json();
                    
                    if (result.success) {
                        this.updateStockInfo(result.data);
                        await this.loadChart(stockCode);
                        document.getElementById('stock-panel').style.display = 'block';
                    } else {
                        throw new Error(result.error || '股票数据获取失败');
                    }
                } catch (error) {
                    console.error('加载股票数据失败:', error);
                    this.showMessage('股票数据加载失败: ' + error.message, 'error');
                }
            }
            
            updateStockInfo(data) {
                document.getElementById('stock-name').textContent = data.name;
                document.getElementById('stock-code').textContent = data.code;
                document.getElementById('current-price').textContent = data.price;
                document.getElementById('change-amount').textContent = (data.change > 0 ? '+' : '') + data.change;
                document.getElementById('change-percent').textContent = (data.change > 0 ? '+' : '') + data.changePercent + '%';
            }
            
            async loadChart(stockCode) {
                try {
                    const response = await fetch(`http://localhost:5001/api/kline/${stockCode}?period=${this.currentPeriod}&count=30`);
                    const result = await response.json();
                    
                    if (result.success) {
                        this.renderChart(result.data);
                    }
                } catch (error) {
                    console.error('图表数据加载失败:', error);
                }
            }
            
            renderChart(klineData) {
                const container = document.getElementById('price-chart');
                const klines = klineData.klines.slice(-20);
                
                const containerWidth = container.clientWidth || 800;
                const containerHeight = 400;
                
                container.innerHTML = `<canvas id="main-chart" width="${containerWidth}" height="${containerHeight}" style="width: 100%; height: 100%;"></canvas>`;
                const canvas = document.getElementById('main-chart');
                const ctx = canvas.getContext('2d');
                
                this.drawChart(ctx, canvas, klines);
            }
            
            drawChart(ctx, canvas, klines) {
                const padding = { top: 40, right: 60, bottom: 40, left: 60 };
                const chartWidth = canvas.width - padding.left - padding.right;
                const chartHeight = canvas.height - padding.top - padding.bottom;
                
                // 计算价格范围
                const prices = klines.map(k => [k.high, k.low, k.open, k.close]).flat();
                const maxPrice = Math.max(...prices);
                const minPrice = Math.min(...prices);
                const priceRange = maxPrice - minPrice || 1;
                const priceBuffer = priceRange * 0.1;
                
                const adjustedMax = maxPrice + priceBuffer;
                const adjustedMin = minPrice - priceBuffer;
                const adjustedRange = adjustedMax - adjustedMin;
                
                // 背景
                ctx.fillStyle = '#1e2329';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // 网格
                ctx.strokeStyle = '#2b3139';
                ctx.lineWidth = 1;
                
                for (let i = 0; i <= 5; i++) {
                    const x = padding.left + (chartWidth / 5) * i;
                    ctx.beginPath();
                    ctx.moveTo(x, padding.top);
                    ctx.lineTo(x, padding.top + chartHeight);
                    ctx.stroke();
                }
                
                for (let i = 0; i <= 4; i++) {
                    const y = padding.top + (chartHeight / 4) * i;
                    ctx.beginPath();
                    ctx.moveTo(padding.left, y);
                    ctx.lineTo(padding.left + chartWidth, y);
                    ctx.stroke();
                }
                
                // K线
                const candleWidth = Math.max(6, chartWidth / klines.length * 0.8);
                
                klines.forEach((kline, index) => {
                    const x = padding.left + (chartWidth / (klines.length - 1)) * index;
                    const openY = padding.top + chartHeight - ((kline.open - adjustedMin) / adjustedRange) * chartHeight;
                    const closeY = padding.top + chartHeight - ((kline.close - adjustedMin) / adjustedRange) * chartHeight;
                    const highY = padding.top + chartHeight - ((kline.high - adjustedMin) / adjustedRange) * chartHeight;
                    const lowY = padding.top + chartHeight - ((kline.low - adjustedMin) / adjustedRange) * chartHeight;
                    
                    const isUp = kline.close >= kline.open;
                    const color = isUp ? '#ff4757' : '#2ed573';
                    
                    // 影线
                    ctx.strokeStyle = color;
                    ctx.lineWidth = 1;
                    ctx.beginPath();
                    ctx.moveTo(x, highY);
                    ctx.lineTo(x, lowY);
                    ctx.stroke();
                    
                    // 实体
                    ctx.fillStyle = color;
                    const bodyTop = Math.min(openY, closeY);
                    const bodyHeight = Math.max(2, Math.abs(closeY - openY));
                    ctx.fillRect(x - candleWidth/2, bodyTop, candleWidth, bodyHeight);
                });
                
                // 价格标签
                ctx.fillStyle = '#d1d4dc';
                ctx.font = '11px Arial';
                ctx.textAlign = 'right';
                
                for (let i = 0; i <= 4; i++) {
                    const price = adjustedMin + adjustedRange * (1 - i / 4);
                    const y = padding.top + (chartHeight / 4) * i;
                    ctx.fillText(price.toFixed(2), padding.left - 10, y + 4);
                }
            }
            
            showDefaultChart() {
                const container = document.getElementById('price-chart');
                container.innerHTML = `
                    <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 400px; color: var(--text-muted);">
                        <div style="font-size: 48px; margin-bottom: 20px;">📊</div>
                        <div style="font-size: 18px; margin-bottom: 10px;">股票图表分析</div>
                        <div style="font-size: 14px;">请搜索股票代码查看K线图和技术指标</div>
                    </div>
                `;
            }
            
            showLoading() {
                document.getElementById('loading-overlay').style.display = 'flex';
            }
            
            hideLoading() {
                document.getElementById('loading-overlay').style.display = 'none';
            }
            
            showMessage(message, type = 'info') {
                console.log(`[${type.toUpperCase()}] ${message}`);
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('修复版页面加载完成');
            
            // 检查系统状态
            setTimeout(() => {
                const allGood = checkSystemStatus();
                console.log('系统状态检查完成:', allGood ? '正常' : '有问题');
            }, 500);
            
            // 初始化分析器
            setTimeout(() => {
                new SimpleStockAnalyzer();
            }, 1000);
        });
    </script>
</body>
</html>
