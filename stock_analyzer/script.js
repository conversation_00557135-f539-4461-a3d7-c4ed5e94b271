// 股票分析系统主要功能 - 版本 2.0 (修复chartHeight错误)
class StockAnalyzer {
    constructor() {
        this.currentStock = '000001'; // 默认加载平安银行
        this.chart = null;
        this.updateInterval = null;
        this.dataStream = [];
        this.marketOpen = false;
        this.currentPeriod = '1d';
        this.currentChartType = 'kline';
        this.activeIndicators = ['volume', 'macd', 'kdj']; // 默认显示成交量、MACD、KDJ
        this.activeMAs = [5, 10, 20]; // 默认显示的均线

        // 指数代码映射：显示代码 -> 查询代码
        this.indexCodeMap = {
            '1A0001': 'sh000001',  // 上证指数
            '1B0300': 'sh000300'   // 沪深300
        };

        // 添加currentStock的setter来跟踪变化
        this._currentStock = null;
        Object.defineProperty(this, 'currentStock', {
            get: function() { return this._currentStock; },
            set: function(value) {
                console.log('🔄 currentStock 变化:', this._currentStock, '->', value);
                console.trace('currentStock 设置调用栈:');
                this._currentStock = value;
            }
        });

        this.init();
    }

    // 将显示代码转换为查询代码
    convertToQueryCode(displayCode) {
        return this.indexCodeMap[displayCode] || displayCode;
    }

    async init() {
        console.log('🚀 开始初始化股票分析器');

        try {
            // 1. 绑定事件（同步操作）
            console.log('🔄 步骤1: 绑定事件...');
            this.bindEvents();
            console.log('✅ 步骤1: 事件绑定完成');

            // 2. 加载市场数据（异步等待）
            console.log('🔄 步骤2: 开始加载市场数据...');
            await this.loadMarketData();
            console.log('✅ 步骤2: 市场数据加载完成');

            // 3. 跳过热门股票加载（已移除）
            console.log('🔄 步骤3: 跳过热门股票加载...');
            console.log('✅ 步骤3: 热门股票加载跳过');

            // 4. 跳过市场概况加载（已移除）
            console.log('🔄 步骤4: 跳过市场概况加载...');
            console.log('✅ 步骤4: 市场概况加载跳过');

            // 5. 启动实时更新
            console.log('🔄 步骤5: 启动实时更新...');
            this.startRealTimeUpdates();
            console.log('✅ 步骤5: 实时更新启动');

            // 6. 加载默认股票（平安银行）
            console.log('🔄 步骤6: 加载默认股票（平安银行）...');
            await this.loadStockData('000001');
            console.log('✅ 步骤6: 默认股票加载完成');

            // 7. 加载指数数据
            console.log('🔄 步骤7: 加载指数数据...');

            await this.loadIndicesData();
            console.log('✅ 步骤7: 指数数据加载完成');

            // 7. 隐藏加载状态
            console.log('🔄 步骤7: 隐藏加载状态...');
            this.hideLoading();
            console.log('✅ 步骤7: 初始化完成，加载状态已隐藏');

            // 添加全局调试函数
            window.debugStockAnalyzer = () => {
                console.log('🔍 调试信息:');
                console.log('当前股票:', this.currentStock);
                console.log('活跃指标:', this.activeIndicators);
                console.log('活跃均线:', this.activeMAs);
                console.log('最后K线数据:', this.lastKlineData ? `${this.lastKlineData.klines?.length}条` : '无');
                console.log('最后指标数据:', this.lastIndicatorData ? '有' : '无');
                if (this.lastIndicatorData) {
                    console.log('指标数据详情:', this.lastIndicatorData);
                }
            };

            console.log('💡 可以在控制台运行 debugStockAnalyzer() 查看调试信息');

        } catch (error) {
            console.error('❌ 初始化失败:', error);
            console.error('❌ 错误堆栈:', error.stack);
            this.hideLoading();
            this.showMessage(`初始化失败: ${error.message}`, 'error');
        }
    }

    showDefaultChart() {
        const container = document.getElementById('price-chart');
        if (container) {
            container.innerHTML = `
                <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 400px; color: var(--text-muted);">
                    <div style="font-size: 48px; margin-bottom: 20px;">📊</div>
                    <div style="font-size: 18px; margin-bottom: 10px;">股票图表分析</div>
                    <div style="font-size: 14px;">请搜索股票代码查看K线图和技术指标</div>
                </div>
            `;
        }
    }

    bindEvents() {
        // 搜索功能 - 添加安全检查
        const searchBtn = document.getElementById('search-btn');
        const searchInput = document.getElementById('stock-search');

        if (searchBtn) {
            searchBtn.addEventListener('click', () => {
                this.searchStock();
            });
        }

        if (searchInput) {
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.searchStock();
                }
            });
        }

        // 快速搜索按钮
        document.querySelectorAll('.quick-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const code = e.target.dataset.code;
                this.loadStockData(code);
            });
        });

        // 绑定指数点击事件
        document.querySelectorAll('.index-item').forEach(item => {
            item.addEventListener('click', () => {
                const code = item.getAttribute('data-code');
                if (code) {
                    console.log('🔥 点击指数:', code);
                    this.loadStockData(code);
                }
            });
        });

        // 使用事件委托绑定按钮事件，避免DOM元素不存在的问题
        this.bindEventDelegation();
    }

    // 移除重复的事件绑定方法，统一使用事件委托

    bindEventDelegation() {
        console.log('绑定事件委托');

        // 使用事件委托处理所有按钮点击
        document.addEventListener('click', (e) => {
            // 调试所有点击事件
            if (e.target.tagName === 'BUTTON') {
                console.log('🖱️ 按钮点击:', e.target.textContent, '类名:', e.target.className, '数据属性:', e.target.dataset);
            }
            // 图表类型按钮
            if (e.target.classList.contains('chart-type-btn')) {
                console.log('事件委托 - 图表类型切换:', e.target.dataset.type);
                document.querySelectorAll('.chart-type-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                this.currentChartType = e.target.dataset.type;
                if (this.currentStock) {
                    this.updateChart(this.currentPeriod);
                }
                return;
            }

            // 周期按钮 (支持两种类名)
            if (e.target.classList.contains('chart-btn') || e.target.classList.contains('period-btn')) {
                const period = e.target.dataset.period;
                console.log('事件委托 - 周期切换:', period);

                // 处理特殊操作
                if (period === 'prev') {
                    this.switchToPreviousPeriod();
                    return;
                } else if (period === 'next') {
                    this.switchToNextPeriod();
                    return;
                } else if (period === 'refresh') {
                    this.refreshCurrentPeriod();
                    return;
                }

                // 正常周期切换
                this.switchToPeriod(period, e.target);
                return;
            }

            // 均线按钮
            if (e.target.classList.contains('ma-btn')) {
                const maPeriod = parseInt(e.target.dataset.ma);
                console.log('🎯 事件委托 - 均线切换:', maPeriod);
                console.log('🎯 按钮当前状态:', e.target.classList.contains('active') ? '激活' : '未激活');
                console.log('🎯 点击前活跃均线:', this.activeMAs);

                if (e.target.classList.contains('active')) {
                    e.target.classList.remove('active');
                    this.activeMAs = this.activeMAs.filter(ma => ma !== maPeriod);
                    console.log(`🔴 移除均线: MA${maPeriod}`);
                } else {
                    e.target.classList.add('active');
                    if (!this.activeMAs.includes(maPeriod)) {
                        this.activeMAs.push(maPeriod);
                        console.log(`🟢 添加均线: MA${maPeriod}`);
                    }
                }

                console.log('🎯 点击后活跃均线:', this.activeMAs);
                console.log('🎯 当前股票:', this.currentStock);

                // 检查是否有股票数据缓存
                if (!this.currentStock) {
                    console.log('🔧 currentStock为null，尝试恢复...');
                    console.log('🔧 lastKlineData存在:', !!this.lastKlineData);

                    // 尝试从页面元素中获取当前股票代码
                    const stockCodeEl = document.getElementById('stock-code');
                    console.log('🔧 stockCodeEl:', stockCodeEl);
                    console.log('🔧 stockCodeEl.textContent:', stockCodeEl?.textContent);

                    if (stockCodeEl && stockCodeEl.textContent && stockCodeEl.textContent.trim()) {
                        this.currentStock = stockCodeEl.textContent.trim();
                        console.log('✅ 从页面元素恢复股票代码:', this.currentStock);
                    } else {
                        console.warn('❌ 无法从页面元素恢复股票代码');
                    }
                }

                if (this.currentStock) {
                    console.log('🔄 开始更新图表...');
                    this.updateChart(this.currentPeriod);
                } else {
                    console.warn('⚠️ 没有选择股票，无法更新图表');
                    console.warn('💡 请先选择一个股票，然后再调整均线设置');
                }
                return;
            }

            // 技术指标按钮
            if (e.target.classList.contains('indicator-btn')) {
                const indicator = e.target.dataset.indicator;
                console.log('🎯 事件委托 - 指标切换:', indicator);
                console.log('🎯 按钮当前状态:', e.target.classList.contains('active') ? '激活' : '未激活');

                if (e.target.classList.contains('active')) {
                    e.target.classList.remove('active');
                    this.activeIndicators = this.activeIndicators.filter(i => i !== indicator);
                    console.log(`🔴 移除指标: ${indicator}`);
                } else {
                    e.target.classList.add('active');
                    if (!this.activeIndicators.includes(indicator)) {
                        this.activeIndicators.push(indicator);
                        console.log(`🟢 添加指标: ${indicator}`);
                    }
                }

                console.log('🎯 当前活跃指标:', this.activeIndicators);
                console.log('🎯 当前股票:', this.currentStock);

                // 检查是否有股票数据缓存，如果有就恢复currentStock
                if (!this.currentStock) {
                    const stockCodeEl = document.getElementById('stock-code');
                    if (stockCodeEl && stockCodeEl.textContent && stockCodeEl.textContent.trim()) {
                        this.currentStock = stockCodeEl.textContent.trim();
                        console.log('🔧 从页面恢复股票代码:', this.currentStock);
                    }
                }

                if (this.currentStock) {
                    console.log('🔄 开始更新图表...');
                    this.updateChart(this.currentPeriod);
                } else {
                    console.warn('⚠️ 没有选择股票，无法更新图表');
                    console.warn('💡 请先选择一个股票，然后再调整均线设置');
                }
                return;
            }

            // 移除热门股票标签按钮事件（已删除热门股票功能）
        });

        // 绑定键盘快捷键
        this.bindKeyboardShortcuts();

        console.log('事件委托绑定完成');
    }

    // 统一键盘事件处理系统 - 避免事件监听器冲突
    bindKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // 如果在输入框中，不处理快捷键
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
                return;
            }

            // 检查修饰键状态
            const isCtrl = e.ctrlKey || e.metaKey;
            const isShift = e.shiftKey;
            const isAlt = e.altKey;
            const key = e.key.toLowerCase();

            console.log(`🎹 键盘事件: ${key}, Ctrl:${isCtrl}, Shift:${isShift}, Alt:${isAlt}`);

            // === 1. 周期切换 - 数字键 (最高优先级) ===
            if (!isCtrl && !isShift && !isAlt) {
                const periodMap = {
                    '1': 'tick',  // 1: 分时
                    '2': '1m',    // 2: 1分钟
                    '3': '5m',    // 3: 5分钟
                    '4': '15m',   // 4: 15分钟
                    '5': '30m',   // 5: 30分钟
                    '6': '60m',   // 6: 60分钟
                    '7': '120m',  // 7: 120分钟
                    '8': '1d',    // 8: 日K
                    '9': '1w',    // 9: 周K
                    '0': '1M'     // 0: 月K
                };

                if (periodMap[key]) {
                    e.preventDefault();
                    const period = periodMap[key];
                    const button = document.querySelector(`[data-period="${period}"]`);
                    if (button) {
                        this.switchToPeriod(period, button);
                        this.showKeyboardMessage(`切换到 ${this.getPeriodDisplayName(period)}`, '#2196f3');
                    }
                    return;
                }
            }

            // === 2. 十字光标导航 - 无修饰键 (第二优先级) ===
            if (!isCtrl && !isShift && !isAlt && this.lastKlineData && this.lastKlineData.klines) {
                const klines = this.getStandardKlineData(this.lastKlineData.klines);

                switch(key) {
                    // 方向键导航 - 最重要的功能
                    case 'arrowleft':
                        e.preventDefault();
                        this.moveCrosshair(-1, klines);
                        console.log('🎯 左键导航');
                        return;
                    case 'arrowright':
                        e.preventDefault();
                        this.moveCrosshair(1, klines);
                        console.log('🎯 右键导航');
                        return;
                    case 'arrowup':
                        e.preventDefault();
                        this.moveCrosshairFast(-5, klines);
                        return;
                    case 'arrowdown':
                        e.preventDefault();
                        this.moveCrosshairFast(5, klines);
                        return;

                    // WASD导航
                    case 'a':
                        e.preventDefault();
                        this.moveCrosshair(-1, klines);
                        return;
                    case 'd':
                        e.preventDefault();
                        this.moveCrosshair(1, klines);
                        return;
                    case 'w':
                        e.preventDefault();
                        this.moveCrosshairFast(-5, klines);
                        return;
                    case 's':
                        e.preventDefault();
                        this.moveCrosshairFast(5, klines);
                        return;

                    // 快速跳转 - 修改为避免与Shift组合冲突
                    case 'home':
                        e.preventDefault();
                        this.jumpToFirstKline(klines);
                        return;
                    case 'end':
                        e.preventDefault();
                        this.jumpToLastKline(klines);
                        return;
                    case 'z':
                        e.preventDefault();
                        this.moveCrosshairFast(-10, klines);
                        return;
                    case 'x':
                        e.preventDefault();
                        this.moveCrosshairFast(10, klines);
                        return;

                    // 功能键
                    case ' ':
                        e.preventDefault();
                        this.toggleCrosshairLock();
                        return;
                    case 'escape':
                        e.preventDefault();
                        this.hideCrosshair();
                        return;
                    case 'c':
                        e.preventDefault();
                        this.centerCrosshair(klines);
                        return;
                    case 'v':
                        e.preventDefault();
                        this.toggleDataPanel();
                        return;
                    case 'b':
                        e.preventDefault();
                        this.toggleCrosshairGrid();
                        return;
                    case 'f1':
                        e.preventDefault();
                        this.showKeyboardHelp();
                        return;
                    case 'f2':
                        e.preventDefault();
                        this.testDataPanel();
                        return;
                }
            }

            // === 3. Shift组合键 ===
            if (isShift && !isCtrl && !isAlt) {
                // 均线切换
                const maMap = {
                    'w': 10,    // Shift+W: MA10 (避免与Q冲突)
                    'e': 20,    // Shift+E: MA20 (避免与E冲突)
                    'r': 30,    // Shift+R: MA30
                    't': 60,    // Shift+T: MA60
                    'y': 120,   // Shift+Y: MA120
                    'u': 250    // Shift+U: MA250
                };

                if (maMap[key]) {
                    e.preventDefault();
                    const maPeriod = maMap[key];
                    this.toggleMA(maPeriod);
                    this.showKeyboardMessage(`切换 MA${maPeriod}`, '#ff9800');
                    return;
                }

                // 周期导航 - 使用不冲突的键
                if (key === 'q') {
                    e.preventDefault();
                    this.switchToPreviousPeriod();
                    this.showKeyboardMessage('切换到上一周期 (Shift+Q)', '#2196f3');
                    return;
                }
                if (key === 'e') {
                    e.preventDefault();
                    this.switchToNextPeriod();
                    this.showKeyboardMessage('切换到下一周期 (Shift+E)', '#2196f3');
                    return;
                }
            }

            // === 4. 系统功能 - 无修饰键 ===
            if (!isCtrl && !isShift && !isAlt) {
                switch (key) {
                    case '`':  // 反引号键刷新数据
                        e.preventDefault();
                        this.refreshCurrentPeriod();
                        this.showKeyboardMessage('数据已刷新', '#4caf50');
                        return;
                }
            }
        });

        // 绑定均线和指标快速操作
        this.bindOperatorControls();
    }

    // 获取周期显示名称
    getPeriodDisplayName(period) {
        const periodNames = {
            'tick': '分时图',
            '1m': '1分钟',
            '5m': '5分钟',
            '15m': '15分钟',
            '30m': '30分钟',
            '60m': '60分钟',
            '120m': '120分钟',
            '1d': '日K线',
            '1w': '周K线',
            '1M': '月K线'
        };
        return periodNames[period] || period;

        // 同步按钮状态与默认设置
        this.syncButtonStates();

        // 初始化十字光标联动系统
        this.initCrosshairSystem();
    }

    // 绑定操作员控制功能
    bindOperatorControls() {
        // 均线预设按钮
        const maPresetShort = document.getElementById('ma-preset-short');
        const maPresetSwing = document.getElementById('ma-preset-swing');
        const maPresetTrend = document.getElementById('ma-preset-trend');
        const maClearAll = document.getElementById('ma-clear-all');

        if (maPresetShort) {
            maPresetShort.addEventListener('click', () => this.setMAPreset('short'));
        }
        if (maPresetSwing) {
            maPresetSwing.addEventListener('click', () => this.setMAPreset('swing'));
        }
        if (maPresetTrend) {
            maPresetTrend.addEventListener('click', () => this.setMAPreset('trend'));
        }
        if (maClearAll) {
            maClearAll.addEventListener('click', () => this.clearAllMA());
        }

        // 技术指标预设按钮
        const indicatorPresetBasic = document.getElementById('indicator-preset-basic');
        const indicatorPresetTrend = document.getElementById('indicator-preset-trend');
        const indicatorPresetOscillator = document.getElementById('indicator-preset-oscillator');
        const indicatorClearAll = document.getElementById('indicator-clear-all');

        if (indicatorPresetBasic) {
            indicatorPresetBasic.addEventListener('click', () => this.setIndicatorPreset('basic'));
        }
        if (indicatorPresetTrend) {
            indicatorPresetTrend.addEventListener('click', () => this.setIndicatorPreset('trend'));
        }
        if (indicatorPresetOscillator) {
            indicatorPresetOscillator.addEventListener('click', () => this.setIndicatorPreset('oscillator'));
        }
        if (indicatorClearAll) {
            indicatorClearAll.addEventListener('click', () => this.clearAllIndicators());
        }
    }

    // 切换均线显示
    toggleMA(maPeriod) {
        const button = document.querySelector(`[data-ma="${maPeriod}"]`);
        if (!button) return;

        if (button.classList.contains('active')) {
            button.classList.remove('active');
            this.activeMAs = this.activeMAs.filter(ma => ma !== maPeriod);
            console.log(`🔴 移除均线: MA${maPeriod}`);
        } else {
            button.classList.add('active');
            if (!this.activeMAs.includes(maPeriod)) {
                this.activeMAs.push(maPeriod);
                console.log(`🟢 添加均线: MA${maPeriod}`);
            }
        }

        // 更新图表
        if (this.currentStock) {
            this.updateChart(this.currentPeriod);
        }

        // 显示切换提示
        this.showMAToggleMessage(maPeriod, button.classList.contains('active'));
    }

    // 设置均线预设
    setMAPreset(presetType) {
        console.log('🎯 设置均线预设:', presetType);

        // 清除所有均线
        this.clearAllMA(false);

        // 根据预设类型设置均线
        let maList = [];
        switch (presetType) {
            case 'short':
                maList = [5, 10, 20];
                break;
            case 'swing':
                maList = [10, 20, 60];
                break;
            case 'trend':
                maList = [20, 60, 120];
                break;
        }

        // 激活对应的均线
        maList.forEach(ma => {
            const button = document.querySelector(`[data-ma="${ma}"]`);
            if (button) {
                button.classList.add('active');
                if (!this.activeMAs.includes(ma)) {
                    this.activeMAs.push(ma);
                }
            }
        });

        // 更新图表
        if (this.currentStock) {
            this.updateChart(this.currentPeriod);
        }

        // 显示预设提示
        const presetNames = {
            'short': '短线组合 (MA5, MA10, MA20)',
            'swing': '波段组合 (MA10, MA20, MA60)',
            'trend': '趋势组合 (MA20, MA60, MA120)'
        };
        this.showMessage(`已设置${presetNames[presetType]}`, 'success');
    }

    // 清除所有均线
    clearAllMA(updateChart = true) {
        console.log('🧹 清除所有均线');

        // 移除所有均线按钮的激活状态
        document.querySelectorAll('.ma-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        // 清空活跃均线数组
        this.activeMAs = [];

        // 更新图表
        if (updateChart && this.currentStock) {
            this.updateChart(this.currentPeriod);
        }

        if (updateChart) {
            this.showMessage('已清除所有均线', 'info');
        }
    }

    // 设置技术指标预设
    setIndicatorPreset(presetType) {
        console.log('🎯 设置技术指标预设:', presetType);

        // 清除所有指标
        this.clearAllIndicators(false);

        // 根据预设类型设置指标
        let indicatorList = [];
        switch (presetType) {
            case 'basic':
                indicatorList = ['volume', 'macd', 'kdj'];
                break;
            case 'trend':
                indicatorList = ['macd', 'boll', 'dmi'];
                break;
            case 'oscillator':
                indicatorList = ['kdj', 'rsi', 'cci'];
                break;
        }

        // 激活对应的指标
        indicatorList.forEach(indicator => {
            const button = document.querySelector(`[data-indicator="${indicator}"]`);
            if (button) {
                button.classList.add('active');
                if (!this.activeIndicators.includes(indicator)) {
                    this.activeIndicators.push(indicator);
                }
            }
        });

        // 更新图表
        if (this.currentStock) {
            this.updateChart(this.currentPeriod);
        }

        // 显示预设提示
        const presetNames = {
            'basic': '基础组合 (成交量, MACD, KDJ)',
            'trend': '趋势组合 (MACD, BOLL, DMI)',
            'oscillator': '震荡组合 (KDJ, RSI, CCI)'
        };
        this.showMessage(`已设置${presetNames[presetType]}`, 'success');
    }

    // 清除所有技术指标
    clearAllIndicators(updateChart = true) {
        console.log('🧹 清除所有技术指标');

        // 移除所有指标按钮的激活状态
        document.querySelectorAll('.indicator-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        // 清空活跃指标数组
        this.activeIndicators = [];

        // 更新图表
        if (updateChart && this.currentStock) {
            this.updateChart(this.currentPeriod);
        }

        if (updateChart) {
            this.showMessage('已清除所有技术指标', 'info');
        }
    }

    // 显示均线切换消息
    showMAToggleMessage(maPeriod, isActive) {
        const action = isActive ? '添加' : '移除';
        const color = isActive ? '#4caf50' : '#f44336';

        // 创建临时提示
        const toast = document.createElement('div');
        toast.style.cssText = `
            position: fixed;
            top: 120px;
            right: 20px;
            background: ${color};
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
            z-index: 10000;
            opacity: 0;
            transform: translateX(100px);
            transition: all 0.3s ease;
        `;
        toast.textContent = `${action} MA${maPeriod}`;
        document.body.appendChild(toast);

        // 显示动画
        setTimeout(() => {
            toast.style.opacity = '1';
            toast.style.transform = 'translateX(0)';
        }, 10);

        // 自动消失
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transform = 'translateX(100px)';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 1500);
    }

    // 同步按钮状态与默认设置
    syncButtonStates() {
        console.log('🔄 同步按钮状态与默认设置');

        // 同步均线按钮状态
        document.querySelectorAll('.ma-btn').forEach(btn => {
            const maPeriod = parseInt(btn.dataset.ma);
            if (this.activeMAs.includes(maPeriod)) {
                btn.classList.add('active');
            } else {
                btn.classList.remove('active');
            }
        });

        // 同步技术指标按钮状态
        document.querySelectorAll('.indicator-btn').forEach(btn => {
            const indicator = btn.dataset.indicator;
            if (this.activeIndicators.includes(indicator)) {
                btn.classList.add('active');
            } else {
                btn.classList.remove('active');
            }
        });

        console.log('✅ 按钮状态同步完成');
        console.log('活跃均线:', this.activeMAs);
        console.log('活跃指标:', this.activeIndicators);
    }

    // 操作员专用十字光标联动系统
    initCrosshairSystem() {
        console.log('🎯 初始化十字光标联动系统');

        // 当前选中的K线索引
        this.currentKlineIndex = -1;
        this.crosshairActive = false;
        this.crosshairLocked = false; // 十字光标锁定状态
        this.showCrosshairGrid = false; // 十字光标网格显示
        this.lastKlineData = null;
        this.lastIndicatorData = null;

        // 键盘事件已在主函数中统一处理

        // 创建数据显示面板
        this.createDataDisplayPanel();
    }



    // 检查是否有活跃的输入元素
    hasActiveInput() {
        const activeElement = document.activeElement;
        return activeElement && (
            activeElement.tagName === 'INPUT' ||
            activeElement.tagName === 'TEXTAREA' ||
            activeElement.contentEditable === 'true'
        );
    }

    // 移动十字光标
    moveCrosshair(direction, klines) {
        console.log(`🎯 moveCrosshair调用: direction=${direction}, klines长度=${klines ? klines.length : 'null'}`);

        if (!klines || klines.length === 0) {
            console.warn('❌ 十字光标移动失败: 没有K线数据');
            this.showMessage('没有K线数据，无法移动十字光标', 'warning');
            return;
        }

        // 如果十字光标未激活，从最后一根K线开始
        if (this.currentKlineIndex === -1) {
            this.currentKlineIndex = klines.length - 1;
            console.log(`🎯 十字光标初始化到最后位置: ${this.currentKlineIndex}`);
        } else {
            const oldIndex = this.currentKlineIndex;
            this.currentKlineIndex += direction;
            console.log(`🎯 十字光标移动: ${oldIndex} -> ${this.currentKlineIndex}`);
        }

        // 边界检查
        const newIndex = Math.max(0, Math.min(klines.length - 1, this.currentKlineIndex));
        if (newIndex !== this.currentKlineIndex) {
            console.log(`🎯 边界修正: ${this.currentKlineIndex} -> ${newIndex}`);
            this.currentKlineIndex = newIndex;
        }

        // 激活十字光标并更新显示
        this.crosshairActive = true;
        console.log(`🎯 更新十字光标显示，当前索引: ${this.currentKlineIndex}`);
        this.updateCrosshairDisplay(klines);

        // 显示移动提示
        const kline = klines[this.currentKlineIndex];
        if (kline) {
            this.showKeyboardMessage(`十字光标: ${kline.date} 收盘${kline.close}`, '#2196f3');
        }

        console.log(`🎯 移动到K线索引: ${this.currentKlineIndex}/${klines.length - 1}`);
    }

    // 快速移动十字光标
    moveCrosshairFast(direction, klines) {
        if (!klines || klines.length === 0) return;

        // 如果十字光标未激活，从最后一根K线开始
        if (this.currentKlineIndex === -1) {
            this.currentKlineIndex = klines.length - 1;
        } else {
            this.currentKlineIndex += direction;
        }

        // 边界检查
        this.currentKlineIndex = Math.max(0, Math.min(klines.length - 1, this.currentKlineIndex));

        // 激活十字光标并更新显示
        this.crosshairActive = true;
        this.updateCrosshairDisplay(klines);

        console.log(`🎯 快速移动到K线索引: ${this.currentKlineIndex}/${klines.length - 1} (步长: ${direction})`);
    }

    // 跳转到第一根K线
    jumpToFirstKline(klines) {
        if (!klines || klines.length === 0) return;

        this.currentKlineIndex = 0;
        this.crosshairActive = true;
        this.updateCrosshairDisplay(klines);

        console.log(`🎯 跳转到第一根K线: ${this.currentKlineIndex}/${klines.length - 1}`);
    }

    // 跳转到最后一根K线
    jumpToLastKline(klines) {
        if (!klines || klines.length === 0) return;

        this.currentKlineIndex = klines.length - 1;
        this.crosshairActive = true;
        this.updateCrosshairDisplay(klines);

        console.log(`🎯 跳转到最后一根K线: ${this.currentKlineIndex}/${klines.length - 1}`);
    }

    // 居中显示十字光标
    centerCrosshair(klines) {
        if (!klines || klines.length === 0) return;

        this.currentKlineIndex = Math.floor(klines.length / 2);
        this.crosshairActive = true;
        this.updateCrosshairDisplay(klines);

        console.log(`🎯 居中显示十字光标: ${this.currentKlineIndex}/${klines.length - 1}`);
    }

    // 切换十字光标锁定状态
    toggleCrosshairLock() {
        this.crosshairLocked = !this.crosshairLocked;

        const message = this.crosshairLocked ? '十字光标已锁定' : '十字光标已解锁';
        const color = this.crosshairLocked ? '#ffc107' : '#2ed573';

        this.showKeyboardMessage(message, color);
        console.log(`🔒 ${message}`);
    }

    // 切换数据面板显示
    toggleDataPanel() {
        const panel = document.getElementById('crosshair-data-panel');
        if (!panel) return;

        const isVisible = panel.style.display !== 'none';
        panel.style.display = isVisible ? 'none' : 'block';

        const message = isVisible ? '数据面板已隐藏' : '数据面板已显示';
        this.showKeyboardMessage(message, '#2196f3');
        console.log(`📊 ${message}`);
    }

    // 切换十字光标网格显示
    toggleCrosshairGrid() {
        this.showCrosshairGrid = !this.showCrosshairGrid;

        const message = this.showCrosshairGrid ? '十字光标网格已开启' : '十字光标网格已关闭';
        const color = this.showCrosshairGrid ? '#4caf50' : '#ff9800';

        this.showKeyboardMessage(message, color);

        // 如果十字光标当前激活，重新绘制以应用网格设置
        if (this.crosshairActive && this.lastKlineData && this.lastKlineData.klines) {
            const klines = this.getStandardKlineData(this.lastKlineData.klines);
            this.updateCrosshairDisplay(klines);
        }

        console.log(`🎯 ${message}`);
    }

    // 测试数据面板功能
    testDataPanel() {
        console.log('🧪 开始测试数据面板功能');

        // 🔧 强制测试MACD数据
        if (this.lastIndicatorData && this.lastIndicatorData.indicators && this.lastIndicatorData.indicators.macd) {
            const macdData = this.lastIndicatorData.indicators.macd;
            const lastIndex = macdData.macd.length - 1;
            console.log('🔧 强制测试MACD数据:', {
                lastIndex: lastIndex,
                lastMacd: macdData.macd[lastIndex],
                lastDif: macdData.dif[lastIndex],
                lastDea: macdData.dea[lastIndex],
                expectedValues: {
                    dif: 0.2121,
                    dea: 0.1866,
                    macd: 0.0511
                }
            });
        }

        // 检查面板是否存在
        let panel = document.getElementById('crosshair-data-panel');
        if (!panel) {
            console.log('📊 数据面板不存在，创建新面板');
            this.createDataDisplayPanel();
            panel = document.getElementById('crosshair-data-panel');
        }

        if (panel) {
            // 强制显示面板
            panel.style.display = 'block';
            console.log('✅ 数据面板已强制显示');

            // 如果有K线数据，使用最后一根K线进行测试
            if (this.lastKlineData && this.lastKlineData.klines) {
                const klines = this.getStandardKlineData(this.lastKlineData.klines);
                const lastKline = klines[klines.length - 1];
                const lastIndex = klines.length - 1;

                console.log('📊 使用最后一根K线进行测试:', lastKline);
                this.updateDataPanel(lastKline, lastIndex);

                // 激活十字光标
                this.currentKlineIndex = lastIndex;
                this.crosshairActive = true;

                this.showKeyboardMessage('数据面板测试完成 (T键)', '#4caf50');
            } else {
                console.warn('⚠️ 没有K线数据可用于测试');
                this.showKeyboardMessage('没有K线数据可用于测试', '#ff9800');
            }
        } else {
            console.error('❌ 无法创建数据面板');
            this.showKeyboardMessage('数据面板创建失败', '#ff4757');
        }
    }

    // 显示快捷键帮助面板
    showKeyboardHelp() {
        // 移除旧的帮助面板
        const oldHelp = document.getElementById('keyboard-help-panel');
        if (oldHelp) {
            oldHelp.remove();
            return; // 如果已经显示，则隐藏
        }

        // 创建帮助面板
        const helpPanel = document.createElement('div');
        helpPanel.id = 'keyboard-help-panel';
        helpPanel.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.95);
            color: #ffffff;
            padding: 24px;
            border-radius: 12px;
            font-size: 13px;
            z-index: 10002;
            border: 2px solid #ffc107;
            box-shadow: 0 8px 32px rgba(0,0,0,0.8);
            backdrop-filter: blur(20px);
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        `;

        helpPanel.innerHTML = `
            <div style="text-align: center; margin-bottom: 20px;">
                <h3 style="color: #ffc107; margin: 0; font-size: 18px;">📚 操作员快捷键指南</h3>
                <p style="color: #888; margin: 8px 0 0 0; font-size: 11px;">专业股票分析系统 - 快捷键完全手册</p>
            </div>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <h4 style="color: #2196f3; margin: 0 0 12px 0; border-bottom: 1px solid #333; padding-bottom: 4px;">🎮 十字光标导航</h4>
                    <div style="line-height: 1.6; font-size: 12px;">
                        <div style="color: #ffc107; font-weight: bold; margin-bottom: 4px;">WASD游戏化操作:</div>
                        <div><kbd>A D</kbd> 单步移动</div>
                        <div><kbd>W S</kbd> 快速移动(5步)</div>
                        <div style="color: #ffc107; font-weight: bold; margin: 8px 0 4px 0;">传统方向键:</div>
                        <div><kbd>← →</kbd> 单步移动</div>
                        <div><kbd>↑ ↓</kbd> 快速移动(5步)</div>
                        <div style="color: #ffc107; font-weight: bold; margin: 8px 0 4px 0;">快速跳转:</div>
                        <div><kbd>Home</kbd> 跳转到开始</div>
                        <div><kbd>End</kbd> 跳转到结束</div>
                        <div><kbd>Z</kbd> 快速向前(10步)</div>
                        <div><kbd>X</kbd> 快速向后(10步)</div>
                    </div>
                </div>

                <div>
                    <h4 style="color: #ff9800; margin: 0 0 12px 0; border-bottom: 1px solid #333; padding-bottom: 4px;">⚡ 周期切换</h4>
                    <div style="line-height: 1.6; font-size: 12px;">
                        <div style="color: #ffc107; font-weight: bold; margin-bottom: 4px;">数字键直接切换:</div>
                        <div><kbd>1</kbd> 分时图</div>
                        <div><kbd>2</kbd> 1分钟</div>
                        <div><kbd>3</kbd> 5分钟</div>
                        <div><kbd>4</kbd> 15分钟</div>
                        <div><kbd>5</kbd> 30分钟</div>
                        <div><kbd>6</kbd> 60分钟</div>
                        <div><kbd>7</kbd> 120分钟</div>
                        <div><kbd>8</kbd> 日K线</div>
                        <div><kbd>9</kbd> 周K线</div>
                        <div><kbd>0</kbd> 月K线</div>
                        <div style="margin-top: 8px;"><kbd>Shift+Q/E</kbd> 周期导航</div>
                    </div>
                </div>

                <div>
                    <h4 style="color: #4caf50; margin: 0 0 12px 0; border-bottom: 1px solid #333; padding-bottom: 4px;">📈 均线控制</h4>
                    <div style="line-height: 1.6; font-size: 12px;">
                        <div style="color: #ffc107; font-weight: bold; margin-bottom: 4px;">Shift + 字母键:</div>
                        <div><kbd>Shift+W</kbd> MA10</div>
                        <div><kbd>Shift+E</kbd> MA20</div>
                        <div><kbd>Shift+R</kbd> MA30</div>
                        <div><kbd>Shift+T</kbd> MA60</div>
                        <div><kbd>Shift+Y</kbd> MA120</div>
                        <div><kbd>Shift+U</kbd> MA250</div>
                    </div>
                </div>

                <div>
                    <h4 style="color: #e91e63; margin: 0 0 12px 0; border-bottom: 1px solid #333; padding-bottom: 4px;">🔧 功能控制</h4>
                    <div style="line-height: 1.6; font-size: 12px;">
                        <div><kbd>Space</kbd> 锁定/解锁</div>
                        <div><kbd>C</kbd> 居中显示</div>
                        <div><kbd>V</kbd> 切换数据面板</div>
                        <div><kbd>B</kbd> 切换网格</div>
                        <div><kbd>ESC</kbd> 退出十字光标</div>
                        <div><kbd>\`</kbd> 刷新数据</div>
                        <div><kbd>F1</kbd> 显示帮助</div>
                        <div><kbd>F2</kbd> 测试面板</div>
                        <div style="margin-top: 8px; color: #888; font-size: 11px;">
                            💡 完全避免浏览器快捷键冲突
                        </div>
                    </div>
                </div>
            </div>

            <div style="text-align: center; margin-top: 20px; padding-top: 16px; border-top: 1px solid #333;">
                <button onclick="document.getElementById('keyboard-help-panel').remove()" style="
                    background: #ffc107;
                    color: #000;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 6px;
                    cursor: pointer;
                    font-weight: bold;
                ">关闭帮助 (F1)</button>
            </div>
        `;

        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            #keyboard-help-panel kbd {
                background: #333;
                color: #ffc107;
                padding: 2px 6px;
                border-radius: 3px;
                font-size: 11px;
                font-weight: bold;
                margin-right: 8px;
                display: inline-block;
                min-width: 20px;
                text-align: center;
            }
        `;
        document.head.appendChild(style);

        document.body.appendChild(helpPanel);

        // 自动关闭（可选）
        setTimeout(() => {
            if (helpPanel && helpPanel.parentNode) {
                // 可以设置自动关闭时间，这里注释掉让用户手动关闭
                // helpPanel.remove();
            }
        }, 30000); // 30秒后自动关闭

        console.log('📚 显示快捷键帮助面板');
    }

    // 显示键盘操作提示消息
    showKeyboardMessage(message, color = '#ffc107') {
        // 移除旧的提示
        const oldMessage = document.getElementById('keyboard-message');
        if (oldMessage) {
            oldMessage.remove();
        }

        // 创建新的提示
        const messageDiv = document.createElement('div');
        messageDiv.id = 'keyboard-message';
        messageDiv.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            color: ${color};
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: bold;
            z-index: 10001;
            border: 2px solid ${color};
            box-shadow: 0 4px 12px rgba(0,0,0,0.5);
            backdrop-filter: blur(10px);
        `;
        messageDiv.textContent = message;

        document.body.appendChild(messageDiv);

        // 自动移除提示
        setTimeout(() => {
            if (messageDiv && messageDiv.parentNode) {
                messageDiv.remove();
            }
        }, 1500);
    }

    // 优化的十字光标显示更新 - 支持鼠标坐标和高性能渲染
    updateCrosshairDisplay(klines, mouseX = null, mouseY = null) {
        console.log('🎯 开始更新十字光标显示，索引:', this.currentKlineIndex, '激活状态:', this.crosshairActive);

        if (!this.crosshairActive || this.currentKlineIndex === -1) {
            console.warn('⚠️ 十字光标未激活或索引无效');
            return;
        }

        const currentKline = klines[this.currentKlineIndex];
        if (!currentKline) {
            console.warn('⚠️ 当前K线数据无效');
            return;
        }

        console.log('📊 当前K线数据:', currentKline);

        // 高性能十字光标绘制 - 直接使用鼠标坐标避免重复计算
        this.drawOptimizedCrosshair(klines, this.currentKlineIndex, mouseX, mouseY);

        // 立即更新数据面板，确保显示
        console.log('📊 开始更新数据面板...');
        this.updateDataPanel(currentKline, this.currentKlineIndex);
    }

    // 隐藏十字光标
    hideCrosshair() {
        this.crosshairActive = false;
        this.currentKlineIndex = -1;

        // 清除十字光标叠加Canvas
        const crosshairCanvas = document.getElementById('crosshair-overlay-canvas');
        if (crosshairCanvas && crosshairCanvas.getContext) {
            const ctx = crosshairCanvas.getContext('2d');
            ctx.clearRect(0, 0, crosshairCanvas.width, crosshairCanvas.height);
        }

        // 清除子图十字光标叠加层
        this.clearAllSubChartsCrosshair();

        // 隐藏数据面板
        this.hideDataPanel();

        console.log('🎯 隐藏十字光标');
    }

    // 清除之前的十字光标
    clearPreviousCrosshair() {
        // 清除主图十字光标
        this.clearMainChartCrosshair();

        // 清除子图十字光标
        this.clearSubChartsCrosshair();
    }

    // 清除主图十字光标
    clearMainChartCrosshair() {
        const canvas = document.getElementById('main-kline-chart') ||
                      document.getElementById('kline-chart') ||
                      document.getElementById('main-chart') ||
                      document.getElementById('fallback-chart');

        if (canvas && canvas.getContext) {
            // 只清除十字光标区域，不重绘整个图表
            const ctx = canvas.getContext('2d');

            // 保存当前的合成操作
            const oldCompositeOperation = ctx.globalCompositeOperation;

            // 使用destination-out模式清除十字光标
            ctx.globalCompositeOperation = 'source-over';

            // 重新绘制图表（这里可以优化为只重绘必要部分）
            if (this.lastKlineData && this.lastIndicatorData) {
                // 暂时禁用十字光标绘制
                const wasActive = this.crosshairActive;
                this.crosshairActive = false;

                // 重绘图表
                this.renderKlineChart(this.lastKlineData, this.lastIndicatorData);

                // 恢复十字光标状态
                this.crosshairActive = wasActive;
            }

            // 恢复合成操作
            ctx.globalCompositeOperation = oldCompositeOperation;
        }
    }

    // 清除子图十字光标
    clearSubChartsCrosshair() {
        // 清除成交量子图十字光标
        const volumeCanvas = document.getElementById('volume-canvas') ||
                           document.getElementById('volumeChart') ||
                           document.querySelector('[id*="volume"]');

        if (volumeCanvas && volumeCanvas.getContext) {
            // 子图重绘逻辑可以在这里实现
        }

        // 清除技术指标子图十字光标
        this.activeIndicators.forEach(indicator => {
            if (indicator !== 'volume') {
                const canvas = document.getElementById(`${indicator}-canvas`) ||
                              document.getElementById(`${indicator}Chart`) ||
                              document.querySelector(`[id*="${indicator}"]`);

                if (canvas && canvas.getContext) {
                    // 子图重绘逻辑可以在这里实现
                }
            }
        });
    }

    // 创建数据显示面板
    createDataDisplayPanel() {
        // 移除旧面板
        const oldPanel = document.getElementById('crosshair-data-panel');
        if (oldPanel) {
            oldPanel.remove();
        }

        const panel = document.createElement('div');
        panel.id = 'crosshair-data-panel';
        panel.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            background: rgba(30, 35, 41, 0.95);
            border: 2px solid var(--primary-color);
            border-radius: 8px;
            padding: 16px;
            color: #d1d4dc;
            font-size: 12px;
            font-family: 'Courier New', monospace;
            display: none;
            z-index: 10000;
            min-width: 280px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.4);
            backdrop-filter: blur(10px);
        `;

        panel.innerHTML = `
            <div class="panel-header" style="
                color: var(--primary-color);
                font-weight: bold;
                font-size: 14px;
                margin-bottom: 12px;
                border-bottom: 1px solid var(--border-color);
                padding-bottom: 8px;
                display: flex;
                justify-content: space-between;
                align-items: center;
            ">
                <span>📊 专业数据面板</span>
                <div style="display: flex; align-items: center; gap: 8px;">
                    <span id="kline-position" style="font-size: 10px; color: var(--text-muted);">--/--</span>
                    <button id="panel-pin-btn" style="
                        background: none;
                        border: 1px solid var(--border-color);
                        color: var(--text-muted);
                        padding: 2px 6px;
                        border-radius: 3px;
                        font-size: 10px;
                        cursor: pointer;
                    ">📌</button>
                </div>
            </div>

            <div class="panel-hotkeys" style="
                font-size: 9px;
                color: var(--text-muted);
                margin-bottom: 12px;
                padding: 6px;
                background: rgba(255,255,255,0.05);
                border-radius: 4px;
                line-height: 1.4;
            ">
                <div style="margin-bottom: 4px;"><strong>🎮 十字光标导航 (无冲突设计):</strong></div>
                WASD 导航 | ← → ↑ ↓ 传统 | Q/E 首尾 | Z/X 快速<br>
                Space 锁定 | C 居中 | V 面板 | B 网格 | ESC 退出 | F1 帮助<br>
                <div style="margin-top: 4px;"><strong>⚡ 系统快捷键:</strong></div>
                1-0 周期切换 | Shift+Q/W/E/R 均线 | Shift+Q/E 周期导航 | \` 刷新
            </div>

            <div class="kline-data-section">
                <div class="section-title" style="
                    color: var(--accent-color);
                    font-weight: bold;
                    margin-bottom: 8px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                ">
                    <span>📈 K线数据</span>
                    <span id="kline-trend-indicator" style="font-size: 10px;">--</span>
                </div>
                <div class="kline-data-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 12px;">
                    <div>日期: <span id="kline-date" style="color: #ffffff; font-weight: bold;">--</span></div>
                    <div>时间: <span id="kline-time" style="color: #ffffff; font-weight: bold;">--</span></div>
                    <div>开盘: <span id="kline-open" style="color: #ffffff;">--</span></div>
                    <div>收盘: <span id="kline-close" style="color: #ffffff;">--</span></div>
                    <div>最高: <span id="kline-high" style="color: #ff4757;">--</span></div>
                    <div>最低: <span id="kline-low" style="color: #2ed573;">--</span></div>
                    <div>涨跌: <span id="kline-change" style="font-weight: bold;">--</span></div>
                    <div>涨幅: <span id="kline-change-percent" style="font-weight: bold;">--</span></div>
                    <div>振幅: <span id="kline-amplitude" style="color: #ffc107;">--</span></div>
                    <div>实体: <span id="kline-body-ratio" style="color: #9c27b0;">--</span></div>
                </div>
            </div>

            <div class="volume-data-section">
                <div class="section-title" style="color: var(--accent-color); font-weight: bold; margin-bottom: 8px;">
                    📊 成交数据
                </div>
                <div class="volume-data-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 12px;">
                    <div>成交量: <span id="volume-value" style="color: #ffc107; font-weight: bold;">--</span></div>
                    <div>成交额: <span id="turnover-value" style="color: #ffc107; font-weight: bold;">--</span></div>
                    <div>均价: <span id="avg-price" style="color: #2196f3;">--</span></div>
                    <div>量比: <span id="volume-ratio" style="color: #4caf50;">--</span></div>
                </div>
            </div>

            <div class="ma-data-section">
                <div class="section-title" style="color: var(--accent-color); font-weight: bold; margin-bottom: 8px;">
                    📉 均线数据
                </div>
                <div id="ma-data-content" style="font-size: 11px;">
                    <!-- 动态生成均线数据 -->
                </div>
            </div>

            <div class="indicators-data-section">
                <div class="section-title" style="color: var(--accent-color); font-weight: bold; margin-bottom: 8px;">
                    📈 技术指标
                </div>
                <div id="indicators-data-content" style="font-size: 11px;">
                    <!-- 动态生成技术指标数据 -->
                </div>
            </div>

            <div class="analysis-section">
                <div class="section-title" style="color: var(--accent-color); font-weight: bold; margin-bottom: 8px;">
                    🎯 快速分析
                </div>
                <div id="quick-analysis-content" style="font-size: 10px; color: var(--text-muted);">
                    <!-- 动态生成快速分析 -->
                </div>
            </div>

            <div id="data-sync-status" style="margin-top: 8px;">
                <!-- 数据同步状态显示 -->
            </div>
        `;

        document.body.appendChild(panel);

        // 绑定固定按钮事件
        const pinBtn = panel.querySelector('#panel-pin-btn');
        if (pinBtn) {
            pinBtn.addEventListener('click', () => {
                this.togglePanelPin(panel, pinBtn);
            });
        }

        console.log('✅ 增强数据显示面板创建完成');

        // 验证面板是否正确添加到DOM
        setTimeout(() => {
            const verifyPanel = document.getElementById('crosshair-data-panel');
            if (verifyPanel) {
                console.log('✅ 数据面板验证成功，已添加到DOM');
                console.log('📊 面板样式:', verifyPanel.style.cssText);
            } else {
                console.error('❌ 数据面板验证失败，未找到在DOM中');
            }
        }, 100);
    }

    // 切换面板固定状态
    togglePanelPin(panel, button) {
        const isPinned = panel.dataset.pinned === 'true';

        if (isPinned) {
            // 取消固定
            panel.dataset.pinned = 'false';
            panel.style.position = 'fixed';
            button.textContent = '📌';
            button.style.color = 'var(--text-muted)';
            this.showKeyboardMessage('面板已取消固定', '#2196f3');
        } else {
            // 固定面板
            panel.dataset.pinned = 'true';
            panel.style.position = 'fixed';
            button.textContent = '📍';
            button.style.color = '#ffc107';
            this.showKeyboardMessage('面板已固定', '#ffc107');
        }
    }

    // 更新数据面板 - 增强数据同步检查
    updateDataPanel(kline, index) {
        console.log('📊 开始更新数据面板，索引:', index, 'K线数据:', kline);

        const panel = document.getElementById('crosshair-data-panel');
        if (!panel) {
            console.warn('⚠️ 数据面板未找到，尝试重新创建');
            this.createDataDisplayPanel();
            return;
        }

        // 数据同步检查
        if (!this.lastKlineData || !this.lastKlineData.klines) {
            console.error('❌ K线数据缺失，无法更新数据面板');
            panel.innerHTML = '<div style="color: #ff4757; padding: 20px; text-align: center;">K线数据缺失</div>';
            return;
        }

        const klines = this.getStandardKlineData(this.lastKlineData.klines);
        if (index < 0 || index >= klines.length) {
            console.error(`❌ 索引超出范围: ${index}, K线数据长度: ${klines.length}`);
            panel.innerHTML = '<div style="color: #ff4757; padding: 20px; text-align: center;">数据索引错误</div>';
            return;
        }

        // 数据一致性检查
        const currentKline = klines[index];
        if (!currentKline) {
            console.error(`❌ 当前索引${index}的K线数据为空`);
            return;
        }

        // 技术指标数据同步检查
        if (this.lastIndicatorData && this.lastIndicatorData.indicators) {
            console.log('📊 技术指标数据同步检查通过');
        } else {
            console.warn('⚠️ 技术指标数据缺失或不完整');
        }

        // 显示面板
        panel.style.display = 'block';
        console.log('📊 数据面板已显示');

        try {
            // 更新K线数据
            this.updateKlineDataInPanel(currentKline, index);
            console.log('✅ K线数据更新完成');

            // 更新技术指标数据
            this.updateIndicatorsDataInPanel(index);
            console.log('✅ 技术指标数据更新完成');

            // 更新均线数据
            this.updateMADataInPanel(index);
            console.log('✅ 均线数据更新完成');

            // 更新数据同步状态显示
            this.updateDataSyncStatus(index, klines.length);

        } catch (error) {
            console.error('❌ 数据面板更新出错:', error);
            console.error('❌ 错误堆栈:', error.stack);
        }
    }

    // 更新数据同步状态显示
    updateDataSyncStatus(index, totalLength) {
        const statusElement = document.getElementById('data-sync-status');
        if (statusElement) {
            const klineStatus = this.lastKlineData ? '✅' : '❌';
            const indicatorStatus = this.lastIndicatorData ? '✅' : '❌';
            statusElement.innerHTML = `
                <div style="font-size: 9px; color: var(--text-muted); margin-top: 8px; padding: 4px; background: rgba(0,0,0,0.3); border-radius: 3px;">
                    数据状态: K线${klineStatus} 指标${indicatorStatus} | 位置: ${index + 1}/${totalLength}
                </div>
            `;
        }
    }

    // 增强的K线数据显示 - 包含更多专业信息
    updateKlineDataInPanel(kline, index) {
        const klines = this.cachedKlines || this.getStandardKlineData(this.lastKlineData.klines);

        // 更新位置信息
        const positionElement = document.getElementById('kline-position');
        if (positionElement) {
            positionElement.textContent = `${index + 1}/${klines.length}`;
        }

        const dateTime = kline.date.split(' ');
        const date = dateTime[0] || kline.date;
        const time = dateTime[1] || '';

        // 安全更新DOM元素
        const updateElement = (id, value) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            } else {
                console.warn(`⚠️ 元素未找到: ${id}`);
            }
        };

        updateElement('kline-date', date);
        updateElement('kline-time', time);
        updateElement('kline-open', kline.open);
        updateElement('kline-close', kline.close);
        updateElement('kline-high', kline.high);
        updateElement('kline-low', kline.low);

        // 计算涨跌 - 修正：应该是相对于前一日收盘价
        // 如果有前一根K线，使用前一根的收盘价作为基准
        let prevClose = kline.open; // 默认使用开盘价
        if (index > 0 && klines[index - 1]) {
            prevClose = klines[index - 1].close;
        }

        const change = (kline.close - prevClose).toFixed(2);
        const changePercent = ((change / prevClose) * 100).toFixed(2);
        const isUp = change >= 0;
        // 中国股市颜色约定：红涨绿跌
        const changeColor = isUp ? '#ff4757' : '#2ed573';

        const changeElement = document.getElementById('kline-change');
        const changePercentElement = document.getElementById('kline-change-percent');

        if (changeElement) {
            changeElement.textContent = `${isUp ? '+' : ''}${change}`;
            changeElement.style.color = changeColor;
        }

        if (changePercentElement) {
            changePercentElement.textContent = `${isUp ? '+' : ''}${changePercent}%`;
            changePercentElement.style.color = changeColor;
        }

        // 计算振幅 - 修正：应该基于前一日收盘价
        const amplitude = (((kline.high - kline.low) / prevClose) * 100).toFixed(2);
        updateElement('kline-amplitude', `${amplitude}%`);

        // 计算实体比例
        const bodyRatio = (Math.abs(kline.close - kline.open) / (kline.high - kline.low) * 100).toFixed(1);
        updateElement('kline-body-ratio', `${bodyRatio}%`);

        // 更新趋势指示器
        const trendElement = document.getElementById('kline-trend-indicator');
        if (trendElement) {
            const trendText = this.analyzeTrend(kline, isUp);
            trendElement.innerHTML = trendText;
        }

        // 更新成交量数据
        if (kline.volume) {
            updateElement('volume-value', this.formatVolume(kline.volume));

            // 计算均价
            if (kline.turnover && kline.volume) {
                const avgPrice = (kline.turnover / kline.volume).toFixed(2);
                updateElement('avg-price', avgPrice);
            } else {
                updateElement('avg-price', ((kline.high + kline.low + kline.close) / 3).toFixed(2));
            }

            // 计算量比（简化版本）
            const volumeRatio = this.calculateVolumeRatio(klines, index);
            updateElement('volume-ratio', volumeRatio);
        } else {
            updateElement('volume-value', '--');
            updateElement('avg-price', '--');
            updateElement('volume-ratio', '--');
        }

        if (kline.turnover) {
            updateElement('turnover-value', this.formatTurnover(kline.turnover));
        } else {
            updateElement('turnover-value', '--');
        }

        // 更新快速分析
        this.updateQuickAnalysis(kline, index, klines);
    }

    // 分析K线趋势
    analyzeTrend(kline, isUp) {
        const bodySize = Math.abs(kline.close - kline.open);
        const totalSize = kline.high - kline.low;
        const bodyRatio = bodySize / totalSize;

        let trendIcon = '';
        let trendText = '';
        let color = '';

        if (bodyRatio > 0.7) {
            // 大实体
            if (isUp) {
                trendIcon = '🚀';
                trendText = '强涨';
                color = '#ff4757'; // 红色表示上涨
            } else {
                trendIcon = '📉';
                trendText = '强跌';
                color = '#2ed573'; // 绿色表示下跌
            }
        } else if (bodyRatio > 0.3) {
            // 中等实体
            if (isUp) {
                trendIcon = '📈';
                trendText = '上涨';
                color = '#ff4757'; // 红色表示上涨
            } else {
                trendIcon = '📉';
                trendText = '下跌';
                color = '#2ed573'; // 绿色表示下跌
            }
        } else {
            // 小实体/十字星
            trendIcon = '⚖️';
            trendText = '震荡';
            color = '#ffc107';
        }

        return `<span style="color: ${color};">${trendIcon} ${trendText}</span>`;
    }

    // 计算量比
    calculateVolumeRatio(klines, currentIndex) {
        if (currentIndex < 5) return '--';

        const currentVolume = klines[currentIndex].volume || 0;
        const avgVolume = klines.slice(Math.max(0, currentIndex - 5), currentIndex)
            .reduce((sum, k) => sum + (k.volume || 0), 0) / 5;

        if (avgVolume === 0) return '--';

        const ratio = (currentVolume / avgVolume).toFixed(2);
        return ratio;
    }

    // 更新快速分析
    updateQuickAnalysis(kline, index, klines) {
        const content = document.getElementById('quick-analysis-content');
        if (!content) return;

        const analysis = [];

        // 价格位置分析
        const prices = klines.map(k => [k.high, k.low]).flat();
        const maxPrice = Math.max(...prices);
        const minPrice = Math.min(...prices);
        const pricePosition = ((kline.close - minPrice) / (maxPrice - minPrice) * 100).toFixed(1);

        analysis.push(`价格位置: ${pricePosition}% (${pricePosition > 70 ? '高位' : pricePosition < 30 ? '低位' : '中位'})`);

        // 成交量分析
        if (kline.volume) {
            const volumes = klines.map(k => k.volume || 0);
            const avgVolume = volumes.reduce((sum, v) => sum + v, 0) / volumes.length;
            const volumeLevel = kline.volume / avgVolume;

            let volumeDesc = '';
            if (volumeLevel > 2) volumeDesc = '放量';
            else if (volumeLevel > 1.5) volumeDesc = '温和放量';
            else if (volumeLevel < 0.5) volumeDesc = '缩量';
            else volumeDesc = '正常';

            analysis.push(`成交量: ${volumeDesc} (${volumeLevel.toFixed(2)}倍)`);
        }

        // K线形态分析
        const bodySize = Math.abs(kline.close - kline.open);
        const upperShadow = kline.high - Math.max(kline.open, kline.close);
        const lowerShadow = Math.min(kline.open, kline.close) - kline.low;

        if (upperShadow > bodySize * 2) {
            analysis.push('形态: 长上影线 (上方压力)');
        } else if (lowerShadow > bodySize * 2) {
            analysis.push('形态: 长下影线 (下方支撑)');
        } else if (bodySize < (kline.high - kline.low) * 0.2) {
            analysis.push('形态: 十字星 (变盘信号)');
        }

        content.innerHTML = analysis.join('<br>');
    }

    // 更新技术指标数据显示 - 使用新的API数据
    updateIndicatorsDataInPanel(index) {
        console.log(`📊 更新技术指标数据面板: 索引=${index}`);

        const content = document.getElementById('indicators-data-content');
        if (!content) {
            console.error('❌ 技术指标内容容器未找到');
            return;
        }

        // 验证API数据
        if (!this.lastIndicatorData) {
            console.warn('⚠️ 没有技术指标数据 (lastIndicatorData为空)');
            content.innerHTML = '<div style="color: var(--text-muted);">正在加载技术指标数据...</div>';
            return;
        }

        if (!this.lastIndicatorData.indicators) {
            console.warn('⚠️ 技术指标数据结构异常 (indicators字段为空)');
            content.innerHTML = '<div style="color: var(--text-muted);">技术指标数据结构异常</div>';
            return;
        }

        console.log('📊 使用API技术指标数据，结构:', Object.keys(this.lastIndicatorData.indicators));
        console.log('📊 当前激活的指标:', this.activeIndicators);

        let html = '';
        const indicators = this.lastIndicatorData.indicators;

        // MACD指标 - 使用API数据
        if (this.activeIndicators.includes('macd')) {
            console.log('📊 处理API MACD指标数据...');

            if (indicators.macd) {
                const macdData = indicators.macd;
                console.log('📊 API MACD数据结构:', Object.keys(macdData));
                console.log('📊 API MACD数据长度:', {
                    macd: macdData.macd?.length || 0,
                    dif: macdData.dif?.length || 0,
                    dea: macdData.dea?.length || 0
                });

                // 🔧 调试：检查数据长度和索引
                console.log('🔧 MACD数据调试:', {
                    index: index,
                    macdLength: macdData.macd?.length || 0,
                    difLength: macdData.dif?.length || 0,
                    deaLength: macdData.dea?.length || 0,
                    lastIndex: (macdData.macd?.length || 0) - 1,
                    isLastIndex: index === (macdData.macd?.length || 0) - 1
                });

                // 直接使用API返回的数据
                const macdValue = this.getApiIndicatorValue(macdData.macd, index);
                const difValue = this.getApiIndicatorValue(macdData.dif, index);
                const deaValue = this.getApiIndicatorValue(macdData.dea, index);

                console.log('📊 API MACD值:', { macdValue, difValue, deaValue, index });

                // 🔧 调试：显示最新几个数据
                const lastIndex = macdData.macd.length - 1;
                console.log('🔧 最新MACD数据:', {
                    lastIndex: lastIndex,
                    lastMacd: macdData.macd[lastIndex],
                    lastDif: macdData.dif[lastIndex],
                    lastDea: macdData.dea[lastIndex]
                });

                // 🔧 强制测试：如果是最新索引，显示警告
                if (index === lastIndex) {
                    console.log('🚨 当前正在显示最新数据索引！');
                    console.log('🚨 应该显示的值:', {
                        dif: macdData.dif[lastIndex],
                        dea: macdData.dea[lastIndex],
                        macd: macdData.macd[lastIndex]
                    });
                }

                // 检查是否有有效数据
                const hasValidData = macdValue !== '--' && difValue !== '--' && deaValue !== '--';

                if (hasValidData) {
                    // 显示API数据
                    html += `
                        <div style="margin-bottom: 8px; padding: 6px; background: rgba(255, 193, 7, 0.1); border-radius: 4px;">
                            <div style="color: #ffc107; font-weight: bold; margin-bottom: 4px;">
                                MACD 🌐 (API数据)
                            </div>
                            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 8px; font-size: 10px;">
                                <div>DIF: <span style="color: #ffc107;">${difValue}</span></div>
                                <div>DEA: <span style="color: #2196f3;">${deaValue}</span></div>
                                <div>MACD: <span style="color: ${parseFloat(macdValue) >= 0 ? '#ff4757' : '#2ed573'};">${macdValue}</span></div>
                            </div>
                            <div style="color: #4caf50; font-size: 8px; margin-top: 2px;">✅ 来自财经API的专业数据</div>
                        </div>
                    `;
                } else {
                    // 显示数据状态
                    const totalLength = macdData.macd ? macdData.macd.length : 0;
                    let statusMessage = '';

                    if (index >= totalLength) {
                        statusMessage = `索引超出范围 (${index}/${totalLength})`;
                    } else if (macdData.macd && macdData.macd[index] === null) {
                        statusMessage = '前期数据不足，MACD计算中...';
                    } else {
                        statusMessage = '数据加载中...';
                    }

                    html += `
                        <div style="margin-bottom: 8px; padding: 6px; background: rgba(255, 193, 7, 0.1); border-radius: 4px;">
                            <div style="color: #ffc107; font-weight: bold; margin-bottom: 4px;">MACD 🌐</div>
                            <div style="color: var(--text-muted); font-size: 9px;">${statusMessage}</div>
                            <div style="color: var(--text-muted); font-size: 8px;">位置: ${index + 1}/${totalLength}</div>
                        </div>
                    `;
                }
            } else {
                console.warn('⚠️ API未返回MACD数据');
                html += `
                    <div style="margin-bottom: 8px; padding: 6px; background: rgba(255, 193, 7, 0.1); border-radius: 4px;">
                        <div style="color: #ffc107; font-weight: bold; margin-bottom: 4px;">MACD</div>
                        <div style="color: var(--text-muted); font-size: 10px;">API数据加载中...</div>
                    </div>
                `;
            }
        }

        // KDJ指标 - 使用API数据
        if (this.activeIndicators.includes('kdj')) {
            console.log('📊 处理API KDJ指标数据...');

            if (indicators.kdj) {
                const kdjData = indicators.kdj;
                console.log('📊 API KDJ数据结构:', Object.keys(kdjData));
                console.log('📊 API KDJ数据长度:', {
                    k: kdjData.k?.length || 0,
                    d: kdjData.d?.length || 0,
                    j: kdjData.j?.length || 0
                });

                // 直接使用API返回的数据
                const kValue = this.getApiIndicatorValue(kdjData.k, index);
                const dValue = this.getApiIndicatorValue(kdjData.d, index);
                const jValue = this.getApiIndicatorValue(kdjData.j, index);

                console.log('📊 API KDJ值:', { kValue, dValue, jValue, index });

                // 检查是否有有效数据
                const hasValidData = kValue !== '--' && dValue !== '--' && jValue !== '--';

                if (hasValidData) {
                    html += `
                        <div style="margin-bottom: 8px; padding: 6px; background: rgba(33, 150, 243, 0.1); border-radius: 4px;">
                            <div style="color: #2196f3; font-weight: bold; margin-bottom: 4px;">KDJ 🌐 (API数据)</div>
                            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 8px; font-size: 10px;">
                                <div>K: <span style="color: #ff9800;">${kValue}</span></div>
                                <div>D: <span style="color: #2196f3;">${dValue}</span></div>
                                <div>J: <span style="color: #4caf50;">${jValue}</span></div>
                            </div>
                            <div style="color: #4caf50; font-size: 8px; margin-top: 2px;">✅ 来自财经API的专业数据</div>
                        </div>
                    `;
                } else {
                    // 显示数据状态
                    const totalLength = kdjData.k ? kdjData.k.length : 0;
                    let statusMessage = '';

                    if (index >= totalLength) {
                        statusMessage = `索引超出范围 (${index}/${totalLength})`;
                    } else if (kdjData.k && kdjData.k[index] === null) {
                        statusMessage = '前期数据不足，KDJ计算中...';
                    } else {
                        statusMessage = '数据加载中...';
                    }

                    html += `
                        <div style="margin-bottom: 8px; padding: 6px; background: rgba(33, 150, 243, 0.1); border-radius: 4px;">
                            <div style="color: #2196f3; font-weight: bold; margin-bottom: 4px;">KDJ 🌐</div>
                            <div style="color: var(--text-muted); font-size: 9px;">${statusMessage}</div>
                            <div style="color: var(--text-muted); font-size: 8px;">位置: ${index + 1}/${totalLength}</div>
                        </div>
                    `;
                }
            } else {
                console.warn('⚠️ API未返回KDJ数据');
                html += `
                    <div style="margin-bottom: 8px; padding: 6px; background: rgba(33, 150, 243, 0.1); border-radius: 4px;">
                        <div style="color: #2196f3; font-weight: bold; margin-bottom: 4px;">KDJ</div>
                        <div style="color: var(--text-muted); font-size: 10px;">API数据加载中...</div>
                    </div>
                `;
            }
        }

        // RSI指标
        if (this.activeIndicators.includes('rsi') && indicators.rsi) {
            const rsiData = indicators.rsi;
            let rsiHtml = '';

            Object.entries(rsiData).forEach(([period, values]) => {
                const value = this.getIndicatorValueAtIndex(values, index);
                const color = value > 70 ? '#ff4757' : value < 30 ? '#2ed573' : '#ffc107';
                rsiHtml += `<div>RSI${period.replace('rsi', '')}: <span style="color: ${color};">${value}</span></div>`;
            });

            if (rsiHtml) {
                html += `
                    <div style="margin-bottom: 8px; padding: 6px; background: rgba(156, 39, 176, 0.1); border-radius: 4px;">
                        <div style="color: #9c27b0; font-weight: bold; margin-bottom: 4px;">RSI</div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; font-size: 10px;">
                            ${rsiHtml}
                        </div>
                    </div>
                `;
            }
        }

        content.innerHTML = html || '<div style="color: var(--text-muted);">未选择技术指标</div>';
    }

    // 更新均线数据显示
    updateMADataInPanel(index) {
        const content = document.getElementById('ma-data-content');
        if (!content || !this.lastIndicatorData || !this.lastIndicatorData.indicators || !this.lastIndicatorData.indicators.ma) {
            content.innerHTML = '<div style="color: var(--text-muted);">暂无均线数据</div>';
            return;
        }

        const maData = this.lastIndicatorData.indicators.ma;
        const colors = {
            5: '#ff9800',   // 橙色
            10: '#9c27b0',  // 紫色
            20: '#2196f3',  // 蓝色
            30: '#4caf50',  // 绿色
            60: '#f44336',  // 红色
            120: '#795548', // 棕色
            250: '#607d8b'  // 蓝灰色
        };

        let html = '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; font-size: 10px;">';

        this.activeMAs.forEach(period => {
            const maKey = `ma${period}`;
            if (maData[maKey]) {
                const value = this.getIndicatorValueAtIndex(maData[maKey], index);
                const color = colors[period] || '#ffffff';
                html += `<div>MA${period}: <span style="color: ${color}; font-weight: bold;">${value}</span></div>`;
            }
        });

        html += '</div>';

        content.innerHTML = this.activeMAs.length > 0 ? html : '<div style="color: var(--text-muted);">未选择均线</div>';
    }

    // 获取API指标值 - 专门处理API返回的数据
    getApiIndicatorValue(values, index) {
        console.log(`🌐 获取API指标值: 数组长度=${values?.length}, 索引=${index}`);

        if (!values || !Array.isArray(values)) {
            console.warn('⚠️ API指标数据不是有效数组:', values);
            return '--';
        }

        // 🔧 修复：确保索引在有效范围内
        if (index < 0) {
            console.warn(`⚠️ 索引为负数: ${index}, 使用0`);
            index = 0;
        }

        if (index >= values.length) {
            console.warn(`⚠️ 索引超出范围: ${index}, 数组长度: ${values.length}, 使用最后一个索引`);
            index = values.length - 1;
        }

        const value = values[index];
        console.log(`🌐 API指标值: 索引${index} = ${value}`);

        // API数据中null表示前期数据不足，这是正常的
        if (value === null || value === undefined) {
            console.log(`ℹ️ API指标值为null（前期数据不足）: 索引${index}`);
            return '--';
        }

        if (isNaN(value)) {
            console.warn(`⚠️ API指标值不是数字: 索引${index}, 值=${value}`);
            return '--';
        }

        // API返回的数据已经是计算好的，直接格式化显示
        const formattedValue = typeof value === 'number' ? value.toFixed(4) : value;
        console.log(`✅ API格式化指标值: ${formattedValue}`);
        return formattedValue;
    }

    // 获取指定索引位置的指标值 - 兼容旧版本（保留用于其他指标）
    getIndicatorValueAtIndex(values, index) {
        console.log(`🔍 获取指标值: 数组长度=${values?.length}, 索引=${index}`);

        if (!values || !Array.isArray(values)) {
            console.warn('⚠️ 指标数据不是有效数组:', values);
            return '--';
        }

        if (index < 0 || index >= values.length) {
            console.warn(`⚠️ 索引超出范围: ${index}, 数组长度: ${values.length}`);
            return '--';
        }

        const value = values[index];
        console.log(`📊 指标值: 索引${index} = ${value}`);

        // 正确处理null值
        if (value === null || value === undefined) {
            console.log(`ℹ️ 指标值为null（前期数据不足）: 索引${index}`);
            return '--';
        }

        if (isNaN(value)) {
            console.warn(`⚠️ 指标值不是数字: 索引${index}, 值=${value}`);
            return '--';
        }

        // 格式化数值
        const formattedValue = typeof value === 'number' ? value.toFixed(4) : value;
        console.log(`✅ 格式化指标值: ${formattedValue}`);
        return formattedValue;
    }

    // 隐藏数据面板
    hideDataPanel() {
        const panel = document.getElementById('crosshair-data-panel');
        if (panel) {
            panel.style.display = 'none';
        }
    }

    // 绑定鼠标交互事件
    bindMouseInteraction(canvas) {
        if (!canvas) return;

        // 移除旧的事件监听器，避免重复绑定
        if (canvas._mouseEventsbound) {
            return; // 已经绑定过了
        }

        console.log('🖱️ 绑定鼠标交互事件到Canvas:', canvas.id);

        // 鼠标移动事件
        const mouseMoveHandler = (e) => {
            this.handleMouseMove(e, canvas);
        };
        canvas.addEventListener('mousemove', mouseMoveHandler);

        // 鼠标离开事件
        const mouseLeaveHandler = () => {
            if (!this.crosshairLocked) {
                this.hideCrosshair();
            }
        };
        canvas.addEventListener('mouseleave', mouseLeaveHandler);

        // 鼠标点击事件 - 固定十字光标
        const mouseClickHandler = (e) => {
            this.handleMouseClick(e, canvas);
        };
        canvas.addEventListener('click', mouseClickHandler);

        // 标记已绑定，避免重复绑定
        canvas._mouseEventsbound = true;
        canvas._mouseHandlers = {
            mousemove: mouseMoveHandler,
            mouseleave: mouseLeaveHandler,
            click: mouseClickHandler
        };

        console.log('✅ 鼠标交互事件绑定完成');
    }

    // 优化的鼠标移动处理 - 提升响应速度，支持锁定模式
    handleMouseMove(e, canvas) {
        console.log('🖱️ 鼠标移动事件触发');

        if (!this.lastKlineData || !this.lastKlineData.klines) {
            console.warn('⚠️ 没有K线数据，忽略鼠标移动');
            return;
        }

        // 如果十字光标被锁定，忽略鼠标移动
        if (this.crosshairLocked) {
            console.log('🔒 十字光标已锁定，忽略鼠标移动');
            return;
        }

        // 使用高性能的坐标计算，避免重复计算
        const rect = this.canvasRect || canvas.getBoundingClientRect();
        this.canvasRect = rect; // 缓存rect以提升性能

        const mouseX = e.clientX - rect.left;
        const mouseY = e.clientY - rect.top;

        // 预先计算的数据，避免重复处理
        const klines = this.cachedKlines || this.getStandardKlineData(this.lastKlineData.klines);
        this.cachedKlines = klines;

        const index = this.getKlineIndexFromMouseX(mouseX, canvas, klines);
        console.log(`🎯 计算得到K线索引: ${index}, 总数: ${klines.length}`);

        // 只有索引变化时才更新，避免不必要的重绘
        if (index >= 0 && index < klines.length) {
            if (index !== this.currentKlineIndex) {
                console.log(`🎯 索引变化: ${this.currentKlineIndex} -> ${index}`);
                this.currentKlineIndex = index;
                this.crosshairActive = true;

                // 确保数据面板存在
                const panel = document.getElementById('crosshair-data-panel');
                if (!panel) {
                    console.warn('⚠️ 数据面板不存在，重新创建');
                    this.createDataDisplayPanel();
                }

                // 使用requestAnimationFrame确保流畅的动画
                if (!this.crosshairUpdatePending) {
                    this.crosshairUpdatePending = true;
                    requestAnimationFrame(() => {
                        this.updateCrosshairDisplay(klines, mouseX, mouseY);
                        this.crosshairUpdatePending = false;
                    });
                }
            }
        } else {
            console.warn(`⚠️ 无效的K线索引: ${index}`);
        }
    }

    // 处理鼠标点击 - 固定十字光标位置
    handleMouseClick(e, canvas) {
        if (!this.lastKlineData || !this.lastKlineData.klines) return;

        const rect = canvas.getBoundingClientRect();
        const mouseX = e.clientX - rect.left;

        const klines = this.getStandardKlineData(this.lastKlineData.klines);
        const index = this.getKlineIndexFromMouseX(mouseX, canvas, klines);

        if (index >= 0 && index < klines.length) {
            this.currentKlineIndex = index;
            this.crosshairActive = true;
            this.updateCrosshairDisplay(klines);
            console.log(`🎯 固定十字光标到索引: ${index}`);
        }
    }

    // 根据鼠标X坐标获取K线索引
    getKlineIndexFromMouseX(mouseX, canvas, klines) {
        const padding = this.getStandardPadding();
        const chartWidth = canvas.width - padding.left - padding.right;

        // 计算相对位置
        const relativeX = mouseX - padding.left;

        if (relativeX < 0 || relativeX > chartWidth) {
            return -1;
        }

        // 根据x坐标计算索引
        const index = Math.round((relativeX / chartWidth) * (klines.length - 1));
        return Math.max(0, Math.min(klines.length - 1, index));
    }

    // 重绘图表并添加十字光标
    redrawChartsWithCrosshair(klines, index) {
        if (!this.lastKlineData || index < 0 || index >= klines.length) return;

        // 直接绘制十字光标，不重新渲染整个图表
        // 这样可以避免闪烁问题
        this.drawCrosshair(klines, index);
    }

    // 绘制十字光标
    drawCrosshair(klines, index) {
        console.log('🎯 开始绘制十字光标，索引:', index, '数据长度:', klines?.length);

        // 查找主图Canvas元素（支持多种ID）
        const canvas = document.getElementById('main-kline-chart') ||
                      document.getElementById('kline-chart') ||
                      document.getElementById('main-chart') ||
                      document.getElementById('fallback-chart');

        console.log('🎯 找到的Canvas元素:', canvas?.id || '未找到');

        if (!canvas || !canvas.getContext) {
            console.log('⚠️ 未找到有效的主图Canvas元素，跳过十字光标绘制');
            return;
        }

        // 创建或获取十字光标叠加Canvas
        const crosshairCanvas = this.getCrosshairCanvas(canvas);
        if (!crosshairCanvas) {
            console.log('⚠️ 无法创建十字光标叠加Canvas');
            return;
        }

        console.log('🎯 十字光标Canvas尺寸:', crosshairCanvas.width, 'x', crosshairCanvas.height);

        // 清除之前的十字光标
        const ctx = crosshairCanvas.getContext('2d');
        ctx.clearRect(0, 0, crosshairCanvas.width, crosshairCanvas.height);

        const padding = this.getStandardPadding();
        const chartWidth = crosshairCanvas.width - padding.left - padding.right;
        const chartHeight = crosshairCanvas.height - padding.top - padding.bottom;

        // 计算十字光标位置
        const x = this.calculateXPosition(index, klines.length, padding, chartWidth);
        const currentKline = klines[index];

        if (!currentKline) return;

        // 计算价格对应的y坐标
        const priceRange = this.calculatePriceRange(klines);
        if (!priceRange || priceRange.max === priceRange.min) {
            console.log('⚠️ 价格范围计算异常，跳过十字光标绘制');
            return;
        }

        const priceY = padding.top + chartHeight - ((currentKline.close - priceRange.min) / (priceRange.max - priceRange.min)) * chartHeight;

        console.log('🎯 十字光标坐标:', { x, priceY, price: currentKline.close, priceRange });

        // 绘制十字光标线条
        ctx.save();
        ctx.strokeStyle = '#ffc107';
        ctx.lineWidth = 2; // 增加线宽使其更明显
        ctx.setLineDash([5, 5]);

        // 垂直线
        ctx.beginPath();
        ctx.moveTo(x, padding.top);
        ctx.lineTo(x, crosshairCanvas.height - padding.bottom);
        ctx.stroke();

        // 水平线
        ctx.beginPath();
        ctx.moveTo(padding.left, priceY);
        ctx.lineTo(crosshairCanvas.width - padding.right, priceY);
        ctx.stroke();

        ctx.restore();

        // 绘制价格标签
        this.drawPriceLabel(ctx, priceY, currentKline.close, crosshairCanvas.width - padding.right);

        // 绘制时间标签
        this.drawTimeLabel(ctx, x, currentKline.date, crosshairCanvas.height - padding.bottom);

        // 绘制子图十字光标
        this.drawSubChartsCrosshair(x, index);

        console.log('✅ 十字光标绘制完成');
    }

    // 优化的十字光标绘制方法 - 高性能版本
    drawOptimizedCrosshair(klines, index, mouseX = null, mouseY = null) {
        // 查找主图Canvas元素
        const canvas = document.getElementById('main-kline-chart') ||
                      document.getElementById('kline-chart') ||
                      document.getElementById('main-chart') ||
                      document.getElementById('fallback-chart');

        if (!canvas || !canvas.getContext) {
            return;
        }

        // 获取或创建十字光标叠加Canvas
        const crosshairCanvas = this.getCrosshairCanvas(canvas);
        if (!crosshairCanvas) return;

        // 清除之前的十字光标（高性能清除）
        const ctx = crosshairCanvas.getContext('2d');
        ctx.clearRect(0, 0, crosshairCanvas.width, crosshairCanvas.height);

        const padding = this.getStandardPadding();
        const chartWidth = crosshairCanvas.width - padding.left - padding.right;
        const chartHeight = crosshairCanvas.height - padding.top - padding.bottom;

        // 计算十字光标位置（优化版本）
        const x = this.calculateXPosition(index, klines.length, padding, chartWidth);
        const currentKline = klines[index];

        if (!currentKline) return;

        // 使用缓存的价格范围，避免重复计算
        if (!this.cachedPriceRange || this.cachedPriceRange.klineCount !== klines.length) {
            this.cachedPriceRange = this.calculatePriceRange(klines);
            this.cachedPriceRange.klineCount = klines.length;
        }

        const priceRange = this.cachedPriceRange;
        const priceY = padding.top + chartHeight - ((currentKline.close - priceRange.min) / (priceRange.max - priceRange.min)) * chartHeight;

        // 专业级十字光标绘制 - 增强视觉效果
        this.drawProfessionalCrosshair(ctx, x, priceY, padding, crosshairCanvas.width, crosshairCanvas.height);

        // 绘制标签（优化版本）
        this.drawOptimizedLabels(ctx, x, priceY, currentKline, crosshairCanvas.width - padding.right, crosshairCanvas.height - padding.bottom);

        // 绘制子图十字光标（异步处理，避免阻塞主图）
        setTimeout(() => {
            this.drawSubChartsCrosshair(x, index);
        }, 0);
    }

    // 优化的标签绘制
    drawOptimizedLabels(ctx, x, priceY, kline, rightEdge, bottomEdge) {
        ctx.save();

        // 价格标签
        const priceText = kline.close.toFixed(2);
        ctx.font = '12px Arial';
        const priceTextWidth = ctx.measureText(priceText).width;
        const priceLabelWidth = priceTextWidth + 12;
        const labelHeight = 20;

        // 价格标签背景
        ctx.fillStyle = '#ffc107';
        ctx.fillRect(rightEdge - priceLabelWidth, priceY - labelHeight/2, priceLabelWidth, labelHeight);

        // 价格标签文字
        ctx.fillStyle = '#000000';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(priceText, rightEdge - priceLabelWidth/2, priceY);

        // 时间标签
        const date = kline.date.split(' ')[0] || kline.date;
        const timeText = date.replace(/-/g, '/');
        ctx.font = '11px Arial';
        const timeTextWidth = ctx.measureText(timeText).width;
        const timeLabelWidth = timeTextWidth + 8;
        const timeLabelHeight = 18;

        // 时间标签背景
        ctx.fillStyle = '#ffc107';
        ctx.fillRect(x - timeLabelWidth/2, bottomEdge, timeLabelWidth, timeLabelHeight);

        // 时间标签文字
        ctx.fillStyle = '#000000';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'top';
        ctx.fillText(timeText, x, bottomEdge + 2);

        ctx.restore();
    }

    // 专业级十字光标绘制 - 增强视觉效果
    drawProfessionalCrosshair(ctx, x, y, padding, canvasWidth, canvasHeight) {
        ctx.save();

        // 主十字线 - 亮黄色，较粗
        ctx.strokeStyle = '#ffc107';
        ctx.lineWidth = 2;
        ctx.setLineDash([8, 4]);
        ctx.globalAlpha = 0.9;

        // 垂直线
        ctx.beginPath();
        ctx.moveTo(x, padding.top);
        ctx.lineTo(x, canvasHeight - padding.bottom);
        ctx.stroke();

        // 水平线
        ctx.beginPath();
        ctx.moveTo(padding.left, y);
        ctx.lineTo(canvasWidth - padding.right, y);
        ctx.stroke();

        // 辅助十字线 - 半透明，细线
        ctx.strokeStyle = '#ffc107';
        ctx.lineWidth = 1;
        ctx.setLineDash([3, 3]);
        ctx.globalAlpha = 0.4;

        // 绘制辅助网格线（可选）
        if (this.showCrosshairGrid) {
            // 垂直辅助线
            for (let i = 1; i <= 4; i++) {
                const auxX = padding.left + (canvasWidth - padding.left - padding.right) / 4 * i;
                if (Math.abs(auxX - x) > 20) { // 避免与主线重叠
                    ctx.beginPath();
                    ctx.moveTo(auxX, padding.top);
                    ctx.lineTo(auxX, canvasHeight - padding.bottom);
                    ctx.stroke();
                }
            }

            // 水平辅助线
            for (let i = 1; i <= 4; i++) {
                const auxY = padding.top + (canvasHeight - padding.top - padding.bottom) / 4 * i;
                if (Math.abs(auxY - y) > 20) { // 避免与主线重叠
                    ctx.beginPath();
                    ctx.moveTo(padding.left, auxY);
                    ctx.lineTo(canvasWidth - padding.right, auxY);
                    ctx.stroke();
                }
            }
        }

        // 十字光标中心点
        ctx.globalAlpha = 1.0;
        ctx.fillStyle = '#ffc107';
        ctx.beginPath();
        ctx.arc(x, y, 3, 0, 2 * Math.PI);
        ctx.fill();

        // 中心点外圈
        ctx.strokeStyle = '#000000';
        ctx.lineWidth = 1;
        ctx.setLineDash([]);
        ctx.beginPath();
        ctx.arc(x, y, 3, 0, 2 * Math.PI);
        ctx.stroke();

        ctx.restore();
    }

    // 清理性能优化缓存
    clearPerformanceCache() {
        this.canvasRect = null;
        this.cachedKlines = null;
        this.cachedPriceRange = null;
        this.crosshairUpdatePending = false;
        this.dataPanelUpdatePending = false;
        console.log('🧹 清理交互性能缓存');
    }

    // 重置交互状态
    resetInteractionState() {
        this.crosshairActive = false;
        this.currentKlineIndex = -1;
        this.clearPerformanceCache();
        this.hideCrosshair();
        console.log('🔄 重置交互状态');
    }

    // 绘制价格标签
    drawPriceLabel(ctx, y, price, x) {
        ctx.save();

        // 标签背景
        const text = price.toFixed(2);
        ctx.font = '12px Arial';
        const textWidth = ctx.measureText(text).width;
        const labelWidth = textWidth + 12;
        const labelHeight = 20;

        ctx.fillStyle = '#ffc107';
        ctx.fillRect(x - labelWidth, y - labelHeight/2, labelWidth, labelHeight);

        // 标签文字
        ctx.fillStyle = '#000000';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(text, x - labelWidth/2, y);

        ctx.restore();
    }

    // 绘制时间标签
    drawTimeLabel(ctx, x, dateTime, y) {
        ctx.save();

        // 提取日期部分
        const date = dateTime.split(' ')[0] || dateTime;
        const text = date.replace(/-/g, '/');

        ctx.font = '11px Arial';
        const textWidth = ctx.measureText(text).width;
        const labelWidth = textWidth + 8;
        const labelHeight = 18;

        // 标签背景
        ctx.fillStyle = '#ffc107';
        ctx.fillRect(x - labelWidth/2, y, labelWidth, labelHeight);

        // 标签文字
        ctx.fillStyle = '#000000';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'top';
        ctx.fillText(text, x, y + 2);

        ctx.restore();
    }

    // 优化的子图十字光标联动系统
    drawSubChartsCrosshair(x, index) {
        console.log(`🎯 绘制子图十字光标联动，X=${x}, 索引=${index}`);

        // 使用叠加Canvas方式绘制子图十字光标，避免直接在子图Canvas上绘制
        this.drawVolumeSubChartCrosshairOverlay(x, index);

        // 绘制技术指标子图十字光标叠加层，并显示对应数值
        this.activeIndicators.forEach(indicator => {
            if (indicator !== 'volume') {
                this.drawIndicatorSubChartCrosshairOverlay(x, index, indicator);
            }
        });
    }

    // 绘制成交量子图十字光标叠加层
    drawVolumeSubChartCrosshairOverlay(x, index) {
        const canvas = document.getElementById('volume-canvas');
        if (!canvas) return;

        // 获取或创建叠加Canvas
        const overlayCanvas = this.getSubChartCrosshairCanvas(canvas, 'volume');
        if (!overlayCanvas) return;

        const ctx = overlayCanvas.getContext('2d');
        ctx.clearRect(0, 0, overlayCanvas.width, overlayCanvas.height);

        // 绘制专业级垂直十字线
        this.drawSubChartCrosshairLine(ctx, x, overlayCanvas.height);

        // 绘制成交量数值标签
        if (this.lastKlineData && this.lastKlineData.klines) {
            const klines = this.getStandardKlineData(this.lastKlineData.klines);
            const kline = klines[index];
            if (kline && kline.volume) {
                this.drawVolumeValueLabel(ctx, x, kline.volume, overlayCanvas.height);
            }
        }

        console.log(`✅ 成交量子图十字光标叠加层绘制完成`);
    }

    // 绘制技术指标子图十字光标叠加层
    drawIndicatorSubChartCrosshairOverlay(x, index, indicator) {
        const canvas = document.getElementById(`${indicator}-canvas`);
        if (!canvas) return;

        // 获取或创建叠加Canvas
        const overlayCanvas = this.getSubChartCrosshairCanvas(canvas, indicator);
        if (!overlayCanvas) return;

        const ctx = overlayCanvas.getContext('2d');
        ctx.clearRect(0, 0, overlayCanvas.width, overlayCanvas.height);

        // 绘制专业级垂直十字线
        this.drawSubChartCrosshairLine(ctx, x, overlayCanvas.height);

        // 绘制技术指标数值标签
        this.drawIndicatorValueLabels(ctx, x, index, indicator, overlayCanvas.height);

        console.log(`✅ ${indicator}子图十字光标叠加层绘制完成`);
    }

    // 绘制成交量数值标签
    drawVolumeValueLabel(ctx, x, volume, canvasHeight) {
        const formattedVolume = this.formatVolume(volume);

        ctx.save();
        ctx.font = '11px Arial';
        ctx.textAlign = 'center';

        // 计算标签位置
        const labelY = 20;
        const textWidth = ctx.measureText(formattedVolume).width;
        const labelWidth = textWidth + 12;
        const labelHeight = 18;

        // 确保标签不超出画布边界
        const labelX = Math.max(labelWidth/2, Math.min(ctx.canvas.width - labelWidth/2, x));

        // 绘制标签背景
        ctx.fillStyle = 'rgba(255, 193, 7, 0.9)';
        ctx.fillRect(labelX - labelWidth/2, labelY - labelHeight/2, labelWidth, labelHeight);

        // 绘制标签边框
        ctx.strokeStyle = '#ffc107';
        ctx.lineWidth = 1;
        ctx.strokeRect(labelX - labelWidth/2, labelY - labelHeight/2, labelWidth, labelHeight);

        // 绘制标签文字
        ctx.fillStyle = '#000000';
        ctx.textBaseline = 'middle';
        ctx.fillText(formattedVolume, labelX, labelY);

        ctx.restore();
    }

    // 绘制技术指标数值标签
    drawIndicatorValueLabels(ctx, x, index, indicator, canvasHeight) {
        if (!this.lastIndicatorData || !this.lastIndicatorData.indicators) return;

        const indicators = this.lastIndicatorData.indicators;
        let labels = [];

        // 根据指标类型获取数值
        switch (indicator) {
            case 'macd':
                if (indicators.macd) {
                    const macdData = indicators.macd;
                    // 使用API数据获取函数
                    const macdValue = this.getApiIndicatorValue(macdData.macd, index);
                    const difValue = this.getApiIndicatorValue(macdData.dif, index);
                    const deaValue = this.getApiIndicatorValue(macdData.dea, index);

                    labels = [
                        { text: `DIF: ${difValue}`, color: '#ffc107' },
                        { text: `DEA: ${deaValue}`, color: '#2196f3' },
                        { text: `MACD: ${macdValue}`, color: macdValue !== '--' && parseFloat(macdValue) >= 0 ? '#ff4757' : '#2ed573' }
                    ];
                }
                break;

            case 'kdj':
                if (indicators.kdj) {
                    const kdjData = indicators.kdj;
                    // 使用API数据获取函数
                    const kValue = this.getApiIndicatorValue(kdjData.k, index);
                    const dValue = this.getApiIndicatorValue(kdjData.d, index);
                    const jValue = this.getApiIndicatorValue(kdjData.j, index);

                    labels = [
                        { text: `K: ${kValue}`, color: '#ff9800' },
                        { text: `D: ${dValue}`, color: '#2196f3' },
                        { text: `J: ${jValue}`, color: '#4caf50' }
                    ];
                }
                break;

            case 'rsi':
                if (indicators.rsi) {
                    const rsiData = indicators.rsi;
                    Object.entries(rsiData).forEach(([period, values]) => {
                        const value = this.getIndicatorValueAtIndex(values, index);
                        const color = value > 70 ? '#ff4757' : value < 30 ? '#2ed573' : '#ffc107';
                        labels.push({ text: `RSI${period.replace('rsi', '')}: ${value}`, color });
                    });
                }
                break;
        }

        // 绘制标签
        if (labels.length > 0) {
            this.drawMultipleValueLabels(ctx, x, labels, canvasHeight);
        }
    }

    // 绘制多个数值标签
    drawMultipleValueLabels(ctx, x, labels, canvasHeight) {
        ctx.save();
        ctx.font = '10px Arial';
        ctx.textAlign = 'left';

        const labelHeight = 16;
        const labelPadding = 4;
        const startY = 10;

        // 计算总宽度
        let maxWidth = 0;
        labels.forEach(label => {
            const width = ctx.measureText(label.text).width + 8;
            maxWidth = Math.max(maxWidth, width);
        });

        // 确保标签不超出画布边界
        const labelX = Math.max(5, Math.min(ctx.canvas.width - maxWidth - 5, x + 10));

        // 绘制背景
        const totalHeight = labels.length * labelHeight + labelPadding * 2;
        ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
        ctx.fillRect(labelX - labelPadding, startY - labelPadding, maxWidth + labelPadding * 2, totalHeight);

        // 绘制边框
        ctx.strokeStyle = '#ffc107';
        ctx.lineWidth = 1;
        ctx.strokeRect(labelX - labelPadding, startY - labelPadding, maxWidth + labelPadding * 2, totalHeight);

        // 绘制每个标签
        labels.forEach((label, index) => {
            const y = startY + index * labelHeight + labelHeight / 2;

            ctx.fillStyle = label.color;
            ctx.textBaseline = 'middle';
            ctx.fillText(label.text, labelX, y);
        });

        ctx.restore();
    }

    // 清除所有子图十字光标叠加层
    clearAllSubChartsCrosshair() {
        // 清除成交量子图十字光标
        const volumeOverlay = document.getElementById('crosshair-overlay-volume');
        if (volumeOverlay && volumeOverlay.getContext) {
            const ctx = volumeOverlay.getContext('2d');
            ctx.clearRect(0, 0, volumeOverlay.width, volumeOverlay.height);
        }

        // 清除技术指标子图十字光标
        this.activeIndicators.forEach(indicator => {
            if (indicator !== 'volume') {
                const overlay = document.getElementById(`crosshair-overlay-${indicator}`);
                if (overlay && overlay.getContext) {
                    const ctx = overlay.getContext('2d');
                    ctx.clearRect(0, 0, overlay.width, overlay.height);
                }
            }
        });

        console.log('🧹 清除所有子图十字光标叠加层');
    }

    // 绘制子图专业级十字光标线
    drawSubChartCrosshairLine(ctx, x, canvasHeight) {
        ctx.save();

        // 主十字线 - 与主图保持一致的样式
        ctx.strokeStyle = '#ffc107';
        ctx.lineWidth = 2;
        ctx.setLineDash([8, 4]);
        ctx.globalAlpha = 0.9;

        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, canvasHeight);
        ctx.stroke();

        // 辅助线 - 更细的虚线
        ctx.strokeStyle = '#ffc107';
        ctx.lineWidth = 1;
        ctx.setLineDash([3, 3]);
        ctx.globalAlpha = 0.5;

        // 绘制辅助参考线（可选）
        if (this.showCrosshairGrid) {
            const quarterX1 = x - 50;
            const quarterX2 = x + 50;

            if (quarterX1 > 0) {
                ctx.beginPath();
                ctx.moveTo(quarterX1, 0);
                ctx.lineTo(quarterX1, canvasHeight);
                ctx.stroke();
            }

            if (quarterX2 < ctx.canvas.width) {
                ctx.beginPath();
                ctx.moveTo(quarterX2, 0);
                ctx.lineTo(quarterX2, canvasHeight);
                ctx.stroke();
            }
        }

        ctx.restore();
    }

    // 绘制成交量子图十字光标
    drawVolumeSubChartCrosshair(x, index) {
        // 查找成交量子图Canvas元素（支持多种ID）
        const canvas = document.getElementById('volume-canvas') ||
                      document.getElementById('volumeChart') ||
                      document.querySelector('[id*="volume"]');

        if (!canvas || !canvas.getContext) {
            console.log('⚠️ 未找到有效的成交量子图Canvas元素，跳过十字光标绘制');
            return;
        }

        const ctx = canvas.getContext('2d');

        ctx.save();
        ctx.strokeStyle = '#ffc107';
        ctx.lineWidth = 1;
        ctx.setLineDash([3, 3]);

        // 垂直线
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, canvas.height);
        ctx.stroke();

        ctx.restore();
    }

    // 绘制技术指标子图十字光标
    drawIndicatorSubChartCrosshair(x, index, indicator) {
        // 查找技术指标子图Canvas元素（支持多种ID格式）
        const canvas = document.getElementById(`${indicator}-canvas`) ||
                      document.getElementById(`${indicator}Chart`) ||
                      document.querySelector(`[id*="${indicator}"]`);

        if (!canvas || !canvas.getContext) {
            console.log(`⚠️ 未找到有效的${indicator}子图Canvas元素，跳过十字光标绘制`);
            return;
        }

        const ctx = canvas.getContext('2d');

        ctx.save();
        ctx.strokeStyle = '#ffc107';
        ctx.lineWidth = 1;
        ctx.setLineDash([3, 3]);

        // 垂直线
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, canvas.height);
        ctx.stroke();

        ctx.restore();
    }

    // 格式化成交量显示
    formatVolume(volume) {
        if (!volume) return '--';

        const num = parseFloat(volume.toString().replace(/[^\d.]/g, ''));
        if (isNaN(num)) return '--';

        if (num >= 100000000) {
            return (num / 100000000).toFixed(2) + '亿';
        } else if (num >= 10000) {
            return (num / 10000).toFixed(2) + '万';
        } else {
            return num.toFixed(0);
        }
    }

    // 格式化成交额显示
    formatTurnover(turnover) {
        if (!turnover) return '--';

        const num = parseFloat(turnover.toString().replace(/[^\d.]/g, ''));
        if (isNaN(num)) return '--';

        if (num >= 100000000) {
            return (num / 100000000).toFixed(2) + '亿';
        } else if (num >= 10000) {
            return (num / 10000).toFixed(2) + '万';
        } else {
            return num.toFixed(0);
        }
    }

    // 统一的数据长度配置 - 确保所有图表使用相同的数据范围
    getStandardDataLength() {
        return 30; // 统一显示最近30个数据点
    }

    // 统一的数据切片方法 - 确保主图和子图使用完全相同的数据
    getStandardKlineData(klines) {
        const standardLength = this.getStandardDataLength();
        const result = klines.slice(-standardLength);
        return result;
    }

    // 简单的对齐检查
    checkAlignment() {
        setTimeout(() => {
            console.log('📊 对齐检查: 红色=主图, 绿色=成交量, 蓝色=技术指标');
        }, 500);
    }

    // 统一的padding配置 - 确保所有图表使用相同的边距
    getStandardPadding() {
        return { left: 60, right: 20, top: 20, bottom: 40 };
    }

    // 专业级统一坐标计算系统 - 确保主图和子图完美像素级对齐
    calculateXPosition(index, klineCount, padding, chartWidth) {
        // 操作员专业需求：主图K线和技术指标每天的点位必须完全对应
        // 使用高精度坐标计算，确保时间轴完美对齐

        if (klineCount <= 1) {
            return Math.round(padding.left);
        }

        // 专业级像素对齐算法
        // 1. 计算理论位置（高精度）
        const theoreticalX = padding.left + (chartWidth / (klineCount - 1)) * index;

        // 2. 应用专业级像素对齐策略
        // 对于操作员界面，确保所有元素都在整像素边界上，避免模糊
        const alignedX = Math.round(theoreticalX);

        // 3. 验证对齐精度（调试模式）
        if (Math.abs(theoreticalX - alignedX) > 0.5) {
            console.warn(`⚠️ 坐标对齐偏差较大: 理论${theoreticalX.toFixed(3)} -> 对齐${alignedX}`);
        }

        return alignedX;
    }

    // 专业级对齐验证系统 - 绘制可视化对齐参考线
    drawAlignmentReference(ctx, x, index, chartType, color) {
        ctx.save();

        // 设置参考线样式
        ctx.strokeStyle = color;
        ctx.lineWidth = 2;
        ctx.setLineDash([8, 4]); // 更清晰的虚线模式
        ctx.globalAlpha = 0.8;   // 适当透明度，不影响数据观察

        // 绘制垂直参考线
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, ctx.canvas.height);
        ctx.stroke();

        // 添加位置标识
        ctx.fillStyle = color;
        ctx.font = 'bold 10px Arial';
        ctx.textAlign = 'center';
        ctx.globalAlpha = 1.0;

        const label = index === 0 ? '起始' : '结束';
        const labelY = 15;

        // 绘制标签背景
        const labelWidth = ctx.measureText(label).width + 8;
        ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        ctx.fillRect(x - labelWidth/2, labelY - 12, labelWidth, 16);

        // 绘制标签文字
        ctx.fillStyle = color;
        ctx.fillText(label, x, labelY);

        // 在控制台输出精确坐标信息（供操作员验证）
        console.log(`🎯 ${chartType} 对齐参考线 [${label}]: X=${x.toFixed(3)}px, 索引=${index}`);

        ctx.restore();
    }

    // 专业级对齐验证报告 - 生成详细的对齐精度报告
    generateAlignmentReport(mainCanvas, subCanvases) {
        console.log('📊 ===== 专业级对齐验证报告 =====');

        if (!mainCanvas) {
            console.warn('⚠️ 主图Canvas未找到，无法生成对齐报告');
            return;
        }

        console.log(`📏 主图Canvas尺寸: ${mainCanvas.width}x${mainCanvas.height}`);

        const padding = this.getStandardPadding();
        const chartWidth = mainCanvas.width - padding.left - padding.right;

        console.log(`📐 图表绘制区域: 宽度=${chartWidth}px, 左边距=${padding.left}px, 右边距=${padding.right}px`);

        // 验证子图对齐情况
        if (subCanvases && subCanvases.length > 0) {
            console.log(`📊 子图数量: ${subCanvases.length}`);

            subCanvases.forEach((subCanvas, index) => {
                if (subCanvas) {
                    const widthMatch = subCanvas.width === mainCanvas.width;
                    const paddingMatch = true; // 所有图表使用相同的padding

                    console.log(`📈 子图${index + 1}: 宽度${subCanvas.width}px ${widthMatch ? '✅' : '❌'} 对齐${paddingMatch ? '✅' : '❌'}`);

                    if (!widthMatch) {
                        console.error(`❌ 子图${index + 1}宽度不匹配! 主图:${mainCanvas.width}px vs 子图:${subCanvas.width}px`);
                    }
                }
            });
        }

        console.log('📊 ===== 对齐验证报告结束 =====');
    }

    // 获取所有子图Canvas元素
    getSubCanvases() {
        const subCanvases = [];
        const indicators = ['volume', 'macd', 'kdj', 'rsi'];

        indicators.forEach(indicator => {
            const canvas = document.getElementById(`${indicator}-canvas`);
            if (canvas) {
                subCanvases.push(canvas);
            }
        });

        return subCanvases;
    }

    // 计算价格范围
    calculatePriceRange(klines) {
        if (!klines || klines.length === 0) {
            return { min: 0, max: 100 };
        }

        const prices = klines.map(k => [k.high, k.low, k.open, k.close]).flat();
        const maxPrice = Math.max(...prices);
        const minPrice = Math.min(...prices);
        const priceBuffer = (maxPrice - minPrice) * 0.1;

        return {
            min: minPrice - priceBuffer,
            max: maxPrice + priceBuffer
        };
    }

    // 创建或获取十字光标叠加Canvas
    getCrosshairCanvas(baseCanvas) {
        const crosshairId = 'crosshair-overlay-canvas';
        let crosshairCanvas = document.getElementById(crosshairId);

        if (!crosshairCanvas) {
            // 创建新的叠加Canvas
            crosshairCanvas = document.createElement('canvas');
            crosshairCanvas.id = crosshairId;
            crosshairCanvas.width = baseCanvas.width;
            crosshairCanvas.height = baseCanvas.height;

            // 设置样式使其叠加在原Canvas上
            crosshairCanvas.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                pointer-events: none;
                z-index: 10;
            `;

            // 确保父容器有相对定位
            const parent = baseCanvas.parentNode;
            if (parent) {
                if (getComputedStyle(parent).position === 'static') {
                    parent.style.position = 'relative';
                }
                parent.appendChild(crosshairCanvas);
            }

            console.log('✅ 创建十字光标叠加Canvas');
        } else {
            // 更新尺寸以匹配基础Canvas
            if (crosshairCanvas.width !== baseCanvas.width || crosshairCanvas.height !== baseCanvas.height) {
                crosshairCanvas.width = baseCanvas.width;
                crosshairCanvas.height = baseCanvas.height;
            }
        }

        return crosshairCanvas;
    }

    // 为子图创建叠加Canvas
    getSubChartCrosshairCanvas(baseCanvas, chartType) {
        const crosshairId = `crosshair-overlay-${chartType}`;
        let crosshairCanvas = document.getElementById(crosshairId);

        if (!crosshairCanvas) {
            // 创建新的叠加Canvas
            crosshairCanvas = document.createElement('canvas');
            crosshairCanvas.id = crosshairId;
            crosshairCanvas.width = baseCanvas.width;
            crosshairCanvas.height = baseCanvas.height;

            // 设置样式使其叠加在原Canvas上
            crosshairCanvas.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                pointer-events: none;
                z-index: 10;
            `;

            // 确保父容器有相对定位
            const parent = baseCanvas.parentNode;
            if (parent) {
                if (getComputedStyle(parent).position === 'static') {
                    parent.style.position = 'relative';
                }
                parent.appendChild(crosshairCanvas);
            }

            console.log(`✅ 创建${chartType}子图十字光标叠加Canvas`);
        } else {
            // 更新尺寸以匹配基础Canvas
            if (crosshairCanvas.width !== baseCanvas.width || crosshairCanvas.height !== baseCanvas.height) {
                crosshairCanvas.width = baseCanvas.width;
                crosshairCanvas.height = baseCanvas.height;
            }
        }

        return crosshairCanvas;
    }

    // 绘制成交量子图十字光标叠加层
    drawVolumeSubChartCrosshairOverlay(x, index) {
        const canvas = document.getElementById('volume-canvas') ||
                      document.getElementById('volumeChart') ||
                      document.querySelector('[id*="volume"]');

        if (!canvas || !canvas.getContext) {
            return;
        }

        const crosshairCanvas = this.getSubChartCrosshairCanvas(canvas, 'volume');
        if (!crosshairCanvas) return;

        const ctx = crosshairCanvas.getContext('2d');

        // 清除之前的十字光标
        ctx.clearRect(0, 0, crosshairCanvas.width, crosshairCanvas.height);

        // 绘制垂直线
        ctx.save();
        ctx.strokeStyle = '#ffc107';
        ctx.lineWidth = 1;
        ctx.setLineDash([3, 3]);

        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, crosshairCanvas.height);
        ctx.stroke();

        ctx.restore();
    }

    // 绘制技术指标子图十字光标叠加层
    drawIndicatorSubChartCrosshairOverlay(x, index, indicator) {
        const canvas = document.getElementById(`${indicator}-canvas`) ||
                      document.getElementById(`${indicator}Chart`) ||
                      document.querySelector(`[id*="${indicator}"]`);

        if (!canvas || !canvas.getContext) {
            return;
        }

        const crosshairCanvas = this.getSubChartCrosshairCanvas(canvas, indicator);
        if (!crosshairCanvas) return;

        const ctx = crosshairCanvas.getContext('2d');

        // 清除之前的十字光标
        ctx.clearRect(0, 0, crosshairCanvas.width, crosshairCanvas.height);

        // 绘制垂直线
        ctx.save();
        ctx.strokeStyle = '#ffc107';
        ctx.lineWidth = 1;
        ctx.setLineDash([3, 3]);

        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, crosshairCanvas.height);
        ctx.stroke();

        ctx.restore();
    }

    // 清除所有子图十字光标
    clearAllSubChartsCrosshair() {
        // 清除成交量子图十字光标叠加层
        const volumeCrosshairCanvas = document.getElementById('crosshair-overlay-volume');
        if (volumeCrosshairCanvas && volumeCrosshairCanvas.getContext) {
            const ctx = volumeCrosshairCanvas.getContext('2d');
            ctx.clearRect(0, 0, volumeCrosshairCanvas.width, volumeCrosshairCanvas.height);
        }

        // 清除技术指标子图十字光标叠加层
        this.activeIndicators.forEach(indicator => {
            if (indicator !== 'volume') {
                const indicatorCrosshairCanvas = document.getElementById(`crosshair-overlay-${indicator}`);
                if (indicatorCrosshairCanvas && indicatorCrosshairCanvas.getContext) {
                    const ctx = indicatorCrosshairCanvas.getContext('2d');
                    ctx.clearRect(0, 0, indicatorCrosshairCanvas.width, indicatorCrosshairCanvas.height);
                }
            }
        });

        console.log('🎯 清除所有子图十字光标叠加层');
    }

    // 为主图创建叠加Canvas
    getMainCrosshairCanvas(baseCanvas) {
        const crosshairId = 'crosshair-overlay-main';
        let crosshairCanvas = document.getElementById(crosshairId);

        if (!crosshairCanvas) {
            // 创建新的叠加Canvas
            crosshairCanvas = document.createElement('canvas');
            crosshairCanvas.id = crosshairId;
            crosshairCanvas.width = baseCanvas.width;
            crosshairCanvas.height = baseCanvas.height;

            // 设置样式使其叠加在原Canvas上
            crosshairCanvas.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                pointer-events: none;
                z-index: 10;
            `;

            // 确保父容器有相对定位
            const parent = baseCanvas.parentNode;
            if (parent) {
                if (getComputedStyle(parent).position === 'static') {
                    parent.style.position = 'relative';
                }
                parent.appendChild(crosshairCanvas);
            }

            console.log('✅ 创建主图十字光标叠加Canvas');
        } else {
            // 更新尺寸以匹配基础Canvas
            if (crosshairCanvas.width !== baseCanvas.width || crosshairCanvas.height !== baseCanvas.height) {
                crosshairCanvas.width = baseCanvas.width;
                crosshairCanvas.height = baseCanvas.height;
            }
        }

        return crosshairCanvas;
    }

    // 注意：重绘方法已移除，因为子图十字光标功能已禁用
    // 这样可以避免复杂的重绘逻辑和潜在的错误

    // 切换到指定周期
    switchToPeriod(period, button) {
        // 移除所有周期按钮的active状态
        document.querySelectorAll('.chart-btn, .period-btn').forEach(b => {
            if (!b.dataset.period || !['prev', 'next', 'refresh'].includes(b.dataset.period)) {
                b.classList.remove('active');
            }
        });

        // 激活当前按钮
        if (button) {
            button.classList.add('active');
        }

        this.currentPeriod = period;
        console.log('🔄 切换到周期:', period);

        if (this.currentStock) {
            this.updateChart(this.currentPeriod);
        }

        // 显示切换提示
        this.showPeriodSwitchMessage(period);
    }

    // 切换到上一个周期
    switchToPreviousPeriod() {
        const periods = ['tick', '1m', '5m', '15m', '30m', '60m', '120m', '1d', '1w', '1M'];
        const currentIndex = periods.indexOf(this.currentPeriod);

        if (currentIndex > 0) {
            const prevPeriod = periods[currentIndex - 1];
            const button = document.querySelector(`[data-period="${prevPeriod}"]`);
            this.switchToPeriod(prevPeriod, button);
        } else {
            this.showMessage('已经是第一个周期', 'info');
        }
    }

    // 切换到下一个周期
    switchToNextPeriod() {
        const periods = ['tick', '1m', '5m', '15m', '30m', '60m', '120m', '1d', '1w', '1M'];
        const currentIndex = periods.indexOf(this.currentPeriod);

        if (currentIndex < periods.length - 1) {
            const nextPeriod = periods[currentIndex + 1];
            const button = document.querySelector(`[data-period="${nextPeriod}"]`);
            this.switchToPeriod(nextPeriod, button);
        } else {
            this.showMessage('已经是最后一个周期', 'info');
        }
    }

    // 刷新当前周期
    refreshCurrentPeriod() {
        console.log('🔄 刷新当前周期:', this.currentPeriod);

        if (this.currentStock) {
            // 添加刷新动画
            const refreshBtn = document.querySelector('[data-period="refresh"]');
            if (refreshBtn) {
                refreshBtn.style.transform = 'rotate(360deg)';
                setTimeout(() => {
                    refreshBtn.style.transform = '';
                }, 500);
            }

            this.updateChart(this.currentPeriod);
            this.showMessage('数据已刷新', 'success');
        } else {
            this.showMessage('请先选择股票', 'warning');
        }
    }

    // 显示周期切换消息
    showPeriodSwitchMessage(period) {
        const periodNames = {
            'tick': '分时图',
            '1m': '1分钟',
            '5m': '5分钟',
            '15m': '15分钟',
            '30m': '30分钟',
            '60m': '60分钟',
            '120m': '120分钟',
            '1d': '日K线',
            '1w': '周K线',
            '1M': '月K线'
        };

        const periodName = periodNames[period] || period;

        // 创建临时提示
        const toast = document.createElement('div');
        toast.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            background: var(--primary-color);
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
            z-index: 10000;
            opacity: 0;
            transform: translateX(100px);
            transition: all 0.3s ease;
        `;
        toast.textContent = `切换到 ${periodName}`;
        document.body.appendChild(toast);

        // 显示动画
        setTimeout(() => {
            toast.style.opacity = '1';
            toast.style.transform = 'translateX(0)';
        }, 10);

        // 自动消失
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transform = 'translateX(100px)';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 2000);
    }

    async searchStock() {
        const searchInput = document.getElementById('stock-search');
        const query = searchInput.value.trim();
        
        if (!query) {
            this.showMessage('请输入股票代码或名称', 'warning');
            return;
        }

        this.showLoading();
        
        try {
            // 模拟搜索API调用
            const stockCode = this.parseStockCode(query);
            await this.loadStockData(stockCode);
        } catch (error) {
            this.showMessage('搜索失败，请检查股票代码', 'error');
        } finally {
            this.hideLoading();
        }
    }

    parseStockCode(query) {
        // 去除空格和特殊字符
        query = query.trim().replace(/[^\w\u4e00-\u9fa5]/g, '');

        // 如果是6位数字，直接返回
        if (/^\d{6}$/.test(query)) {
            return query;
        }

        // 扩展的股票名称到代码映射
        const nameToCode = {
            // 银行股
            '平安银行': '000001',
            '万科A': '000002',
            '万科': '000002',
            '招商银行': '600036',
            '工商银行': '601398',
            '建设银行': '601939',
            '农业银行': '601288',
            '中国银行': '601988',
            '交通银行': '601328',
            '浦发银行': '600000',
            '民生银行': '600016',
            '兴业银行': '601166',
            '中信银行': '601998',

            // 白酒股
            '贵州茅台': '600519',
            '茅台': '600519',
            '五粮液': '000858',
            '剑南春': '000858',
            '泸州老窖': '000568',
            '山西汾酒': '600809',
            '水井坊': '600779',

            // 科技股
            '腾讯控股': '00700',
            '阿里巴巴': '09988',
            '比亚迪': '002594',
            '宁德时代': '300750',
            '海康威视': '002415',
            '科大讯飞': '002230',
            '东方财富': '300059',
            '同花顺': '300033',

            // 医药股
            '恒瑞医药': '600276',
            '药明康德': '603259',
            '迈瑞医疗': '300760',
            '爱尔眼科': '300015',

            // 其他
            '京东方A': '000725',
            '京东方': '000725',
            '中国平安': '601318',
            '平安': '601318',
            '格力电器': '000651',
            '格力': '000651',
            '美的集团': '000333',
            '美的': '000333'
        };

        // 精确匹配
        if (nameToCode[query]) {
            return nameToCode[query];
        }

        // 模糊匹配
        for (const [name, code] of Object.entries(nameToCode)) {
            if (name.includes(query) || query.includes(name)) {
                return code;
            }
        }

        return query;
    }

    async loadStockData(stockCode) {
        console.log('🔄 开始加载股票数据:', stockCode);
        this.showLoading();

        try {
            // 模拟从东方财富网获取数据
            console.log('📡 调用 fetchStockData...');
            const stockData = await this.fetchStockData(stockCode);
            console.log('✅ 股票数据获取成功:', stockData);

            console.log('🖥️ 显示股票数据...');
            this.displayStockData(stockData);

            this.currentStock = stockCode;
            this.marketOpen = stockData.is_trading || false;

            console.log('📊 更新图表...');
            this.updateChart(this.currentPeriod);

            console.log('📝 生成分析...');
            this.generateAnalysis(stockData);

            console.log('🎉 股票数据加载完成!');
        } catch (error) {
            console.error('❌ 加载股票数据失败:', error);
            this.showMessage(`加载股票数据失败: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }

    async fetchStockData(stockCode) {
        try {
            // 转换显示代码为查询代码
            const queryCode = this.convertToQueryCode(stockCode);
            console.log(`🔄 代码转换: ${stockCode} -> ${queryCode}`);

            // 调用后端API获取真实数据
            const response = await fetch(`http://localhost:5001/api/stock/${queryCode}`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            console.log('📡 API响应数据:', result);

            if (result.success) {
                // 数据验证
                if (!result.data) {
                    throw new Error('API返回数据为空');
                }

                // 保持显示代码不变
                result.data.code = stockCode;
                return result.data;
            } else {
                throw new Error(result.error || '获取数据失败');
            }
        } catch (error) {
            console.error('❌ API调用失败:', error);
            throw new Error(`无法获取股票数据: ${error.message}`);
        }
    }



    displayStockData(data) {
        console.log('🖥️ 开始显示股票数据:', data);

        // 数据验证
        if (!data) {
            console.error('❌ 股票数据为空');
            this.showMessage('股票数据为空', 'error');
            return;
        }

        // 必要字段验证
        const requiredFields = ['name', 'code', 'price'];
        const missingFields = requiredFields.filter(field => !data[field]);
        if (missingFields.length > 0) {
            console.error('❌ 缺少必要字段:', missingFields);
            this.showMessage(`数据不完整，缺少: ${missingFields.join(', ')}`, 'error');
            return;
        }

        // 显示股票面板 - 兼容两种界面
        const stockPanel = document.getElementById('stock-panel');
        if (stockPanel) {
            stockPanel.style.display = 'block';
            stockPanel.classList.add('fade-in');
            console.log('✅ 股票面板已显示');
        } else {
            console.warn('⚠️ 股票面板元素未找到');
        }

        // 更新操作员面板数据
        this.updateOperatorPanel(data);

        // 更新基本信息 - 安全检查
        const stockNameEl = document.getElementById('stock-name');
        if (stockNameEl) {
            stockNameEl.textContent = data.name;
            console.log('✅ 更新股票名称:', data.name);
        } else {
            console.warn('⚠️ stock-name 元素未找到');
        }

        const stockCodeEl = document.getElementById('stock-code');
        if (stockCodeEl) {
            stockCodeEl.textContent = data.code;
            console.log('✅ 更新股票代码:', data.code);
        } else {
            console.warn('⚠️ stock-code 元素未找到');
        }

        // 显示市场、数据源和交易状态
        let marketText = data.market;
        if (data.source) {
            marketText += ` (${data.source})`;
        }
        if (data.market_status) {
            const statusColor = data.is_trading ? 'var(--color-up)' : 'var(--text-muted)';
            marketText += ` • ${data.market_status.message}`;

            // 更新状态指示器
            const statusEl = document.getElementById('data-status');
            if (statusEl) {
                const dotEl = statusEl.querySelector('i');
                const textEl = statusEl.querySelector('span');
                if (dotEl) {
                    dotEl.style.color = statusColor;
                }
                if (textEl) {
                    textEl.textContent = data.is_trading ? '实时数据' : '静态数据';
                }
            }
        }

        const stockMarketEl = document.getElementById('stock-market');
        if (stockMarketEl) {
            stockMarketEl.textContent = marketText;
            console.log('✅ 更新市场信息:', marketText);
        } else {
            console.warn('⚠️ stock-market 元素未找到');
        }

        // 更新价格信息 - 安全检查
        const currentPriceEl = document.getElementById('current-price');
        if (currentPriceEl) {
            currentPriceEl.textContent = data.price;
            console.log('✅ 更新当前价格:', data.price);
        } else {
            console.warn('⚠️ current-price 元素未找到');
        }

        const changeAmountEl = document.getElementById('change-amount');
        if (changeAmountEl) {
            changeAmountEl.textContent = data.change > 0 ? `+${data.change}` : data.change;
            console.log('✅ 更新涨跌额:', data.change);
        } else {
            console.warn('⚠️ change-amount 元素未找到');
        }

        const changePercentEl = document.getElementById('change-percent');
        if (changePercentEl) {
            changePercentEl.textContent = data.changePercent > 0 ? `+${data.changePercent}%` : `${data.changePercent}%`;
            console.log('✅ 更新涨跌幅:', data.changePercent);
        } else {
            console.warn('⚠️ change-percent 元素未找到');
        }

        // 设置涨跌颜色 - 安全检查
        const isPositive = parseFloat(data.change) > 0;
        if (currentPriceEl) {
            currentPriceEl.style.color = isPositive ? 'var(--color-up)' : 'var(--color-down)';
        }
        if (changeAmountEl) {
            changeAmountEl.style.color = isPositive ? 'var(--color-up)' : 'var(--color-down)';
        }
        if (changePercentEl) {
            changePercentEl.style.color = isPositive ? 'var(--color-up)' : 'var(--color-down)';
        }

        // 更新统计数据 - 安全检查
        const openPriceEl = document.getElementById('open-price');
        if (openPriceEl) {
            openPriceEl.textContent = data.open;
            console.log('✅ 更新开盘价:', data.open);
        } else {
            console.warn('⚠️ open-price 元素未找到');
        }

        const prevCloseEl = document.getElementById('prev-close');
        if (prevCloseEl) {
            prevCloseEl.textContent = data.prevClose;
            console.log('✅ 更新昨收价:', data.prevClose);
        } else {
            console.warn('⚠️ prev-close 元素未找到');
        }

        const highPriceEl = document.getElementById('high-price');
        if (highPriceEl) {
            highPriceEl.textContent = data.high;
            console.log('✅ 更新最高价:', data.high);
        } else {
            console.warn('⚠️ high-price 元素未找到');
        }

        const lowPriceEl = document.getElementById('low-price');
        if (lowPriceEl) {
            lowPriceEl.textContent = data.low;
            console.log('✅ 更新最低价:', data.low);
        } else {
            console.warn('⚠️ low-price 元素未找到');
        }

        // 格式化成交量和成交额显示 - 安全检查
        const volume = typeof data.volume === 'string' ? data.volume : `${data.volume}亿`;
        const turnover = typeof data.turnover === 'string' ? data.turnover : `${data.turnover}亿`;

        const volumeEl = document.getElementById('volume');
        if (volumeEl) {
            volumeEl.textContent = volume;
            console.log('✅ 更新成交量:', volume);
        } else {
            console.warn('⚠️ volume 元素未找到');
        }

        const turnoverEl = document.getElementById('turnover');
        if (turnoverEl) {
            turnoverEl.textContent = turnover;
            console.log('✅ 更新成交额:', turnover);
        } else {
            console.warn('⚠️ turnover 元素未找到');
        }

        // 更新扩展数据字段
        this.updateExtendedData(data);

        // 更新分时数据
        this.updateTimeData(data);

        // 更新资金流向数据
        this.updateFundFlowData(data);

        console.log('🖥️ 股票数据显示完成');
    }

    updateExtendedData(data) {
        // 计算振幅
        const amplitude = data.prevClose > 0 ?
            (((parseFloat(data.high) - parseFloat(data.low)) / parseFloat(data.prevClose)) * 100).toFixed(2) + '%' : '--';

        // 更新扩展数据
        this.safeUpdateElement('amplitude', amplitude);
        this.safeUpdateElement('turnover-rate', data.turnover_rate || '--');
        this.safeUpdateElement('pe-ratio', data.pe_ratio || '--');
        this.safeUpdateElement('pb-ratio', data.pb_ratio || '--');
        this.safeUpdateElement('market-cap', data.market_cap ? this.formatMoney(data.market_cap) : '--');
        this.safeUpdateElement('float-cap', data.float_cap ? this.formatMoney(data.float_cap) : '--');
    }

    safeUpdateElement(id, value) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
        }
    }

    updateTimeData(stockData) {
        // 模拟分时数据
        const timeDataList = document.getElementById('time-data-list');
        if (!timeDataList) return;

        const currentTime = new Date();
        const timeData = [];

        // 生成最近几个时间点的数据
        for (let i = 4; i >= 0; i--) {
            const time = new Date(currentTime.getTime() - i * 15 * 60 * 1000); // 15分钟间隔
            const timeStr = time.getHours().toString().padStart(2, '0') + ':' +
                           time.getMinutes().toString().padStart(2, '0');

            const basePrice = parseFloat(stockData.price) || 20;
            const randomChange = (Math.random() - 0.5) * 0.5;
            const price = (basePrice + randomChange).toFixed(2);
            const change = randomChange.toFixed(2);
            const volume = Math.floor(Math.random() * 1000) + 100;

            timeData.push({ time: timeStr, price, change, volume });
        }

        timeDataList.innerHTML = timeData.map(data => `
            <div class="time-data-row">
                <span>${data.time}</span>
                <span>${data.price}</span>
                <span class="${parseFloat(data.change) >= 0 ? 'price-up' : 'price-down'}">
                    ${parseFloat(data.change) >= 0 ? '+' : ''}${data.change}
                </span>
                <span>${data.volume}</span>
            </div>
        `).join('');
    }

    updateFundFlowData(stockData) {
        // 模拟资金流向数据
        const fundFlowData = {
            main: (Math.random() - 0.5) * 10000000,
            superLarge: (Math.random() - 0.5) * 5000000,
            large: (Math.random() - 0.5) * 3000000,
            medium: (Math.random() - 0.5) * 2000000,
            small: (Math.random() - 0.5) * 1000000
        };

        this.updateFundFlowElement('main-fund-flow', fundFlowData.main);
        this.updateFundFlowElement('super-large-flow', fundFlowData.superLarge);
        this.updateFundFlowElement('large-flow', fundFlowData.large);
        this.updateFundFlowElement('medium-flow', fundFlowData.medium);
        this.updateFundFlowElement('small-flow', fundFlowData.small);
    }

    updateFundFlowElement(id, value) {
        const element = document.getElementById(id);
        if (!element) return;

        const formattedValue = this.formatMoney(Math.abs(value));
        element.textContent = (value >= 0 ? '+' : '-') + formattedValue;
        element.className = 'flow-value ' + (value >= 0 ? 'positive' : 'negative');
    }

    // 更新操作员面板数据
    updateOperatorPanel(data) {
        console.log('🎯 更新操作员面板数据:', data);

        // 1. 更新交易状态
        this.updateTradingStatus(data);

        // 2. 更新技术信号
        this.updateTechnicalSignals(data);

        // 3. 更新操作建议
        this.updateOperationAdvice(data);

        // 4. 更新资金流向指示器
        this.updateFlowIndicator(data);

        // 5. 绑定快速操作按钮
        this.bindQuickActions(data);
    }

    updateTradingStatus(data) {
        // 更新交易状态
        const statusEl = document.getElementById('trading-status');
        if (statusEl) {
            const isTrading = data.is_trading || this.isMarketOpen(new Date());
            statusEl.textContent = isTrading ? '交易中' : '休市';
            statusEl.className = 'status-badge ' + (isTrading ? 'trading' : 'closed');
        }

        // 更新最后更新时间
        const updateTimeEl = document.getElementById('last-update');
        if (updateTimeEl) {
            const now = new Date();
            updateTimeEl.textContent = now.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        }
    }

    updateTechnicalSignals(data) {
        // 计算技术信号
        const signals = this.calculateTechnicalSignals(data);

        this.safeUpdateElement('trend-signal', signals.trend);
        this.safeUpdateElement('support-level', signals.support);
        this.safeUpdateElement('resistance-level', signals.resistance);
        this.safeUpdateElement('strength-signal', signals.strength);

        // 添加信号样式
        this.updateSignalStyle('trend-signal', signals.trendType);
        this.updateSignalStyle('strength-signal', signals.strengthType);
    }

    calculateTechnicalSignals(data) {
        const price = parseFloat(data.price);
        const prevClose = parseFloat(data.prevClose);
        const high = parseFloat(data.high);
        const low = parseFloat(data.low);
        const changePercent = parseFloat(data.changePercent);

        // 简单的技术信号计算
        let trend = '横盘';
        let trendType = 'neutral';
        if (changePercent > 2) {
            trend = '强势上涨';
            trendType = 'bullish';
        } else if (changePercent > 0) {
            trend = '温和上涨';
            trendType = 'bullish';
        } else if (changePercent < -2) {
            trend = '强势下跌';
            trendType = 'bearish';
        } else if (changePercent < 0) {
            trend = '温和下跌';
            trendType = 'bearish';
        }

        // 计算支撑位和阻力位（简化版）
        const support = (low * 0.98).toFixed(2);
        const resistance = (high * 1.02).toFixed(2);

        // 计算强度
        const amplitude = ((high - low) / prevClose * 100);
        let strength = '弱';
        let strengthType = 'neutral';
        if (amplitude > 5) {
            strength = '强';
            strengthType = 'bullish';
        } else if (amplitude > 3) {
            strength = '中';
            strengthType = 'neutral';
        }

        return {
            trend,
            trendType,
            support,
            resistance,
            strength,
            strengthType
        };
    }

    updateSignalStyle(elementId, signalType) {
        const element = document.getElementById(elementId);
        if (element) {
            element.className = `signal-value ${signalType}`;
        }
    }

    updateOperationAdvice(data) {
        const advice = this.generateOperationAdvice(data);

        // 更新风险等级
        const riskEl = document.getElementById('risk-level');
        if (riskEl) {
            riskEl.textContent = advice.riskLevel;
            riskEl.className = `risk-badge ${advice.riskClass}`;
        }

        // 更新操作建议
        this.safeUpdateElement('operation-advice', advice.suggestion);
    }

    generateOperationAdvice(data) {
        const changePercent = parseFloat(data.changePercent);
        const volume = parseFloat(data.volume);

        let riskLevel = '中等';
        let riskClass = 'medium';
        let suggestion = '';

        // 基于涨跌幅和成交量的简单建议逻辑
        if (Math.abs(changePercent) > 5) {
            riskLevel = '高';
            riskClass = 'high';
            if (changePercent > 5) {
                suggestion = '股价大幅上涨，建议谨慎追高，可考虑分批减仓或设置止盈点。';
            } else {
                suggestion = '股价大幅下跌，建议观望为主，等待企稳信号后再考虑介入。';
            }
        } else if (Math.abs(changePercent) > 2) {
            riskLevel = '中等';
            riskClass = 'medium';
            if (changePercent > 2) {
                suggestion = '股价温和上涨，可适量持有，注意控制仓位。';
            } else {
                suggestion = '股价有所回调，可关注支撑位表现，择机布局。';
            }
        } else {
            riskLevel = '低';
            riskClass = 'low';
            suggestion = '股价波动较小，可继续观察，等待明确方向信号。';
        }

        return { riskLevel, riskClass, suggestion };
    }

    updateFlowIndicator(data) {
        // 模拟资金流向趋势
        const flowTrendEl = document.getElementById('flow-trend');
        if (flowTrendEl) {
            const changePercent = parseFloat(data.changePercent);
            if (changePercent > 1) {
                flowTrendEl.textContent = '净流入';
                flowTrendEl.className = 'flow-indicator inflow';
            } else if (changePercent < -1) {
                flowTrendEl.textContent = '净流出';
                flowTrendEl.className = 'flow-indicator outflow';
            } else {
                flowTrendEl.textContent = '平衡';
                flowTrendEl.className = 'flow-indicator';
            }
        }
    }

    bindQuickActions(data) {
        // 绑定快速操作按钮事件
        const watchBtn = document.getElementById('add-to-watch');
        const alertBtn = document.getElementById('set-alert');
        const refreshBtn = document.getElementById('refresh-data');
        const exportBtn = document.getElementById('export-data');

        if (watchBtn) {
            watchBtn.onclick = () => this.addToWatchlist(data);
        }
        if (alertBtn) {
            alertBtn.onclick = () => this.setAlert(data);
        }
        if (refreshBtn) {
            refreshBtn.onclick = () => this.refreshCurrentStock();
        }
        if (exportBtn) {
            exportBtn.onclick = () => this.exportStockData(data);
        }
    }

    // 快速操作功能实现
    addToWatchlist(data) {
        console.log('📌 添加到自选股:', data.name, data.code);

        // 模拟添加到自选股
        let watchlist = JSON.parse(localStorage.getItem('stockWatchlist') || '[]');
        const stockInfo = {
            code: data.code,
            name: data.name,
            addTime: new Date().toISOString()
        };

        // 检查是否已存在
        const exists = watchlist.some(item => item.code === data.code);
        if (!exists) {
            watchlist.push(stockInfo);
            localStorage.setItem('stockWatchlist', JSON.stringify(watchlist));
            this.showMessage(`${data.name} 已添加到自选股`, 'success');
        } else {
            this.showMessage(`${data.name} 已在自选股中`, 'info');
        }
    }

    setAlert(data) {
        console.log('🔔 设置价格提醒:', data.name, data.code);

        // 简单的价格提醒设置
        const currentPrice = parseFloat(data.price);
        const upperLimit = prompt(`设置 ${data.name} 的价格上限提醒（当前价格：${currentPrice}）：`, (currentPrice * 1.05).toFixed(2));
        const lowerLimit = prompt(`设置 ${data.name} 的价格下限提醒（当前价格：${currentPrice}）：`, (currentPrice * 0.95).toFixed(2));

        if (upperLimit && lowerLimit) {
            const alertInfo = {
                code: data.code,
                name: data.name,
                upperLimit: parseFloat(upperLimit),
                lowerLimit: parseFloat(lowerLimit),
                createTime: new Date().toISOString()
            };

            let alerts = JSON.parse(localStorage.getItem('stockAlerts') || '[]');
            // 移除同一股票的旧提醒
            alerts = alerts.filter(item => item.code !== data.code);
            alerts.push(alertInfo);
            localStorage.setItem('stockAlerts', JSON.stringify(alerts));

            this.showMessage(`${data.name} 价格提醒已设置`, 'success');
        }
    }

    refreshCurrentStock() {
        console.log('🔄 刷新当前股票数据');
        if (this.currentStock) {
            this.loadStockData(this.currentStock);
        } else {
            this.showMessage('请先选择一只股票', 'warning');
        }
    }

    exportStockData(data) {
        console.log('📊 导出股票数据:', data.name, data.code);

        // 创建导出数据
        const exportData = {
            基本信息: {
                股票名称: data.name,
                股票代码: data.code,
                当前价格: data.price,
                涨跌额: data.change,
                涨跌幅: data.changePercent + '%',
                导出时间: new Date().toLocaleString('zh-CN')
            },
            交易数据: {
                今开: data.open,
                昨收: data.prevClose,
                最高: data.high,
                最低: data.low,
                成交量: data.volume,
                成交额: data.turnover,
                换手率: data.turnover_rate || '--',
                振幅: this.calculateAmplitude(data)
            },
            估值指标: {
                市盈率: data.pe_ratio || '--',
                市净率: data.pb_ratio || '--',
                总市值: data.market_cap || '--',
                流通市值: data.float_cap || '--'
            }
        };

        // 转换为JSON字符串并下载
        const jsonString = JSON.stringify(exportData, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `${data.name}_${data.code}_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showMessage(`${data.name} 数据已导出`, 'success');
    }

    calculateAmplitude(data) {
        const high = parseFloat(data.high);
        const low = parseFloat(data.low);
        const prevClose = parseFloat(data.prevClose);

        if (prevClose > 0) {
            return (((high - low) / prevClose) * 100).toFixed(2) + '%';
        }
        return '--';
    }

    // 渲染分时图
    async renderIntradayChart(queryCode) {
        console.log('🕐 开始渲染分时图:', queryCode);

        try {
            // 获取分时数据
            const response = await fetch(`http://localhost:5001/api/intraday/${queryCode}`);
            const result = await response.json();

            if (result.success && result.data && result.data.length > 0) {
                console.log('✅ 分时数据获取成功:', result.data.length, '个数据点');
                this.drawIntradayChart(result.data);

                // 保存分时数据用于实时更新
                this.lastIntradayData = result.data;
            } else {
                console.warn('⚠️ 分时数据获取失败或数据为空，使用模拟数据');
                // 使用模拟分时数据
                const mockData = this.generateMockIntradayData();
                this.drawIntradayChart(mockData);
                this.lastIntradayData = mockData;
            }
        } catch (error) {
            console.error('❌ 分时图渲染失败:', error);
            // 使用模拟数据作为后备
            const mockData = this.generateMockIntradayData();
            this.drawIntradayChart(mockData);
            this.lastIntradayData = mockData;
        }
    }

    // 生成模拟分时数据
    generateMockIntradayData() {
        console.log('🎭 生成模拟分时数据');
        const data = [];
        const now = new Date();
        const marketOpen = new Date(now);
        marketOpen.setHours(9, 30, 0, 0); // 9:30开盘

        // 获取当前股票价格作为基准
        const basePrice = this.getCurrentStockPrice() || 20.00;
        let currentPrice = basePrice;

        // 生成从开盘到现在的分时数据（每分钟一个点）
        const currentTime = Math.min(now.getTime(), marketOpen.getTime() + 4 * 60 * 60 * 1000); // 最多4小时
        const startTime = marketOpen.getTime();

        for (let time = startTime; time <= currentTime; time += 60000) { // 每分钟
            const date = new Date(time);

            // 跳过中午休市时间 (11:30-13:00)
            if (date.getHours() === 11 && date.getMinutes() >= 30) {
                time += 90 * 60000; // 跳过90分钟
                continue;
            }
            if (date.getHours() === 12) {
                continue;
            }

            // 模拟价格波动
            const volatility = 0.002; // 0.2%的波动率
            const change = (Math.random() - 0.5) * volatility * basePrice;
            currentPrice = Math.max(currentPrice + change, basePrice * 0.9); // 最低不超过基准价的90%
            currentPrice = Math.min(currentPrice, basePrice * 1.1); // 最高不超过基准价的110%

            data.push({
                time: date.toTimeString().substr(0, 5), // HH:MM格式
                timestamp: time,
                price: parseFloat(currentPrice.toFixed(2)),
                volume: Math.floor(Math.random() * 1000) + 100,
                avgPrice: parseFloat((basePrice + (currentPrice - basePrice) * 0.7).toFixed(2))
            });
        }

        console.log('✅ 生成了', data.length, '个分时数据点');
        return data;
    }

    // 获取当前股票价格
    getCurrentStockPrice() {
        const priceEl = document.getElementById('current-price');
        if (priceEl && priceEl.textContent && priceEl.textContent !== '--') {
            return parseFloat(priceEl.textContent);
        }
        return null;
    }

    // 绘制分时图
    drawIntradayChart(data) {
        console.log('🎨 开始绘制分时图:', data.length, '个数据点');

        // 获取图表容器
        const container = document.getElementById('price-chart') || document.getElementById('main-chart');
        if (!container) {
            console.error('❌ 图表容器未找到');
            return;
        }

        // 创建分时图Canvas
        const containerWidth = container.clientWidth || 800;
        const containerHeight = container.clientHeight || 400;

        container.innerHTML = `
            <div style="position: relative; width: 100%; height: 100%;">
                <canvas id="intraday-chart" width="${containerWidth}" height="${containerHeight}"
                        style="width: 100%; height: 100%; background: #0f1419;"></canvas>
                <div id="intraday-info" style="position: absolute; top: 10px; left: 10px; color: #fff; font-size: 12px; background: rgba(0,0,0,0.7); padding: 8px; border-radius: 4px; display: none;">
                    <div id="hover-time">时间: --</div>
                    <div id="hover-price">价格: --</div>
                    <div id="hover-volume">成交量: --</div>
                </div>
            </div>
        `;

        const canvas = document.getElementById('intraday-chart');
        const ctx = canvas.getContext('2d');

        if (!data || data.length === 0) {
            this.drawNoDataMessage(ctx, canvas);
            return;
        }

        // 绘制分时图表
        this.drawIntradayChartContent(ctx, canvas, data);

        // 添加鼠标交互
        this.addIntradayChartInteraction(canvas, data);
    }

    // 绘制分时图内容
    drawIntradayChartContent(ctx, canvas, data) {
        const padding = { top: 40, right: 80, bottom: 60, left: 60 };
        const chartWidth = canvas.width - padding.left - padding.right;
        const chartHeight = canvas.height - padding.top - padding.bottom;

        // 清空画布
        ctx.fillStyle = '#0f1419';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // 获取价格范围
        const prices = data.map(d => d.price);
        const volumes = data.map(d => d.volume);
        const maxPrice = Math.max(...prices);
        const minPrice = Math.min(...prices);
        const maxVolume = Math.max(...volumes);
        const priceRange = maxPrice - minPrice || 1;

        // 计算昨收价格（用于计算涨跌）
        const yesterdayClose = this.getYesterdayClosePrice() || prices[0];

        // 绘制网格和坐标轴
        this.drawIntradayGrid(ctx, padding, chartWidth, chartHeight, minPrice, maxPrice, yesterdayClose);

        // 绘制成交量柱状图（底部）
        const volumeHeight = chartHeight * 0.2; // 成交量占20%高度
        const priceHeight = chartHeight * 0.8;  // 价格占80%高度

        this.drawIntradayVolume(ctx, data, padding, chartWidth, volumeHeight, maxVolume, chartHeight);

        // 绘制分时线和均价线
        this.drawIntradayPriceLine(ctx, data, padding, chartWidth, priceHeight, minPrice, priceRange);

        // 绘制当前价格指示线
        this.drawCurrentPriceIndicator(ctx, data, padding, chartWidth, priceHeight, minPrice, priceRange, yesterdayClose);

        // 绘制时间轴
        this.drawIntradayTimeAxis(ctx, data, padding, chartWidth, canvas.height);

        // 绘制价格轴
        this.drawIntradayPriceAxis(ctx, padding, chartHeight, minPrice, maxPrice, yesterdayClose);

        // 绘制标题信息
        this.drawIntradayTitle(ctx, data, yesterdayClose);
    }

    // 获取昨收价格
    getYesterdayClosePrice() {
        const prevCloseEl = document.getElementById('prev-close');
        if (prevCloseEl && prevCloseEl.textContent && prevCloseEl.textContent !== '--') {
            return parseFloat(prevCloseEl.textContent);
        }
        return null;
    }

    // 绘制分时图网格
    drawIntradayGrid(ctx, padding, chartWidth, chartHeight, minPrice, maxPrice, yesterdayClose) {
        ctx.strokeStyle = '#2a2e39';
        ctx.lineWidth = 1;

        // 绘制水平网格线（价格）
        const priceLines = 5;
        for (let i = 0; i <= priceLines; i++) {
            const y = padding.top + (chartHeight * 0.8 * i / priceLines);
            ctx.beginPath();
            ctx.moveTo(padding.left, y);
            ctx.lineTo(padding.left + chartWidth, y);
            ctx.stroke();
        }

        // 绘制昨收价格线（红色虚线）
        if (yesterdayClose && yesterdayClose >= minPrice && yesterdayClose <= maxPrice) {
            const priceRange = maxPrice - minPrice || 1;
            const y = padding.top + chartHeight * 0.8 * (1 - (yesterdayClose - minPrice) / priceRange);

            ctx.strokeStyle = '#666';
            ctx.lineWidth = 1;
            ctx.setLineDash([5, 5]);
            ctx.beginPath();
            ctx.moveTo(padding.left, y);
            ctx.lineTo(padding.left + chartWidth, y);
            ctx.stroke();
            ctx.setLineDash([]);
        }

        // 绘制垂直网格线（时间）
        const timeLines = 6;
        for (let i = 0; i <= timeLines; i++) {
            const x = padding.left + (chartWidth * i / timeLines);
            ctx.strokeStyle = '#2a2e39';
            ctx.beginPath();
            ctx.moveTo(x, padding.top);
            ctx.lineTo(x, padding.top + chartHeight);
            ctx.stroke();
        }
    }

    // 绘制成交量
    drawIntradayVolume(ctx, data, padding, chartWidth, volumeHeight, maxVolume, totalHeight) {
        if (maxVolume === 0) return;

        const barWidth = chartWidth / data.length;
        const volumeBottom = totalHeight - padding.bottom;
        const volumeTop = volumeBottom - volumeHeight;

        data.forEach((point, index) => {
            const x = padding.left + (index * barWidth);
            const height = (point.volume / maxVolume) * volumeHeight;
            const y = volumeBottom - height;

            // 根据价格涨跌设置颜色
            const prevPrice = index > 0 ? data[index - 1].price : point.price;
            ctx.fillStyle = point.price >= prevPrice ? '#f4433680' : '#4caf5080';

            ctx.fillRect(x, y, barWidth * 0.8, height);
        });
    }

    // 绘制分时价格线
    drawIntradayPriceLine(ctx, data, padding, chartWidth, priceHeight, minPrice, priceRange) {
        if (data.length === 0) return;

        const pointWidth = chartWidth / (data.length - 1);

        // 绘制价格线
        ctx.strokeStyle = '#00d4ff';
        ctx.lineWidth = 2;
        ctx.beginPath();

        data.forEach((point, index) => {
            const x = padding.left + (index * pointWidth);
            const y = padding.top + priceHeight * (1 - (point.price - minPrice) / priceRange);

            if (index === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        });
        ctx.stroke();

        // 绘制均价线（如果有均价数据）
        if (data[0].avgPrice !== undefined) {
            ctx.strokeStyle = '#ffa726';
            ctx.lineWidth = 1;
            ctx.beginPath();

            data.forEach((point, index) => {
                const x = padding.left + (index * pointWidth);
                const y = padding.top + priceHeight * (1 - (point.avgPrice - minPrice) / priceRange);

                if (index === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            });
            ctx.stroke();
        }

        // 填充价格线下方区域
        ctx.fillStyle = 'rgba(0, 212, 255, 0.1)';
        ctx.beginPath();
        data.forEach((point, index) => {
            const x = padding.left + (index * pointWidth);
            const y = padding.top + priceHeight * (1 - (point.price - minPrice) / priceRange);

            if (index === 0) {
                ctx.moveTo(x, padding.top + priceHeight);
                ctx.lineTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        });
        ctx.lineTo(padding.left + chartWidth, padding.top + priceHeight);
        ctx.closePath();
        ctx.fill();
    }

    // 绘制当前价格指示线
    drawCurrentPriceIndicator(ctx, data, padding, chartWidth, priceHeight, minPrice, priceRange, yesterdayClose) {
        if (data.length === 0) return;

        const currentPrice = data[data.length - 1].price;
        const y = padding.top + priceHeight * (1 - (currentPrice - minPrice) / priceRange);

        // 绘制价格指示线
        ctx.strokeStyle = '#fff';
        ctx.lineWidth = 1;
        ctx.setLineDash([3, 3]);
        ctx.beginPath();
        ctx.moveTo(padding.left, y);
        ctx.lineTo(padding.left + chartWidth, y);
        ctx.stroke();
        ctx.setLineDash([]);

        // 绘制价格标签
        const change = currentPrice - yesterdayClose;
        const changePercent = yesterdayClose ? ((change / yesterdayClose) * 100) : 0;
        const isUp = change >= 0;

        ctx.fillStyle = isUp ? '#f44336' : '#4caf50';
        ctx.fillRect(padding.left + chartWidth + 5, y - 12, 70, 24);

        ctx.fillStyle = '#fff';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(currentPrice.toFixed(2), padding.left + chartWidth + 40, y + 4);
    }

    // 绘制时间轴
    drawIntradayTimeAxis(ctx, data, padding, chartWidth, canvasHeight) {
        if (data.length === 0) return;

        ctx.fillStyle = '#8a8e99';
        ctx.font = '11px Arial';
        ctx.textAlign = 'center';

        // 显示关键时间点
        const timePoints = ['09:30', '10:30', '11:30', '13:00', '14:00', '15:00'];
        const totalMinutes = 240; // 4小时交易时间

        timePoints.forEach(timeStr => {
            const [hour, minute] = timeStr.split(':').map(Number);
            let minutesFromOpen;

            if (hour < 11 || (hour === 11 && minute <= 30)) {
                // 上午时段
                minutesFromOpen = (hour - 9) * 60 + (minute - 30);
            } else {
                // 下午时段
                minutesFromOpen = 120 + (hour - 13) * 60 + minute;
            }

            if (minutesFromOpen >= 0 && minutesFromOpen <= totalMinutes) {
                const x = padding.left + (minutesFromOpen / totalMinutes) * chartWidth;
                ctx.fillText(timeStr, x, canvasHeight - padding.bottom + 20);
            }
        });
    }

    // 绘制价格轴
    drawIntradayPriceAxis(ctx, padding, chartHeight, minPrice, maxPrice, yesterdayClose) {
        ctx.fillStyle = '#8a8e99';
        ctx.font = '11px Arial';
        ctx.textAlign = 'right';

        const priceHeight = chartHeight * 0.8;
        const priceLines = 5;

        for (let i = 0; i <= priceLines; i++) {
            const price = minPrice + (maxPrice - minPrice) * (priceLines - i) / priceLines;
            const y = padding.top + (priceHeight * i / priceLines);

            // 根据与昨收价格的关系设置颜色
            let color = '#8a8e99';
            if (yesterdayClose) {
                if (price > yesterdayClose) color = '#f44336';
                else if (price < yesterdayClose) color = '#4caf50';
            }

            ctx.fillStyle = color;
            ctx.fillText(price.toFixed(2), padding.left - 10, y + 4);
        }
    }

    // 绘制标题信息
    drawIntradayTitle(ctx, data, yesterdayClose) {
        if (data.length === 0) return;

        const currentData = data[data.length - 1];
        const change = currentData.price - yesterdayClose;
        const changePercent = yesterdayClose ? ((change / yesterdayClose) * 100) : 0;
        const isUp = change >= 0;

        // 绘制实时信息
        ctx.fillStyle = '#fff';
        ctx.font = 'bold 14px Arial';
        ctx.textAlign = 'left';
        ctx.fillText(`分时图 ${currentData.time}`, 20, 25);

        ctx.fillStyle = isUp ? '#f44336' : '#4caf50';
        ctx.font = '12px Arial';
        ctx.fillText(`${currentData.price.toFixed(2)} ${isUp ? '+' : ''}${change.toFixed(2)} (${isUp ? '+' : ''}${changePercent.toFixed(2)}%)`, 120, 25);

        // 绘制图例
        ctx.fillStyle = '#00d4ff';
        ctx.fillRect(20, 35, 15, 2);
        ctx.fillStyle = '#fff';
        ctx.font = '10px Arial';
        ctx.fillText('价格', 40, 40);

        if (currentData.avgPrice !== undefined) {
            ctx.fillStyle = '#ffa726';
            ctx.fillRect(80, 35, 15, 2);
            ctx.fillStyle = '#fff';
            ctx.fillText('均价', 100, 40);
        }
    }

    // 添加分时图交互
    addIntradayChartInteraction(canvas, data) {
        const infoDiv = document.getElementById('intraday-info');
        if (!infoDiv) return;

        canvas.addEventListener('mousemove', (e) => {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            // 计算鼠标对应的数据点
            const padding = { left: 60, right: 80 };
            const chartWidth = canvas.width - padding.left - padding.right;
            const dataIndex = Math.floor(((x - padding.left) / chartWidth) * data.length);

            if (dataIndex >= 0 && dataIndex < data.length) {
                const point = data[dataIndex];

                document.getElementById('hover-time').textContent = `时间: ${point.time}`;
                document.getElementById('hover-price').textContent = `价格: ${point.price.toFixed(2)}`;
                document.getElementById('hover-volume').textContent = `成交量: ${point.volume}`;

                infoDiv.style.display = 'block';
                infoDiv.style.left = Math.min(e.clientX - rect.left + 10, rect.width - 150) + 'px';
                infoDiv.style.top = (e.clientY - rect.top - 60) + 'px';
            }
        });

        canvas.addEventListener('mouseleave', () => {
            infoDiv.style.display = 'none';
        });
    }

    // 启动分时图实时更新
    startIntradayRealTimeUpdate() {
        console.log('🔄 启动分时图实时更新');

        // 清除之前的定时器
        this.stopIntradayRealTimeUpdate();

        // 每30秒更新一次分时数据
        this.intradayUpdateInterval = setInterval(() => {
            if (this.currentPeriod === 'tick' && this.currentStock) {
                console.log('🔄 实时更新分时图数据');
                this.updateIntradayData();
            }
        }, 30000);

        // 每5秒更新一次当前价格点
        this.intradayPriceInterval = setInterval(() => {
            if (this.currentPeriod === 'tick' && this.currentStock) {
                this.updateCurrentIntradayPoint();
            }
        }, 5000);
    }

    // 停止分时图实时更新
    stopIntradayRealTimeUpdate() {
        if (this.intradayUpdateInterval) {
            clearInterval(this.intradayUpdateInterval);
            this.intradayUpdateInterval = null;
            console.log('⏹️ 停止分时图数据更新');
        }

        if (this.intradayPriceInterval) {
            clearInterval(this.intradayPriceInterval);
            this.intradayPriceInterval = null;
            console.log('⏹️ 停止分时图价格更新');
        }
    }

    // 更新分时数据
    async updateIntradayData() {
        if (!this.currentStock) return;

        try {
            const queryCode = this.convertToQueryCode(this.currentStock);
            const response = await fetch(`http://localhost:5001/api/intraday/${queryCode}`);
            const result = await response.json();

            if (result.success && result.data) {
                console.log('✅ 分时数据实时更新成功');
                this.lastIntradayData = result.data;
                this.drawIntradayChart(result.data);
            } else {
                // 更新模拟数据
                this.updateMockIntradayData();
            }
        } catch (error) {
            console.warn('⚠️ 分时数据实时更新失败，使用模拟更新:', error);
            this.updateMockIntradayData();
        }
    }

    // 更新当前分时点
    updateCurrentIntradayPoint() {
        if (!this.lastIntradayData || this.lastIntradayData.length === 0) return;

        // 获取最新股票价格
        const currentPrice = this.getCurrentStockPrice();
        if (!currentPrice) return;

        // 更新最后一个数据点
        const lastPoint = this.lastIntradayData[this.lastIntradayData.length - 1];
        const now = new Date();

        // 如果是同一分钟，更新当前点；否则添加新点
        const currentTimeStr = now.toTimeString().substr(0, 5);
        if (lastPoint.time === currentTimeStr) {
            lastPoint.price = currentPrice;
            lastPoint.volume += Math.floor(Math.random() * 100) + 10;
        } else {
            // 添加新的数据点
            this.lastIntradayData.push({
                time: currentTimeStr,
                timestamp: now.getTime(),
                price: currentPrice,
                volume: Math.floor(Math.random() * 500) + 100,
                avgPrice: this.calculateAvgPrice(this.lastIntradayData, currentPrice)
            });
        }

        // 重新绘制图表
        this.drawIntradayChart(this.lastIntradayData);
    }

    // 更新模拟分时数据
    updateMockIntradayData() {
        if (!this.lastIntradayData) {
            this.lastIntradayData = this.generateMockIntradayData();
        } else {
            // 添加新的模拟数据点
            const lastPoint = this.lastIntradayData[this.lastIntradayData.length - 1];
            const now = new Date();
            const currentTimeStr = now.toTimeString().substr(0, 5);

            if (lastPoint.time !== currentTimeStr) {
                const volatility = 0.002;
                const change = (Math.random() - 0.5) * volatility * lastPoint.price;
                const newPrice = Math.max(lastPoint.price + change, lastPoint.price * 0.99);

                this.lastIntradayData.push({
                    time: currentTimeStr,
                    timestamp: now.getTime(),
                    price: parseFloat(newPrice.toFixed(2)),
                    volume: Math.floor(Math.random() * 500) + 100,
                    avgPrice: this.calculateAvgPrice(this.lastIntradayData, newPrice)
                });

                // 限制数据点数量
                if (this.lastIntradayData.length > 240) {
                    this.lastIntradayData.shift();
                }
            }
        }

        this.drawIntradayChart(this.lastIntradayData);
    }

    // 计算均价
    calculateAvgPrice(data, currentPrice) {
        if (data.length === 0) return currentPrice;

        const totalValue = data.reduce((sum, point) => sum + point.price * point.volume, 0) + currentPrice * 100;
        const totalVolume = data.reduce((sum, point) => sum + point.volume, 0) + 100;

        return parseFloat((totalValue / totalVolume).toFixed(2));
    }

    async updateChart(period) {
        if (!this.currentStock) {
            console.warn('❌ 没有当前股票，无法更新图表');
            this.showMessage('请先选择一只股票', 'warning');
            return;
        }

        console.log(`🔄 更新图表: 股票=${this.currentStock}, 周期=${period}, 类型=${this.currentChartType}, 均线=${this.activeMAs.join(',')}, 指标=${this.activeIndicators.join(',')}`);

        // 清理交互性能缓存，确保新数据的交互响应最佳
        this.clearPerformanceCache();

        // 数据验证
        if (!period) {
            console.error('❌ 周期参数无效');
            this.showMessage('周期参数错误', 'error');
            return;
        }

        try {
            this.showChartLoading();

            // 转换显示代码为查询代码
            const queryCode = this.convertToQueryCode(this.currentStock);
            console.log(`🔄 图表代码转换: ${this.currentStock} -> ${queryCode}`);

            // 判断是否为分时图
            if (period === 'tick') {
                console.log('🕐 切换到分时图模式');
                await this.renderIntradayChart(queryCode);
                // 启动分时图实时更新
                this.startIntradayRealTimeUpdate();
            } else {
                console.log('📊 切换到K线图模式');
                // 停止分时图实时更新
                this.stopIntradayRealTimeUpdate();

                // 🔧 修复：确保K线和技术指标数据数量一致
                // K线数据：获取100个数据点用于显示
                // 指标数据：也获取100个数据点，确保索引对应
                const displayCount = 100;
                const klineUrl = `http://localhost:5001/api/kline/${queryCode}?period=${period}&count=${displayCount}`;
                const indicatorUrl = `http://localhost:5001/api/indicators/${queryCode}?period=${period}&count=${displayCount}&mas=${this.activeMAs.join(',')}&indicators=${this.activeIndicators.join(',')}`;

                console.log('🔧 API请求URL:', { klineUrl, indicatorUrl });

                const [klineResponse, indicatorResponse] = await Promise.all([
                    fetch(klineUrl),
                    fetch(indicatorUrl)
                ]);

                if (!klineResponse.ok) {
                    throw new Error(`K线数据API错误: ${klineResponse.status} ${klineResponse.statusText}`);
                }
                if (!indicatorResponse.ok) {
                    throw new Error(`指标数据API错误: ${indicatorResponse.status} ${indicatorResponse.statusText}`);
                }

                const klineResult = await klineResponse.json();
                const indicatorResult = await indicatorResponse.json();

                if (klineResult.success && indicatorResult.success) {
                    console.log('数据获取成功，开始渲染图表');

                    // 🔧 强制清除旧数据和面板
                    this.lastKlineData = null;
                    this.lastIndicatorData = null;
                    const oldPanel = document.getElementById('crosshair-data-panel');
                    if (oldPanel) {
                        oldPanel.remove();
                        console.log('🔧 已清除旧数据面板');
                    }

                    // 保存新数据
                    this.lastKlineData = klineResult.data;
                    this.lastIndicatorData = indicatorResult.data;

                    // 🔧 调试：验证MACD数据是否正确接收
                    console.log('🔧 MACD数据验证:');
                    console.log('🔧 K线数据长度:', klineResult.data.klines?.length || 0);

                    if (indicatorResult.data.indicators && indicatorResult.data.indicators.macd) {
                        const macdData = indicatorResult.data.indicators.macd;
                        console.log('✅ MACD数据结构:', Object.keys(macdData));
                        console.log('✅ MACD数据长度:', {
                            dif: macdData.dif?.length || 0,
                            dea: macdData.dea?.length || 0,
                            macd: macdData.macd?.length || 0
                        });

                        // 检查最新几个值
                        const lastIndex = macdData.dif.length - 1;
                        console.log('✅ 最新MACD值 (索引' + lastIndex + '):', {
                            dif: macdData.dif[lastIndex],
                            dea: macdData.dea[lastIndex],
                            macd: macdData.macd[lastIndex]
                        });

                        // 检查前几个值对比
                        console.log('✅ 前5个MACD值对比:', {
                            dif: macdData.dif.slice(0, 5),
                            dea: macdData.dea.slice(0, 5),
                            macd: macdData.macd.slice(0, 5)
                        });

                        // 检查后5个值对比
                        console.log('✅ 后5个MACD值对比:', {
                            dif: macdData.dif.slice(-5),
                            dea: macdData.dea.slice(-5),
                            macd: macdData.macd.slice(-5)
                        });
                    } else {
                        console.error('❌ MACD数据缺失!');
                    }

                    // 渲染K线图
                    this.renderKlineChart(klineResult.data, indicatorResult.data);

                    console.log('图表更新完成');
                } else {
                    console.error('API返回错误:', {
                        kline: klineResult.error || '未知错误',
                        indicator: indicatorResult.error || '未知错误'
                    });
                    throw new Error(`获取图表数据失败: K线(${klineResult.success ? '成功' : '失败'}), 指标(${indicatorResult.success ? '成功' : '失败'})`);
                }
            }
        } catch (error) {
            console.error('更新图表失败:', error);
            this.showMessage(`图表数据加载失败: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }



    showChartLoading() {
        // 支持两种容器：原版和专业版
        const container = document.getElementById('price-chart') || document.getElementById('main-chart');
        if (container) {
            container.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 400px; color: var(--text-muted);">📊 正在加载图表数据...</div>';
        }
    }

    renderTimelineChart(klineData) {
        console.log('渲染分时图');
        const container = document.getElementById('price-chart');
        if (!container) return;

        // 创建分时图Canvas
        const containerWidth = container.clientWidth || 800;
        const containerHeight = 400;

        container.innerHTML = `<canvas id="timeline-chart" width="${containerWidth}" height="${containerHeight}" style="width: 100%; height: 100%;"></canvas>`;
        const canvas = document.getElementById('timeline-chart');
        const ctx = canvas.getContext('2d');

        if (!klineData || !klineData.klines || klineData.klines.length === 0) {
            this.drawNoDataMessage(ctx, canvas);
            return;
        }

        // 绘制分时图
        this.drawTimelineChart(ctx, canvas, klineData.klines);
    }

    renderKlineChart(klineData, indicatorData) {
        console.log('渲染K线图');

        // 保存数据供十字光标系统使用
        this.lastKlineData = klineData;
        this.lastIndicatorData = indicatorData;

        // 支持两种容器：原版和专业版
        let container = document.getElementById('price-chart') || document.getElementById('main-chart');
        if (!container) {
            console.error('图表容器未找到');
            return;
        }

        // 检查是否为专业版布局
        const isProfessionalLayout = document.getElementById('sub-charts') !== null;

        if (isProfessionalLayout) {
            // 专业版：主图只显示K线和均线，子图单独渲染
            this.renderProfessionalMainChart(container, klineData, indicatorData);
            const subChartsContainer = document.getElementById('sub-charts');
            if (subChartsContainer) {
                this.updateSubCharts(subChartsContainer, klineData, indicatorData);
            }
        } else {
            // 原版：在一个Canvas中绘制所有内容
            this.renderCombinedChart(container, klineData, indicatorData);
        }
    }

    renderProfessionalMainChart(container, klineData, indicatorData) {
        console.log('渲染专业版主图');

        // 创建主图Canvas
        const containerWidth = container.clientWidth || 800;
        const containerHeight = container.clientHeight || 400;

        container.innerHTML = `<canvas id="main-kline-chart" width="${containerWidth}" height="${containerHeight}" style="width: 100%; height: 100%;"></canvas>`;
        const canvas = document.getElementById('main-kline-chart');
        const ctx = canvas.getContext('2d');

        if (!klineData || !klineData.klines || klineData.klines.length === 0) {
            this.drawNoDataMessage(ctx, canvas);
            return;
        }

        // 只绘制主图内容（K线、均线、布林带）
        this.drawMainChartOnly(ctx, canvas, klineData, indicatorData);

        // 延迟生成对齐验证报告，等待子图渲染完成
        setTimeout(() => {
            this.generateAlignmentReport(canvas, this.getSubCanvases());
        }, 1000);
    }

    renderCombinedChart(container, klineData, indicatorData) {
        console.log('渲染组合图表');

        // 创建K线图Canvas
        const containerWidth = container.clientWidth || 800;
        const containerHeight = container.clientHeight || 400;

        container.innerHTML = `<canvas id="kline-chart" width="${containerWidth}" height="${containerHeight}" style="width: 100%; height: 100%;"></canvas>`;
        const canvas = document.getElementById('kline-chart');
        const ctx = canvas.getContext('2d');

        if (!klineData || !klineData.klines || klineData.klines.length === 0) {
            this.drawNoDataMessage(ctx, canvas);
            return;
        }

        // 绘制K线图和技术指标
        this.drawKlineWithIndicators(ctx, canvas, klineData, indicatorData);
    }

    drawMainChartOnly(ctx, canvas, klineData, indicatorData) {
        console.log('绘制专业版主图（仅K线和均线）');

        // 设置背景
        ctx.fillStyle = '#1e2329';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        const klines = this.getStandardKlineData(klineData.klines); // 使用统一的数据切片
        const padding = this.getStandardPadding();
        const chartWidth = canvas.width - padding.left - padding.right;
        const chartHeight = canvas.height - padding.top - padding.bottom;

        // 计算价格范围
        const prices = klines.map(k => [k.high, k.low, k.open, k.close]).flat();
        const maxPrice = Math.max(...prices);
        const minPrice = Math.min(...prices);
        const priceRange = maxPrice - minPrice || 1;
        const priceBuffer = priceRange * 0.1;
        const adjustedMax = maxPrice + priceBuffer;
        const adjustedMin = minPrice - priceBuffer;
        const adjustedRange = adjustedMax - adjustedMin;

        // 绘制网格
        this.drawGrid(ctx, padding, chartWidth, chartHeight);

        // 绘制K线
        this.drawCandlesticks(ctx, klines, padding, chartWidth, chartHeight, adjustedMin, adjustedRange);

        // 绘制均线
        if (this.activeMAs.length > 0 && indicatorData && indicatorData.indicators && indicatorData.indicators.ma) {
            this.drawMovingAverages(ctx, klines, indicatorData.indicators.ma, padding, chartWidth, chartHeight, adjustedMin, adjustedRange);
        }

        // 绘制布林带（如果激活）
        if (this.activeIndicators.includes('boll') && indicatorData && indicatorData.indicators && indicatorData.indicators.boll) {
            this.drawBollingerBands(ctx, klines, indicatorData.indicators.boll, padding, chartWidth, chartHeight, adjustedMin, adjustedRange);
        }

        // 绘制坐标轴标签
        this.drawAxisLabels(ctx, canvas, klines, padding, chartWidth, chartHeight, adjustedMin, adjustedMin + adjustedRange);

        // 绘制图例
        this.drawChartLegend(ctx, canvas, klines[klines.length - 1], indicatorData);

        // 绑定鼠标交互事件（十字光标系统）
        this.bindMouseInteraction(canvas);
    }

    drawTimelineChart(ctx, canvas, klines) {
        console.log('绘制分时图');

        // 设置背景
        ctx.fillStyle = '#1e2329';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        const padding = this.getStandardPadding();
        const chartWidth = canvas.width - padding.left - padding.right;
        const chartHeight = canvas.height - padding.top - padding.bottom;

        // 获取价格数据
        const prices = klines.map(k => k.close);
        const maxPrice = Math.max(...prices);
        const minPrice = Math.min(...prices);
        const priceRange = maxPrice - minPrice || 1;

        // 绘制网格
        this.drawGrid(ctx, padding, chartWidth, chartHeight);

        // 绘制分时线
        ctx.strokeStyle = '#f39c12';
        ctx.lineWidth = 2;
        ctx.beginPath();

        klines.forEach((kline, index) => {
            // 使用统一的x轴坐标计算，确保与主图K线完全对齐
            const x = this.calculateXPosition(index, klines.length, padding, chartWidth);
            const y = padding.top + chartHeight - ((kline.close - minPrice) / priceRange) * chartHeight;

            if (index === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        });

        ctx.stroke();

        // 绘制填充区域
        ctx.fillStyle = 'rgba(243, 156, 18, 0.1)';
        ctx.beginPath();
        ctx.moveTo(padding.left, padding.top + chartHeight);

        klines.forEach((kline, index) => {
            // 使用统一的x轴坐标计算，确保与主图K线完全对齐
            const x = this.calculateXPosition(index, klines.length, padding, chartWidth);
            const y = padding.top + chartHeight - ((kline.close - minPrice) / priceRange) * chartHeight;
            ctx.lineTo(x, y);
        });

        ctx.lineTo(padding.left + chartWidth, padding.top + chartHeight);
        ctx.closePath();
        ctx.fill();

        // 绘制坐标轴标签
        this.drawAxisLabels(ctx, canvas, klines, padding, chartWidth, chartHeight, minPrice, maxPrice);
    }

    drawKlineWithIndicators(ctx, canvas, klineData, indicatorData) {
        console.log('绘制K线图和指标');

        // 设置背景
        ctx.fillStyle = '#1e2329';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        const klines = this.getStandardKlineData(klineData.klines); // 使用统一的数据切片

        // 计算布局 - 根据子指标数量动态调整高度
        const subIndicators = this.activeIndicators.filter(ind => ['macd', 'kdj', 'rsi', 'volume'].includes(ind));
        const hasSubIndicators = subIndicators.length > 0;

        let mainChartHeight, subChartHeight;
        if (hasSubIndicators) {
            // 根据子指标数量动态分配高度
            const subIndicatorCount = subIndicators.length;
            const subChartRatio = Math.min(0.4, 0.15 + subIndicatorCount * 0.08); // 最多占40%
            mainChartHeight = canvas.height * (1 - subChartRatio - 0.05); // 留5%间隔
            subChartHeight = canvas.height * subChartRatio;
        } else {
            mainChartHeight = canvas.height * 0.9;
            subChartHeight = 0;
        }

        const padding = this.getStandardPadding();
        const chartWidth = canvas.width - padding.left - padding.right;

        // 计算价格范围
        const prices = klines.map(k => [k.high, k.low, k.open, k.close]).flat();
        const maxPrice = Math.max(...prices);
        const minPrice = Math.min(...prices);
        const priceRange = maxPrice - minPrice || 1;
        const priceBuffer = priceRange * 0.1;
        const adjustedMax = maxPrice + priceBuffer;
        const adjustedMin = minPrice - priceBuffer;
        const adjustedRange = adjustedMax - adjustedMin;

        // 绘制主图
        this.drawMainKlineChart(ctx, canvas, klines, indicatorData, padding, chartWidth, mainChartHeight, adjustedMin, adjustedRange);

        // 绘制子图指标
        if (hasSubIndicators) {
            this.drawSubIndicators(ctx, canvas, klines, indicatorData, padding, chartWidth, subChartHeight, mainChartHeight + 20);
        }
    }

    drawMainKlineChart(ctx, canvas, klines, indicatorData, padding, chartWidth, chartHeight, adjustedMin, adjustedRange) {
        // 绘制网格
        this.drawGrid(ctx, padding, chartWidth, chartHeight);

        // 绘制K线
        this.drawCandlesticks(ctx, klines, padding, chartWidth, chartHeight, adjustedMin, adjustedRange);

        // 绘制均线
        if (this.activeMAs.length > 0 && indicatorData && indicatorData.indicators && indicatorData.indicators.ma) {
            this.drawMovingAverages(ctx, klines, indicatorData.indicators.ma, padding, chartWidth, chartHeight, adjustedMin, adjustedRange);
        }

        // 主图只显示布林带，其他技术指标在子图显示
        if (this.activeIndicators.includes('boll') && indicatorData && indicatorData.indicators && indicatorData.indicators.boll) {
            this.drawBollingerBands(ctx, klines, indicatorData.indicators.boll, padding, chartWidth, chartHeight, adjustedMin, adjustedRange);
        }

        // 绘制坐标轴标签
        this.drawAxisLabels(ctx, canvas, klines, padding, chartWidth, chartHeight, adjustedMin, adjustedMin + adjustedRange);

        // 绘制图例
        this.drawChartLegend(ctx, canvas, klines[klines.length - 1], indicatorData);

        // 绑定鼠标交互事件（十字光标系统）
        this.bindMouseInteraction(canvas);
    }

    drawMovingAverages(ctx, klines, maData, padding, chartWidth, chartHeight, adjustedMin, adjustedRange) {
        const colors = {
            5: '#ff9800',   // 橙色
            10: '#9c27b0',  // 紫色
            20: '#2196f3',  // 蓝色
            30: '#4caf50',  // 绿色
            60: '#f44336',  // 红色
            120: '#795548', // 棕色
            250: '#607d8b'  // 蓝灰色
        };

        this.activeMAs.forEach(period => {
            const maKey = `ma${period}`;
            if (maData[maKey] && maData[maKey].length > 0) {
                const values = maData[maKey].slice(-klines.length);

                ctx.strokeStyle = colors[period] || '#ffffff';
                ctx.lineWidth = 1.5;
                ctx.beginPath();

                let hasValidPoint = false;
                values.forEach((value, index) => {
                    if (value !== null && value !== undefined) {
                        // 使用统一的x轴坐标计算，确保与主图K线完全对齐
                        const x = this.calculateXPosition(index, klines.length, padding, chartWidth);
                        const y = padding.top + chartHeight - ((value - adjustedMin) / adjustedRange) * chartHeight;

                        if (!hasValidPoint) {
                            ctx.moveTo(x, y);
                            hasValidPoint = true;
                        } else {
                            ctx.lineTo(x, y);
                        }
                    }
                });

                if (hasValidPoint) {
                    ctx.stroke();
                }
            }
        });
    }

    drawSubIndicators(ctx, canvas, klines, indicatorData, padding, chartWidth, subChartHeight, yOffset) {
        // 获取需要在子图显示的指标（排除布林带，它在主图显示）
        const subIndicators = this.activeIndicators.filter(ind => ['macd', 'kdj', 'rsi', 'volume'].includes(ind));

        if (subIndicators.length === 0) return;

        // 动态计算每个指标的高度，留出间隔
        const indicatorSpacing = 15; // 指标间间隔
        const availableHeight = subChartHeight - (subIndicators.length - 1) * indicatorSpacing;
        const indicatorHeight = Math.max(80, availableHeight / subIndicators.length); // 最小高度80px

        subIndicators.forEach((indicator, index) => {
            const indicatorY = yOffset + index * (indicatorHeight + indicatorSpacing);
            this.drawSubIndicator(ctx, canvas, klines, indicatorData, indicator, padding, chartWidth, indicatorHeight, indicatorY);
        });
    }

    drawSubIndicator(ctx, canvas, klines, indicatorData, indicator, padding, chartWidth, chartHeight, yOffset) {
        // 绘制子图背景
        ctx.fillStyle = '#1a1e24';
        ctx.fillRect(padding.left, yOffset, chartWidth, chartHeight);

        // 绘制边框
        ctx.strokeStyle = '#2b3139';
        ctx.lineWidth = 1;
        ctx.strokeRect(padding.left, yOffset, chartWidth, chartHeight);

        // 绘制指标标题
        ctx.fillStyle = '#d1d4dc';
        ctx.font = 'bold 12px Arial';
        ctx.textAlign = 'left';
        const titleY = yOffset + 15;
        ctx.fillText(indicator.toUpperCase(), padding.left + 10, titleY);

        // 调整绘制区域，为标题留出空间
        const drawingYOffset = yOffset + 20;
        const drawingHeight = chartHeight - 25;

        // 根据指标类型绘制
        if (indicator === 'volume') {
            this.drawVolumeIndicator(ctx, klines, padding, chartWidth, drawingHeight, drawingYOffset);
        } else if (indicatorData && indicatorData.indicators && indicatorData.indicators[indicator]) {
            this.drawTechnicalIndicator(ctx, klines, indicatorData.indicators[indicator], indicator, padding, chartWidth, drawingHeight, drawingYOffset);
        }
    }

    drawGrid(ctx, padding, chartWidth, chartHeight) {
        ctx.strokeStyle = '#2b3139';
        ctx.lineWidth = 1;

        // 绘制水平网格线
        for (let i = 0; i <= 5; i++) {
            const y = padding.top + (chartHeight / 5) * i;
            ctx.beginPath();
            ctx.moveTo(padding.left, y);
            ctx.lineTo(padding.left + chartWidth, y);
            ctx.stroke();
        }

        // 绘制垂直网格线
        for (let i = 0; i <= 10; i++) {
            const x = padding.left + (chartWidth / 10) * i;
            ctx.beginPath();
            ctx.moveTo(x, padding.top);
            ctx.lineTo(x, padding.top + chartHeight);
            ctx.stroke();
        }
    }

    drawCandlesticks(ctx, klines, padding, chartWidth, chartHeight, adjustedMin, adjustedRange) {
        const candleWidth = Math.max(2, chartWidth / klines.length * 0.6);

        klines.forEach((kline, index) => {
            const x = this.calculateXPosition(index, klines.length, padding, chartWidth);
            const openY = padding.top + chartHeight - ((kline.open - adjustedMin) / adjustedRange) * chartHeight;
            const closeY = padding.top + chartHeight - ((kline.close - adjustedMin) / adjustedRange) * chartHeight;
            const highY = padding.top + chartHeight - ((kline.high - adjustedMin) / adjustedRange) * chartHeight;
            const lowY = padding.top + chartHeight - ((kline.low - adjustedMin) / adjustedRange) * chartHeight;

            const isUp = kline.close >= kline.open;
            const color = isUp ? '#ff4757' : '#2ed573';

            // 绘制影线
            ctx.strokeStyle = color;
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(x, highY);
            ctx.lineTo(x, lowY);
            ctx.stroke();

            // 绘制实体
            ctx.fillStyle = color;
            const bodyHeight = Math.abs(closeY - openY);
            const bodyY = Math.min(openY, closeY);

            if (isUp) {
                ctx.strokeStyle = color;
                ctx.lineWidth = 1;
                ctx.strokeRect(x - candleWidth/2, bodyY, candleWidth, bodyHeight);
            } else {
                ctx.fillRect(x - candleWidth/2, bodyY, candleWidth, bodyHeight);
            }

            // 专业级对齐验证系统：绘制精确的对齐参考线
            if (index === 0 || index === klines.length - 1) {
                this.drawAlignmentReference(ctx, x, index, 'main-chart', '#ff0000');
            }
        });
    }

    drawAxisLabels(ctx, canvas, klines, padding, chartWidth, chartHeight, minPrice, maxPrice) {
        ctx.fillStyle = '#d1d4dc';
        ctx.font = '11px Arial';
        ctx.textAlign = 'right';

        // 绘制价格标签
        for (let i = 0; i <= 5; i++) {
            const price = minPrice + (maxPrice - minPrice) * (1 - i / 5);
            const y = padding.top + (chartHeight / 5) * i;
            ctx.fillText(price.toFixed(2), padding.left - 10, y + 4);
        }

        // 绘制时间标签
        ctx.textAlign = 'center';
        const timeStep = Math.max(1, Math.floor(klines.length / 6));
        for (let i = 0; i < klines.length; i += timeStep) {
            const x = this.calculateXPosition(i, klines.length, padding, chartWidth);
            const timeStr = klines[i].date.split(' ')[0]; // 只显示日期部分
            ctx.fillText(timeStr, x, canvas.height - 10);
        }
    }

    drawChartLegend(ctx, canvas, latestKline, indicatorData) {
        if (!latestKline) return;

        ctx.fillStyle = '#d1d4dc';
        ctx.font = '12px Arial';
        ctx.textAlign = 'left';

        const legendY = 20;
        let legendX = 20;

        // K线信息
        ctx.fillText(`开: ${latestKline.open}`, legendX, legendY);
        legendX += 80;
        ctx.fillText(`高: ${latestKline.high}`, legendX, legendY);
        legendX += 80;
        ctx.fillText(`低: ${latestKline.low}`, legendX, legendY);
        legendX += 80;
        ctx.fillText(`收: ${latestKline.close}`, legendX, legendY);

        // 均线图例
        if (this.activeMAs.length > 0 && indicatorData && indicatorData.indicators && indicatorData.indicators.ma) {
            this.drawMALegendOnChart(ctx, indicatorData.indicators.ma);
        }
    }

    drawMALegendOnChart(ctx, maData) {
        const colors = {
            5: '#ff9800',   // 橙色
            10: '#9c27b0',  // 紫色
            20: '#2196f3',  // 蓝色
            30: '#4caf50',  // 绿色
            60: '#f44336',  // 红色
            120: '#795548', // 棕色
            250: '#607d8b'  // 蓝灰色
        };

        let legendX = 20;
        const legendY = 40;

        this.activeMAs.forEach(period => {
            const maKey = `ma${period}`;
            if (maData[maKey] && maData[maKey].length > 0) {
                const values = maData[maKey];
                const latestValue = values[values.length - 1];

                if (latestValue !== null && latestValue !== undefined) {
                    // 绘制颜色方块
                    ctx.fillStyle = colors[period];
                    ctx.fillRect(legendX, legendY - 8, 12, 8);

                    // 绘制文字
                    ctx.fillStyle = colors[period];
                    ctx.font = '11px Arial';
                    ctx.fillText(`MA${period}: ${latestValue.toFixed(2)}`, legendX + 16, legendY);

                    legendX += 100;
                }
            }
        });
    }

    drawVolumeIndicator(ctx, klines, padding, chartWidth, chartHeight, yOffset) {
        // 计算成交量范围
        const volumes = klines.map(k => k.volume || 0);
        const maxVolume = Math.max(...volumes);
        const minVolume = 0;
        const volumeRange = maxVolume - minVolume || 1;

        const barWidth = Math.max(1, chartWidth / klines.length * 0.6);

        klines.forEach((kline, index) => {
            // 使用统一的x轴坐标计算，确保与主图K线完全对齐
            const x = this.calculateXPosition(index, klines.length, padding, chartWidth);
            const volume = kline.volume || 0;
            const barHeight = (volume / volumeRange) * chartHeight * 0.8;
            const barY = yOffset + chartHeight - barHeight;

            const isUp = kline.close >= kline.open;
            ctx.fillStyle = isUp ? '#ff4757' : '#2ed573';
            ctx.fillRect(x - barWidth/2, barY, barWidth, barHeight);

            // 在首尾位置绘制对齐参考线（绿色，区别于主图的红色）
            if (index === 0 || index === klines.length - 1) {
                this.drawAlignmentReference(ctx, x, index, 'volume-chart', '#00ff00');
            }
        });
    }

    drawTechnicalIndicator(ctx, klines, indicatorData, indicator, padding, chartWidth, chartHeight, yOffset) {
        // 这里可以根据不同的技术指标绘制不同的图形
        // 暂时简化处理
        ctx.fillStyle = '#d1d4dc';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(`${indicator.toUpperCase()} 指标图表`, padding.left + chartWidth/2, yOffset + chartHeight/2);
    }

    updateSubCharts(subChartsContainer, klineData, indicatorData) {
        console.log('🔄 更新子图表开始');
        console.log('活跃指标:', this.activeIndicators);
        console.log('K线数据:', klineData ? `${klineData.klines?.length}条` : '无');
        console.log('指标数据:', indicatorData ? '有' : '无');

        if (!subChartsContainer) {
            console.error('❌ 子图表容器未找到');
            return;
        }

        console.log('✅ 找到子图表容器:', subChartsContainer);
        console.log('容器当前尺寸:', subChartsContainer.clientWidth, 'x', subChartsContainer.clientHeight);

        // 清空现有的子图表
        subChartsContainer.innerHTML = '';

        // 只创建需要在子图显示的指标（排除布林带等主图指标）
        const subIndicators = this.activeIndicators.filter(ind => ['macd', 'kdj', 'rsi', 'volume'].includes(ind));

        console.log('📊 需要显示的子图指标:', subIndicators);

        // 如果没有选择子图指标，默认显示成交量
        const finalIndicators = subIndicators.length > 0 ? subIndicators : ['volume'];

        // 根据指标数量动态计算子图表高度分配
        const heightConfig = this.calculateSubChartHeights(finalIndicators.length);
        const totalHeight = heightConfig.totalHeight;
        const chartHeight = heightConfig.individualHeight;

        console.log(`📏 子图表布局: 总高度${totalHeight}px, 指标数量${finalIndicators.length}, 每个高度${chartHeight}px`);

        // 设置子图表容器 - 让内容自然展开，容器高度由内容决定
        subChartsContainer.style.cssText = `
            display: flex;
            flex-direction: column;
            background-color: #0f1419;
            padding: ${heightConfig.containerPadding / 2}px;
            gap: ${heightConfig.indicatorGap}px;
            box-sizing: border-box;
            min-height: auto;
        `;

        finalIndicators.forEach((indicator, index) => {
            console.log(`🎯 创建子图表 ${index + 1}/${finalIndicators.length}: ${indicator}`);
            this.createSubChart(indicator, subChartsContainer, klineData, indicatorData, heightConfig);
        });

        console.log('✅ 子图表更新完成');
    }

    createSubChart(indicator, parentContainer, klineData, indicatorData, heightConfig) {
        const chartHeight = heightConfig.individualHeight;
        console.log(`🎨 创建子图表: ${indicator}, 高度: ${chartHeight}px`);
        console.log('高度配置:', heightConfig);

        // 创建子图表容器
        const subChartDiv = document.createElement('div');
        subChartDiv.id = `${indicator}-chart`;
        subChartDiv.className = 'sub-chart';
        subChartDiv.style.cssText = `
            display: flex;
            flex-direction: column;
            background: #0f1419;
            border-radius: 6px;
            height: ${chartHeight}px;
            flex: none;
            border: 1px solid #2b3139;
            box-sizing: border-box;
        `;

        // 创建标题栏
        const headerDiv = document.createElement('div');
        headerDiv.className = 'sub-chart-header';
        headerDiv.style.cssText = `
            padding: 8px 12px;
            background: rgba(43, 49, 57, 0.5);
            border-bottom: 1px solid #2b3139;
            font-size: 12px;
            font-weight: bold;
            color: #d1d4dc;
            border-radius: 6px 6px 0 0;
            height: ${heightConfig.headerHeight}px;
            min-height: ${heightConfig.headerHeight}px;
            flex: none;
            box-sizing: border-box;
            display: flex;
            align-items: center;
        `;
        headerDiv.innerHTML = `<span class="sub-chart-title">${this.getIndicatorDisplayName(indicator)}</span>`;

        // 创建内容区域
        const contentHeight = heightConfig.contentHeight;
        const contentDiv = document.createElement('div');
        contentDiv.className = 'sub-chart-content';
        contentDiv.style.cssText = `
            flex: 1;
            position: relative;
            height: ${contentHeight}px;
            background: #0f1419;
            border-radius: 0 0 6px 6px;
            overflow: hidden;
        `;

        subChartDiv.appendChild(headerDiv);
        subChartDiv.appendChild(contentDiv);
        parentContainer.appendChild(subChartDiv);

        console.log(`✅ ${indicator} 子图表容器已创建`);
        console.log('高度分配 - 总高度:', chartHeight, '标题高度:', heightConfig.headerHeight, '内容高度:', contentHeight);

        // 延迟渲染，确保DOM已更新
        setTimeout(() => {
            console.log(`⏰ 开始渲染 ${indicator} 子图表内容`);
            this.renderSubChart(indicator, klineData, indicatorData, subChartDiv, heightConfig);
        }, 100);
    }

    getIndicatorDisplayName(indicator) {
        const names = {
            'volume': '成交量',
            'macd': 'MACD',
            'kdj': 'KDJ',
            'rsi': 'RSI',
            'boll': 'BOLL',
            'wr': 'WR',
            'cci': 'CCI',
            'bias': 'BIAS'
        };
        return names[indicator] || indicator.toUpperCase();
    }

    // 根据指标数量动态计算子图表高度分配
    calculateSubChartHeights(indicatorCount) {
        console.log(`📏 计算子图表高度分配，指标数量: ${indicatorCount}`);

        // 操作员专业需求：技术指标图表需要更宽的显示空间以便精确分析
        const config = {
            minIndividualHeight: 180,    // 提高最小高度，确保指标清晰显示
            containerPadding: 16,        // 容器内边距
            indicatorGap: 12,            // 指标间间距，适当增加以提高可读性
            headerHeight: 35,            // 标题栏高度，给标题更多空间
            minTotalHeight: 400          // 提高最小总高度
        };

        let individualHeight, totalHeight;

        // 专业级高度分配策略 - 为操作员提供更好的分析体验
        // 根据指标数量给每个指标充足的分析空间
        switch (indicatorCount) {
            case 1:
                individualHeight = 500; // 单个指标给更充足空间，便于详细分析
                break;
            case 2:
                individualHeight = 380; // 两个指标每个380px，保证足够的分析空间
                break;
            case 3:
                individualHeight = 320; // 三个指标每个320px，平衡显示效果
                break;
            case 4:
                individualHeight = 280; // 四个指标每个280px，确保基本分析需求
                break;
            default:
                individualHeight = Math.max(config.minIndividualHeight, 240); // 更多指标每个至少240px
                break;
        }

        // 计算总高度，让容器自动调整
        totalHeight = (individualHeight * indicatorCount) + (config.indicatorGap * (indicatorCount - 1)) + config.containerPadding;

        // 确保总高度不小于最小值，不设置最大限制
        totalHeight = Math.max(config.minTotalHeight, totalHeight);

        const result = {
            totalHeight,
            individualHeight,
            indicatorCount,
            containerPadding: config.containerPadding,
            indicatorGap: config.indicatorGap,
            headerHeight: config.headerHeight,
            contentHeight: individualHeight - config.headerHeight
        };

        console.log(`📊 高度分配结果:`, result);
        return result;
    }

    // 获取指标数值范围信息
    getIndicatorRanges(klineData, indicatorData) {
        const ranges = {};

        if (!klineData || !klineData.klines || !indicatorData || !indicatorData.indicators) {
            return ranges;
        }

        const klines = this.getStandardKlineData(klineData.klines);

        // MACD范围
        if (indicatorData.indicators.macd) {
            const macdData = indicatorData.indicators.macd;
            let allValues = [];

            if (macdData.macd) {
                allValues = allValues.concat(macdData.macd.slice(-klines.length).filter(v => v !== null && v !== undefined));
            }
            if (macdData.dif) {
                allValues = allValues.concat(macdData.dif.slice(-klines.length).filter(v => v !== null && v !== undefined));
            }
            if (macdData.dea) {
                allValues = allValues.concat(macdData.dea.slice(-klines.length).filter(v => v !== null && v !== undefined));
            }

            if (allValues.length > 0) {
                ranges.macd = {
                    min: Math.min(...allValues),
                    max: Math.max(...allValues)
                };
            }
        }

        // KDJ范围
        if (indicatorData.indicators.kdj) {
            const kdjData = indicatorData.indicators.kdj;
            let allValues = [];

            ['k', 'd', 'j'].forEach(line => {
                if (kdjData[line]) {
                    allValues = allValues.concat(kdjData[line].slice(-klines.length).filter(v => v !== null && v !== undefined && !isNaN(v)));
                }
            });

            if (allValues.length > 0) {
                ranges.kdj = {
                    min: Math.min(...allValues),
                    max: Math.max(...allValues)
                };
            }
        }

        // RSI范围
        if (indicatorData.indicators.rsi) {
            const rsiData = indicatorData.indicators.rsi;
            let allValues = [];

            Object.values(rsiData).forEach(values => {
                if (values) {
                    allValues = allValues.concat(values.slice(-klines.length).filter(v => v !== null && v !== undefined && !isNaN(v)));
                }
            });

            if (allValues.length > 0) {
                ranges.rsi = {
                    min: Math.min(...allValues),
                    max: Math.max(...allValues)
                };
            }
        }

        // 成交量范围
        if (klines.length > 0) {
            const volumes = klines.map(k => {
                let vol = k.volume || 0;
                if (typeof vol === 'string') {
                    vol = parseFloat(vol.replace(/[^\d.]/g, '')) || 0;
                }
                return vol;
            }).filter(v => v > 0);

            if (volumes.length > 0) {
                ranges.volume = {
                    min: Math.min(...volumes),
                    max: Math.max(...volumes)
                };
            }
        }

        return ranges;
    }

    renderSubChart(indicator, klineData, indicatorData, container, heightConfig) {
        console.log(`🖼️ 开始渲染子图表: ${indicator}`);

        const contentDiv = container.querySelector('.sub-chart-content');
        if (!contentDiv) {
            console.error('❌ 子图表内容容器未找到');
            return;
        }

        console.log('✅ 找到内容容器');

        // 强制刷新容器尺寸
        contentDiv.style.display = 'block';

        // 等待一帧后获取尺寸
        requestAnimationFrame(() => {
            // 直接使用主图Canvas的实际宽度，确保完全一致
            const mainCanvas = document.getElementById('main-kline-chart');
            let containerWidth;

            if (mainCanvas) {
                containerWidth = mainCanvas.width;
                console.log(`🎯 使用主图Canvas宽度: ${containerWidth}`);
            } else {
                // 如果主图Canvas不存在，使用主图容器宽度
                const mainContainer = document.querySelector('.main-chart-container');
                containerWidth = mainContainer ? mainContainer.clientWidth : (contentDiv.clientWidth || 800);
                console.log(`📏 使用容器宽度: ${containerWidth}`);
            }

            const containerHeight = Math.max(contentDiv.clientHeight, heightConfig.contentHeight);

            console.log(`📏 ${indicator} 子图表尺寸: ${containerWidth}x${containerHeight}`);
            console.log(`📐 配置高度: 总高度${heightConfig.individualHeight}px, 内容高度${heightConfig.contentHeight}px`);

            contentDiv.innerHTML = `<canvas id="${indicator}-canvas" width="${containerWidth}" height="${containerHeight}" style="width: 100%; height: 100%; display: block;"></canvas>`;
            const canvas = contentDiv.querySelector('canvas');

            if (!canvas) {
                console.error('❌ Canvas创建失败');
                return;
            }

            console.log('✅ Canvas创建成功');

            const ctx = canvas.getContext('2d');

            // 设置背景
            ctx.fillStyle = '#0f1419';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 使用统一的标准padding，确保与主图完全对齐
            const padding = this.getStandardPadding();
            const chartWidth = canvas.width - padding.left - padding.right;
            const canvasChartHeight = canvas.height - padding.top - padding.bottom;

            console.log(`📊 ${indicator} 绘制区域: ${chartWidth}x${canvasChartHeight}, padding:`, padding);

            console.log(`📊 ${indicator} 绘制区域: ${chartWidth}x${canvasChartHeight}`);

            try {
                if (indicator === 'volume') {
                    console.log('📈 绘制成交量图表');
                    this.drawVolumeSubChart(ctx, klineData.klines, padding, chartWidth, canvasChartHeight);
                } else {
                    console.log(`📊 绘制技术指标图表: ${indicator}`);
                    this.drawTechnicalSubChart(ctx, indicator, klineData, indicatorData, padding, chartWidth, canvasChartHeight);
                }
                console.log(`✅ ${indicator} 子图表渲染完成`);
                this.checkAlignment();
            } catch (error) {
                console.error(`❌ 渲染 ${indicator} 子图表失败:`, error);

                // 显示错误信息
                ctx.fillStyle = '#ff4757';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(`${indicator} 渲染失败`, canvas.width/2, canvas.height/2);
                ctx.fillStyle = '#d1d4dc';
                ctx.font = '12px Arial';
                ctx.fillText(error.message, canvas.width/2, canvas.height/2 + 20);
            }
        });
    }

    drawVolumeSubChart(ctx, klines, padding, chartWidth, chartHeight) {
        // 使用统一的数据切片，确保与主图K线完全对应
        const recentKlines = this.getStandardKlineData(klines);
        const candleWidth = Math.max(2, chartWidth / recentKlines.length * 0.6);

        // 计算成交量范围，确保数据有效
        const volumes = recentKlines.map(k => {
            let vol = k.volume || 0;
            // 如果成交量是字符串，尝试转换为数字
            if (typeof vol === 'string') {
                vol = parseFloat(vol.replace(/[^\d.]/g, '')) || 0;
            }
            return vol;
        });

        const maxVolume = Math.max(...volumes);
        const minVolume = 0;
        const volumeRange = maxVolume - minVolume || 1;

        console.log('成交量范围:', { maxVolume, minVolume, volumeRange });

        // candleWidth已在上面计算

        recentKlines.forEach((kline, index) => {
            let volume = kline.volume || 0;
            if (typeof volume === 'string') {
                volume = parseFloat(volume.replace(/[^\d.]/g, '')) || 0;
            }

            // 使用统一的x轴坐标计算，确保与主图K线完全对齐
            const x = this.calculateXPosition(index, recentKlines.length, padding, chartWidth);
            const barHeight = (volume / volumeRange) * chartHeight * 0.95; // 使用95%的高度，充分利用增加的空间
            const barY = padding.top + chartHeight - barHeight;

            const isUp = kline.close >= kline.open;
            ctx.fillStyle = isUp ? '#ff4757' : '#2ed573';

            // 绘制柱子，确保最小高度，使用与K线相同的宽度
            const minBarHeight = 1;
            const actualBarHeight = Math.max(minBarHeight, barHeight);
            ctx.fillRect(x - candleWidth/2, barY, candleWidth, actualBarHeight);

            // 专业级对齐验证：在首尾位置绘制对齐参考线
            if (index === 0 || index === recentKlines.length - 1) {
                this.drawAlignmentReference(ctx, x, index, 'volume-chart', '#00ff00');
            }
        });

        // 绘制背景网格
        ctx.strokeStyle = '#2b3139';
        ctx.lineWidth = 0.5;
        ctx.setLineDash([1, 1]);
        for (let i = 1; i < 4; i++) {
            const y = padding.top + (chartHeight / 4) * i;
            ctx.beginPath();
            ctx.moveTo(padding.left, y);
            ctx.lineTo(padding.left + chartWidth, y);
            ctx.stroke();
        }
        ctx.setLineDash([]);

        // 绘制成交量标签
        ctx.fillStyle = '#d1d4dc';
        ctx.font = '10px Arial';
        ctx.textAlign = 'right';

        // 格式化成交量显示
        let maxVolumeText;
        if (maxVolume >= 100000000) {
            maxVolumeText = (maxVolume / 100000000).toFixed(1) + '亿';
        } else if (maxVolume >= 10000) {
            maxVolumeText = (maxVolume / 10000).toFixed(1) + '万';
        } else {
            maxVolumeText = maxVolume.toFixed(0);
        }

        ctx.fillText(maxVolumeText, padding.left - 5, padding.top + 15);
        ctx.fillText('0', padding.left - 5, padding.top + chartHeight - 5);

        // 绘制成交量图例
        ctx.textAlign = 'left';
        ctx.font = '10px Arial';
        ctx.fillStyle = '#ff4757';
        ctx.fillText('涨', padding.left + 10, padding.top + 15);
        ctx.fillStyle = '#2ed573';
        ctx.fillText('跌', padding.left + 35, padding.top + 15);
    }

    drawTechnicalSubChart(ctx, indicator, klineData, indicatorData, padding, chartWidth, chartHeight) {
        console.log(`🎯 绘制技术指标子图: ${indicator}`);
        console.log('📊 指标数据结构:', indicatorData);
        console.log('📊 指标数据完整内容:', JSON.stringify(indicatorData, null, 2));

        if (indicatorData && indicatorData.indicators) {
            console.log(`✅ 找到indicators对象:`, Object.keys(indicatorData.indicators));
            console.log(`🔍 ${indicator}数据:`, indicatorData.indicators[indicator]);
            console.log(`🔍 ${indicator}数据详细:`, JSON.stringify(indicatorData.indicators[indicator], null, 2));
        }

        if (!indicatorData || !indicatorData.indicators) {
            console.warn(`❌ ${indicator} 技术指标数据缺失`);
            console.log('当前indicatorData:', indicatorData);

            ctx.fillStyle = '#ff4757';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(`${indicator.toUpperCase()} 数据加载中...`, padding.left + chartWidth/2, padding.top + chartHeight/2);

            ctx.fillStyle = '#d1d4dc';
            ctx.font = '12px Arial';
            ctx.fillText('请检查数据源连接', padding.left + chartWidth/2, padding.top + chartHeight/2 + 20);
            return;
        }

        // 检查特定指标数据是否存在
        if (!indicatorData.indicators[indicator]) {
            console.warn(`❌ ${indicator} 指标数据不存在`);
            console.log('可用指标:', Object.keys(indicatorData.indicators));

            ctx.fillStyle = '#ff4757';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(`${indicator.toUpperCase()} 数据不可用`, padding.left + chartWidth/2, padding.top + chartHeight/2);

            ctx.fillStyle = '#d1d4dc';
            ctx.font = '12px Arial';
            ctx.fillText('可能需要等待数据加载', padding.left + chartWidth/2, padding.top + chartHeight/2 + 20);
            return;
        }

        const klines = this.getStandardKlineData(klineData.klines); // 使用统一的数据切片，确保与主图K线完全对应

        switch (indicator) {
            case 'macd':
                console.log('🔵 开始绘制MACD');
                const macdData = indicatorData.indicators.macd;
                console.log('MACD数据详情:', macdData);
                this.drawMACDSubChart(ctx, klines, macdData, padding, chartWidth, chartHeight);
                break;
            case 'kdj':
                console.log('🟡 开始绘制KDJ');
                const kdjData = indicatorData.indicators.kdj;
                console.log('KDJ数据详情:', kdjData);
                this.drawKDJSubChart(ctx, klines, kdjData, padding, chartWidth, chartHeight);
                break;
            case 'rsi':
                console.log('🟢 开始绘制RSI');
                const rsiData = indicatorData.indicators.rsi;
                console.log('RSI数据详情:', rsiData);
                this.drawRSISubChart(ctx, klines, rsiData, padding, chartWidth, chartHeight);
                break;
            default:
                console.log(`⚠️ 未知指标: ${indicator}`);
                ctx.fillStyle = '#d1d4dc';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(`${indicator.toUpperCase()} 指标`, padding.left + chartWidth/2, padding.top + chartHeight/2);
        }
    }

    drawMACDSubChart(ctx, klines, macdData, padding, chartWidth, chartHeight) {
        console.log('🔵 MACD绘制开始');
        console.log('MACD数据:', macdData);

        if (!macdData) {
            console.error('❌ MACD数据为空');
            ctx.fillStyle = '#ff4757';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('MACD数据为空', padding.left + chartWidth/2, padding.top + chartHeight/2);
            return;
        }

        if (!macdData.macd) {
            console.error('❌ MACD数据中没有macd字段');
            console.log('可用字段:', Object.keys(macdData));
            ctx.fillStyle = '#ff4757';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('MACD数据格式错误', padding.left + chartWidth/2, padding.top + chartHeight/2);
            return;
        }

        const macdValues = macdData.macd.slice(-klines.length);
        console.log(`MACD值数量: ${macdValues.length}`);
        console.log('MACD前5个值:', macdValues.slice(0, 5));

        // 收集所有相关数值用于计算范围
        let allValues = [];

        // 添加MACD柱状图数值
        const validMacdValues = macdValues.filter(v => v !== null && v !== undefined);
        allValues = allValues.concat(validMacdValues);

        // 添加DIF和DEA线数值
        if (macdData.dif) {
            const validDifValues = macdData.dif.slice(-klines.length).filter(v => v !== null && v !== undefined);
            allValues = allValues.concat(validDifValues);
        }
        if (macdData.dea) {
            const validDeaValues = macdData.dea.slice(-klines.length).filter(v => v !== null && v !== undefined);
            allValues = allValues.concat(validDeaValues);
        }

        console.log(`有效MACD相关值数量: ${allValues.length}`);

        if (allValues.length === 0) {
            console.error('❌ 没有有效的MACD值');
            ctx.fillStyle = '#ff4757';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('MACD数据无效', padding.left + chartWidth/2, padding.top + chartHeight/2);
            return;
        }

        // 计算合理的显示范围，添加15%的缓冲区确保完全显示
        const rawMaxValue = Math.max(...allValues);
        const rawMinValue = Math.min(...allValues);
        const rawRange = rawMaxValue - rawMinValue || 0.01;
        const buffer = rawRange * 0.15; // 15%缓冲区

        const maxValue = rawMaxValue + buffer;
        const minValue = rawMinValue - buffer;
        const range = maxValue - minValue;

        // 绘制背景网格
        ctx.strokeStyle = '#2b3139';
        ctx.lineWidth = 0.5;
        ctx.setLineDash([1, 1]);
        for (let i = 1; i < 5; i++) {
            const y = padding.top + (chartHeight / 5) * i;
            ctx.beginPath();
            ctx.moveTo(padding.left, y);
            ctx.lineTo(padding.left + chartWidth, y);
            ctx.stroke();
        }
        ctx.setLineDash([]);

        // 计算零轴位置，确保在合理范围内
        const zeroY = padding.top + chartHeight - ((-minValue) / range) * chartHeight;

        // 绘制零轴线（加强显示）
        if (rawMinValue <= 0 && rawMaxValue >= 0) {
            ctx.strokeStyle = '#666666';
            ctx.lineWidth = 1.5;
            ctx.beginPath();
            ctx.moveTo(padding.left, zeroY);
            ctx.lineTo(padding.left + chartWidth, zeroY);
            ctx.stroke();
        }

        // 绘制MACD柱状图，与主图K线对齐
        const candleWidth = Math.max(2, chartWidth / klines.length * 0.6);

        macdValues.forEach((value, index) => {
            if (value !== null && value !== undefined) {
                // 使用统一的x轴坐标计算，确保与主图K线完全对齐
                const x = this.calculateXPosition(index, klines.length, padding, chartWidth);
                // 使用完整范围计算高度，确保充分利用显示空间
                const normalizedValue = (value - minValue) / range;
                const barHeight = Math.abs(value - 0) / range * chartHeight * 0.9; // 使用90%的高度
                const barY = value >= 0 ?
                    padding.top + chartHeight - (normalizedValue * chartHeight) :
                    padding.top + chartHeight - (normalizedValue * chartHeight);

                ctx.fillStyle = value >= 0 ? '#ff4757' : '#2ed573';
                ctx.fillRect(x - candleWidth/2, barY, candleWidth, Math.max(1, barHeight));
            }
        });

        // 绘制DIF和DEA线（专业级显示，线条更粗更清晰）
        if (macdData.dif && macdData.dea) {
            // DIF线 (黄色) - 增加线条粗细以适应更大的显示空间
            this.drawIndicatorLine(ctx, klines, macdData.dif, padding, chartWidth, chartHeight, 0, '#ffc107', minValue, range, 2.5);
            // DEA线 (蓝色) - 增加线条粗细以适应更大的显示空间
            this.drawIndicatorLine(ctx, klines, macdData.dea, padding, chartWidth, chartHeight, 0, '#2196f3', minValue, range, 2.5);
        }

        // 绘制数值标签，显示实际的最大最小值
        ctx.fillStyle = '#d1d4dc';
        ctx.font = '10px Arial';
        ctx.textAlign = 'right';
        ctx.fillText(rawMaxValue.toFixed(3), padding.left - 5, padding.top + 12);
        if (rawMinValue <= 0 && rawMaxValue >= 0) {
            ctx.fillText('0', padding.left - 5, zeroY + 4);
        }
        ctx.fillText(rawMinValue.toFixed(3), padding.left - 5, padding.top + chartHeight - 2);

        // 绘制图例
        ctx.textAlign = 'left';
        ctx.font = '10px Arial';
        let legendX = padding.left + 10;

        ctx.fillStyle = '#ffc107';
        ctx.fillText('DIF', legendX, padding.top + 15);
        legendX += 35;

        ctx.fillStyle = '#2196f3';
        ctx.fillText('DEA', legendX, padding.top + 15);
        legendX += 35;

        ctx.fillStyle = '#ff4757';
        ctx.fillText('MACD+', legendX, padding.top + 15);
        legendX += 50;

        ctx.fillStyle = '#2ed573';
        ctx.fillText('MACD-', legendX, padding.top + 15);
    }

    drawKDJSubChart(ctx, klines, kdjData, padding, chartWidth, chartHeight) {
        console.log('🟡 KDJ绘制开始');
        console.log('KDJ数据:', kdjData);

        if (!kdjData) {
            console.error('❌ KDJ数据为空');
            ctx.fillStyle = '#ff4757';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('KDJ数据为空', padding.left + chartWidth/2, padding.top + chartHeight/2);
            return;
        }

        console.log('KDJ可用字段:', Object.keys(kdjData));

        // 检查是否有K、D、J数据
        const hasK = kdjData.k && kdjData.k.length > 0;
        const hasD = kdjData.d && kdjData.d.length > 0;
        const hasJ = kdjData.j && kdjData.j.length > 0;

        console.log(`KDJ数据检查: K=${hasK}, D=${hasD}, J=${hasJ}`);

        if (!hasK && !hasD && !hasJ) {
            console.error('❌ KDJ数据中没有K、D、J字段');
            ctx.fillStyle = '#ff4757';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('KDJ数据格式错误', padding.left + chartWidth/2, padding.top + chartHeight/2);
            return;
        }

        // 收集所有KDJ数值用于计算实际范围
        let allValues = [];

        if (kdjData.k) {
            const validKValues = kdjData.k.slice(-klines.length).filter(v => v !== null && v !== undefined && !isNaN(v));
            allValues = allValues.concat(validKValues);
        }
        if (kdjData.d) {
            const validDValues = kdjData.d.slice(-klines.length).filter(v => v !== null && v !== undefined && !isNaN(v));
            allValues = allValues.concat(validDValues);
        }
        if (kdjData.j) {
            const validJValues = kdjData.j.slice(-klines.length).filter(v => v !== null && v !== undefined && !isNaN(v));
            allValues = allValues.concat(validJValues);
        }

        console.log(`有效KDJ值数量: ${allValues.length}`);
        console.log('KDJ值范围预览:', allValues.slice(0, 10));

        if (allValues.length === 0) {
            console.error('❌ 没有有效的KDJ值');
            ctx.fillStyle = '#ff4757';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('KDJ数据无效', padding.left + chartWidth/2, padding.top + chartHeight/2);
            return;
        }

        // 计算实际的显示范围，添加10%的缓冲区
        const rawMaxValue = Math.max(...allValues);
        const rawMinValue = Math.min(...allValues);
        const rawRange = rawMaxValue - rawMinValue || 1;
        const buffer = rawRange * 0.1; // 10%缓冲区

        const maxValue = rawMaxValue + buffer;
        const minValue = rawMinValue - buffer;
        const range = maxValue - minValue;

        console.log(`KDJ显示范围: ${minValue.toFixed(2)} - ${maxValue.toFixed(2)} (原始: ${rawMinValue.toFixed(2)} - ${rawMaxValue.toFixed(2)})`);

        // 绘制背景网格
        ctx.strokeStyle = '#2b3139';
        ctx.lineWidth = 0.5;
        ctx.setLineDash([1, 1]);
        for (let i = 1; i < 5; i++) {
            const y = padding.top + (chartHeight / 5) * i;
            ctx.beginPath();
            ctx.moveTo(padding.left, y);
            ctx.lineTo(padding.left + chartWidth, y);
            ctx.stroke();
        }
        ctx.setLineDash([]);

        // 绘制参考线（如果在显示范围内）
        ctx.strokeStyle = '#666666';
        ctx.lineWidth = 1;
        ctx.setLineDash([3, 3]);

        // 只在传统的20、50、80值在当前显示范围内时绘制参考线
        const referenceLines = [
            { value: 20, label: '20' },
            { value: 50, label: '50' },
            { value: 80, label: '80' }
        ];

        const drawnReferenceLines = [];
        referenceLines.forEach(ref => {
            if (ref.value >= minValue && ref.value <= maxValue) {
                const normalizedValue = (ref.value - minValue) / range;
                const y = padding.top + chartHeight - (normalizedValue * chartHeight);

                ctx.beginPath();
                ctx.moveTo(padding.left, y);
                ctx.lineTo(padding.left + chartWidth, y);
                ctx.stroke();

                drawnReferenceLines.push({ ...ref, y });
            }
        });

        ctx.setLineDash([]);

        // 绘制KDJ线条，使用实际计算的范围
        const colors = { k: '#ff9800', d: '#2196f3', j: '#4caf50' };
        const lineNames = { k: 'K', d: 'D', j: 'J' };

        Object.entries(kdjData).forEach(([line, values]) => {
            if (values && colors[line]) {
                // 专业级线条显示，适应更大的显示空间
                this.drawIndicatorLine(ctx, klines, values, padding, chartWidth, chartHeight, 0, colors[line], minValue, range, 3.0);
            }
        });

        // 绘制数值标签，显示实际的最大最小值
        ctx.fillStyle = '#d1d4dc';
        ctx.font = '10px Arial';
        ctx.textAlign = 'right';
        ctx.fillText(rawMaxValue.toFixed(1), padding.left - 5, padding.top + 12);

        // 绘制参考线标签
        drawnReferenceLines.forEach(ref => {
            ctx.fillText(ref.label, padding.left - 5, ref.y + 4);
        });

        ctx.fillText(rawMinValue.toFixed(1), padding.left - 5, padding.top + chartHeight - 2);

        // 绘制图例，增加间距
        ctx.textAlign = 'left';
        ctx.font = '10px Arial';
        let legendX = padding.left + 10;
        Object.entries(colors).forEach(([line, color]) => {
            if (kdjData[line]) { // 只显示有数据的线条
                ctx.fillStyle = color;
                ctx.fillText(lineNames[line], legendX, padding.top + 15);
                legendX += 30; // 增加间距
            }
        });

        // 显示当前显示范围信息
        ctx.fillStyle = '#8b949e';
        ctx.font = '9px Arial';
        ctx.textAlign = 'right';
        ctx.fillText(`范围: ${rawMinValue.toFixed(1)}-${rawMaxValue.toFixed(1)}`, padding.left + chartWidth, padding.top + chartHeight - 5);
    }

    drawRSISubChart(ctx, klines, rsiData, padding, chartWidth, chartHeight) {
        if (!rsiData) return;

        // 收集所有RSI数值用于计算实际范围
        let allValues = [];

        Object.entries(rsiData).forEach(([line, values]) => {
            if (values) {
                const validValues = values.slice(-klines.length).filter(v => v !== null && v !== undefined && !isNaN(v));
                allValues = allValues.concat(validValues);
            }
        });

        console.log(`有效RSI值数量: ${allValues.length}`);

        if (allValues.length === 0) {
            ctx.fillStyle = '#ff4757';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('RSI数据无效', padding.left + chartWidth/2, padding.top + chartHeight/2);
            return;
        }

        // 计算实际的显示范围，但确保不小于0-100的合理范围
        const rawMaxValue = Math.max(...allValues);
        const rawMinValue = Math.min(...allValues);

        // RSI通常在0-100范围内，但给予一定的扩展空间
        const theoreticalMin = 0;
        const theoreticalMax = 100;

        // 使用实际范围，但确保包含重要的参考线
        const displayMin = Math.min(rawMinValue - 5, theoreticalMin);
        const displayMax = Math.max(rawMaxValue + 5, theoreticalMax);

        const minValue = displayMin;
        const maxValue = displayMax;
        const range = maxValue - minValue;

        console.log(`RSI显示范围: ${minValue.toFixed(1)} - ${maxValue.toFixed(1)} (实际: ${rawMinValue.toFixed(1)} - ${rawMaxValue.toFixed(1)})`);

        // 绘制背景网格
        ctx.strokeStyle = '#2b3139';
        ctx.lineWidth = 0.5;
        ctx.setLineDash([1, 1]);
        for (let i = 1; i < 5; i++) {
            const y = padding.top + (chartHeight / 5) * i;
            ctx.beginPath();
            ctx.moveTo(padding.left, y);
            ctx.lineTo(padding.left + chartWidth, y);
            ctx.stroke();
        }
        ctx.setLineDash([]);

        // 绘制参考线（如果在显示范围内）
        ctx.strokeStyle = '#666666';
        ctx.lineWidth = 1;
        ctx.setLineDash([3, 3]);

        const referenceLines = [
            { value: 30, label: '30' },
            { value: 50, label: '50' },
            { value: 70, label: '70' }
        ];

        const drawnReferenceLines = [];
        referenceLines.forEach(ref => {
            if (ref.value >= minValue && ref.value <= maxValue) {
                const normalizedValue = (ref.value - minValue) / range;
                const y = padding.top + chartHeight - (normalizedValue * chartHeight);

                ctx.beginPath();
                ctx.moveTo(padding.left, y);
                ctx.lineTo(padding.left + chartWidth, y);
                ctx.stroke();

                drawnReferenceLines.push({ ...ref, y });
            }
        });

        ctx.setLineDash([]);

        // 绘制RSI线条，使用实际计算的范围
        const colors = { rsi6: '#ff9800', rsi12: '#2196f3', rsi24: '#4caf50' };
        const lineNames = { rsi6: 'RSI6', rsi12: 'RSI12', rsi24: 'RSI24' };

        Object.entries(rsiData).forEach(([line, values]) => {
            if (values && colors[line]) {
                // 专业级线条显示，适应更大的显示空间
                this.drawIndicatorLine(ctx, klines, values, padding, chartWidth, chartHeight, 0, colors[line], minValue, range, 3.0);
            }
        });

        // 绘制数值标签，显示实际的最大最小值和参考线
        ctx.fillStyle = '#d1d4dc';
        ctx.font = '10px Arial';
        ctx.textAlign = 'right';
        ctx.fillText(rawMaxValue.toFixed(1), padding.left - 5, padding.top + 12);

        // 绘制参考线标签
        drawnReferenceLines.forEach(ref => {
            ctx.fillText(ref.label, padding.left - 5, ref.y + 4);
        });

        ctx.fillText(rawMinValue.toFixed(1), padding.left - 5, padding.top + chartHeight - 2);

        // 绘制图例，增加间距
        ctx.textAlign = 'left';
        ctx.font = '10px Arial';
        let legendX = padding.left + 10;
        Object.entries(colors).forEach(([line, color]) => {
            if (rsiData[line]) { // 只显示有数据的线条
                ctx.fillStyle = color;
                ctx.fillText(lineNames[line], legendX, padding.top + 15);
                legendX += 55; // 增加间距以避免重叠
            }
        });

        // 显示当前显示范围信息
        ctx.fillStyle = '#8b949e';
        ctx.font = '9px Arial';
        ctx.textAlign = 'right';
        ctx.fillText(`范围: ${rawMinValue.toFixed(1)}-${rawMaxValue.toFixed(1)}`, padding.left + chartWidth, padding.top + chartHeight - 5);
    }

    drawIndicatorLine(ctx, klines, values, padding, chartWidth, chartHeight, yOffset, color, minValue, range, lineWidth = 1.5) {
        if (!values || values.length === 0) return;

        const lineValues = values.slice(-klines.length);

        ctx.strokeStyle = color;
        ctx.lineWidth = lineWidth;
        ctx.beginPath();

        let hasValidPoint = false;
        const totalPoints = klines.length;

        lineValues.forEach((value, index) => {
            if (value !== null && value !== undefined && !isNaN(value)) {
                // 使用统一的x轴坐标计算，确保与主图K线完全对齐
                const x = this.calculateXPosition(index, totalPoints, padding, chartWidth);
                const normalizedValue = Math.max(0, Math.min(1, (value - minValue) / range));
                const y = yOffset + padding.top + chartHeight - (normalizedValue * chartHeight);

                if (!hasValidPoint) {
                    ctx.moveTo(x, y);
                    hasValidPoint = true;
                } else {
                    ctx.lineTo(x, y);
                }

                // 专业级对齐验证：在首尾位置绘制对齐参考线
                if (index === 0 || index === totalPoints - 1) {
                    this.drawAlignmentReference(ctx, x, index, 'technical-indicator', '#0000ff');
                }
            }
        });

        if (hasValidPoint) {
            ctx.stroke();
        }
    }

    showMessage(message, type = 'info') {
        console.log(`[${type.toUpperCase()}] ${message}`);

        // 可以在这里添加用户界面提示
        const container = document.getElementById('price-chart');
        if (container && type === 'error') {
            container.innerHTML = `
                <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 400px; color: var(--danger-color);">
                    <div style="font-size: 24px; margin-bottom: 10px;">⚠️</div>
                    <div>${message}</div>
                    <button onclick="stockAnalyzer.updateChart('${this.currentPeriod}')" style="margin-top: 15px; padding: 8px 16px; background: var(--primary-color); color: white; border: none; border-radius: 4px; cursor: pointer;">重试</button>
                </div>
            `;
        }
    }

    renderProfessionalChart(klineData, indicatorData) {
        console.log('开始渲染专业图表');
        // 支持两种容器：原版和专业版
        const container = document.getElementById('price-chart') || document.getElementById('main-chart');

        if (!container) {
            console.error('图表容器未找到 - 检查了 price-chart 和 main-chart');
            return;
        }

        console.log('找到图表容器:', container.id);

        console.log('K线数据检查:', klineData);
        console.log('技术指标数据检查:', indicatorData);

        if (!klineData || !klineData.klines || klineData.klines.length === 0) {
            console.error('K线数据无效');
            this.showMessage('K线数据无效', 'error');
            return;
        }

        container.innerHTML = ''; // 清空容器

        if (this.chart) {
            try {
                this.chart.remove();
            } catch (e) {
                console.warn('移除旧图表失败:', e);
            }
        }

        // 保存数据用于重绘
        this.lastKlineData = klineData;
        this.lastIndicatorData = indicatorData;

        // 使用专业Canvas图表
        console.log('使用专业Canvas图表，K线数据条数:', klineData.klines ? klineData.klines.length : 0);

        // 直接调用简化的渲染方法
        this.renderSimpleChart(klineData, indicatorData);
    }

    renderSimpleChart(klineData, indicatorData) {
        console.log('渲染简化图表');
        const container = document.getElementById('price-chart');

        if (!container) {
            console.error('图表容器未找到');
            return;
        }

        if (!klineData || !klineData.klines || klineData.klines.length === 0) {
            console.warn('没有K线数据');
            container.innerHTML = `
                <div style="display: flex; align-items: center; justify-content: center; height: 400px; color: var(--text-muted);">
                    <div style="text-align: center;">
                        <div style="font-size: 48px; margin-bottom: 20px;">📊</div>
                        <div>暂无图表数据</div>
                    </div>
                </div>
            `;
            return;
        }

        // 创建Canvas
        const containerWidth = container.clientWidth || 800;
        const containerHeight = 400;

        container.innerHTML = `<canvas id="main-chart" width="${containerWidth}" height="${containerHeight}" style="width: 100%; height: 100%;"></canvas>`;
        const canvas = document.getElementById('main-chart');
        const ctx = canvas.getContext('2d');

        try {
            this.drawBasicChart(ctx, canvas, klineData);

            // 添加鼠标交互
            this.addSimpleMouseInteraction(canvas, klineData.klines);

            console.log('简化图表渲染完成');
        } catch (error) {
            console.error('简化图表渲染失败:', error);
            container.innerHTML = `
                <div style="display: flex; align-items: center; justify-content: center; height: 400px; color: var(--danger-color);">
                    <div style="text-align: center;">
                        <div style="font-size: 24px; margin-bottom: 10px;">⚠️</div>
                        <div>图表渲染失败</div>
                        <div style="font-size: 12px; margin-top: 10px;">${error.message}</div>
                    </div>
                </div>
            `;
        }
    }

    drawBasicChart(ctx, canvas, klineData) {
        const klines = this.getStandardKlineData(klineData.klines); // 使用统一的数据切片
        const padding = this.getStandardPadding();
        const chartWidth = canvas.width - padding.left - padding.right;
        const chartHeight = canvas.height - padding.top - padding.bottom;

        // 计算价格范围
        const prices = klines.map(k => [k.high, k.low, k.open, k.close]).flat();
        const maxPrice = Math.max(...prices);
        const minPrice = Math.min(...prices);
        const priceRange = maxPrice - minPrice || 1;
        const priceBuffer = priceRange * 0.1;

        const adjustedMax = maxPrice + priceBuffer;
        const adjustedMin = minPrice - priceBuffer;
        const adjustedRange = adjustedMax - adjustedMin;

        // 背景
        ctx.fillStyle = '#1e2329';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // 简单网格
        ctx.strokeStyle = '#2b3139';
        ctx.lineWidth = 1;

        // 垂直线
        for (let i = 0; i <= 5; i++) {
            const x = padding.left + (chartWidth / 5) * i;
            ctx.beginPath();
            ctx.moveTo(x, padding.top);
            ctx.lineTo(x, padding.top + chartHeight);
            ctx.stroke();
        }

        // 水平线
        for (let i = 0; i <= 4; i++) {
            const y = padding.top + (chartHeight / 4) * i;
            ctx.beginPath();
            ctx.moveTo(padding.left, y);
            ctx.lineTo(padding.left + chartWidth, y);
            ctx.stroke();
        }

        // K线
        const candleWidth = Math.max(6, chartWidth / klines.length * 0.8);

        klines.forEach((kline, index) => {
            // 使用统一的x轴坐标计算，确保与子图完全对齐
            const x = this.calculateXPosition(index, klines.length, padding, chartWidth);
            const openY = padding.top + chartHeight - ((kline.open - adjustedMin) / adjustedRange) * chartHeight;
            const closeY = padding.top + chartHeight - ((kline.close - adjustedMin) / adjustedRange) * chartHeight;
            const highY = padding.top + chartHeight - ((kline.high - adjustedMin) / adjustedRange) * chartHeight;
            const lowY = padding.top + chartHeight - ((kline.low - adjustedMin) / adjustedRange) * chartHeight;

            const isUp = kline.close >= kline.open;
            const color = isUp ? '#ff4757' : '#2ed573';

            // 影线
            ctx.strokeStyle = color;
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(x, highY);
            ctx.lineTo(x, lowY);
            ctx.stroke();

            // 实体
            ctx.fillStyle = color;
            const bodyTop = Math.min(openY, closeY);
            const bodyHeight = Math.max(2, Math.abs(closeY - openY));
            ctx.fillRect(x - candleWidth/2, bodyTop, candleWidth, bodyHeight);
        });

        // 价格标签
        ctx.fillStyle = '#d1d4dc';
        ctx.font = '11px Arial';
        ctx.textAlign = 'right';

        for (let i = 0; i <= 4; i++) {
            const price = adjustedMin + adjustedRange * (1 - i / 4);
            const y = padding.top + (chartHeight / 4) * i;
            ctx.fillText(price.toFixed(2), padding.left - 10, y + 4);
        }

        // 简单图例
        const latestKline = klines[klines.length - 1];
        ctx.fillStyle = '#d1d4dc';
        ctx.font = '12px Arial';
        ctx.textAlign = 'left';

        const legendY = 20;
        ctx.fillText(`开: ${latestKline.open}`, 20, legendY);
        ctx.fillText(`高: ${latestKline.high}`, 100, legendY);
        ctx.fillText(`低: ${latestKline.low}`, 180, legendY);
        ctx.fillText(`收: ${latestKline.close}`, 260, legendY);
    }

    addSimpleMouseInteraction(canvas, klines) {
        console.log('添加鼠标交互');

        // 创建信息显示面板
        this.createSimpleInfoPanel(canvas.parentNode);

        const padding = this.getStandardPadding();
        const chartWidth = canvas.width - padding.left - padding.right;
        const chartHeight = canvas.height - padding.top - padding.bottom;

        // 计算价格范围
        const prices = klines.map(k => [k.high, k.low, k.open, k.close]).flat();
        const maxPrice = Math.max(...prices);
        const minPrice = Math.min(...prices);
        const priceRange = maxPrice - minPrice || 1;
        const priceBuffer = priceRange * 0.1;
        const adjustedMin = minPrice - priceBuffer;
        const adjustedRange = maxPrice + priceBuffer * 2 - adjustedMin;

        canvas.addEventListener('mousemove', (e) => {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            // 转换为画布坐标
            const canvasX = x * (canvas.width / rect.width);
            const canvasY = y * (canvas.height / rect.height);

            // 检查是否在图表区域
            if (canvasX >= padding.left && canvasX <= padding.left + chartWidth &&
                canvasY >= padding.top && canvasY <= padding.top + chartHeight) {

                // 计算对应的K线索引
                const dataIndex = Math.round((canvasX - padding.left) / chartWidth * (klines.length - 1));

                if (dataIndex >= 0 && dataIndex < klines.length) {
                    this.showSimpleKlineInfo(klines[dataIndex]);
                    this.drawSimpleCrosshair(canvas, canvasX, canvasY, padding, chartWidth, chartHeight);
                }
            }
        });

        canvas.addEventListener('mouseleave', () => {
            this.hideSimpleKlineInfo();
            this.clearSimpleCrosshair(canvas);
        });
    }

    createSimpleInfoPanel(container) {
        // 移除旧的信息面板
        const oldPanel = container.querySelector('.simple-info-panel');
        if (oldPanel) {
            oldPanel.remove();
        }

        const infoPanel = document.createElement('div');
        infoPanel.className = 'simple-info-panel';
        infoPanel.style.cssText = `
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(30, 35, 41, 0.95);
            border: 1px solid #2b3139;
            border-radius: 8px;
            padding: 12px;
            color: #d1d4dc;
            font-size: 12px;
            font-family: monospace;
            display: none;
            z-index: 1000;
            min-width: 180px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        `;
        container.style.position = 'relative';
        container.appendChild(infoPanel);
    }

    showSimpleKlineInfo(kline) {
        const panel = document.querySelector('.simple-info-panel');
        if (!panel) return;

        const change = kline.close - kline.open;
        const changePercent = (change / kline.open * 100).toFixed(2);
        const isUp = change >= 0;
        const changeColor = isUp ? '#ff4757' : '#2ed573';

        panel.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 8px; color: #ffffff; border-bottom: 1px solid #2b3139; padding-bottom: 4px;">
                📅 ${kline.date}
            </div>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 6px; font-size: 11px;">
                <div>开盘: <span style="color: #ffffff;">${kline.open}</span></div>
                <div>收盘: <span style="color: ${changeColor}; font-weight: bold;">${kline.close}</span></div>
                <div>最高: <span style="color: #ffffff;">${kline.high}</span></div>
                <div>最低: <span style="color: #ffffff;">${kline.low}</span></div>
                <div>涨跌: <span style="color: ${changeColor};">${isUp ? '+' : ''}${change.toFixed(2)}</span></div>
                <div>涨幅: <span style="color: ${changeColor};">${isUp ? '+' : ''}${changePercent}%</span></div>
            </div>
            <div style="margin-top: 8px; padding-top: 6px; border-top: 1px solid #2b3139; font-size: 10px;">
                <div>成交量: <span style="color: #ffc107;">${this.formatVolume(kline.volume)}</span></div>
                <div>成交额: <span style="color: #ffc107;">${this.formatTurnover(kline.turnover)}</span></div>
            </div>
        `;

        panel.style.display = 'block';
    }

    hideSimpleKlineInfo() {
        const panel = document.querySelector('.simple-info-panel');
        if (panel) {
            panel.style.display = 'none';
        }
    }

    drawSimpleCrosshair(canvas, x, y, padding, chartWidth, chartHeight) {
        // 验证Canvas元素有效性
        if (!canvas || !canvas.getContext) {
            console.log('⚠️ 传入的canvas参数无效，跳过简单十字光标绘制');
            return;
        }

        // 使用叠加Canvas绘制十字光标，避免重绘主图
        const crosshairCanvas = this.getMainCrosshairCanvas(canvas);
        if (!crosshairCanvas) return;

        const ctx = crosshairCanvas.getContext('2d');

        // 清除之前的十字光标
        ctx.clearRect(0, 0, crosshairCanvas.width, crosshairCanvas.height);

        // 绘制十字线
        ctx.save();
        ctx.strokeStyle = '#ffc107';
        ctx.lineWidth = 1;
        ctx.setLineDash([3, 3]);

        // 垂直线
        ctx.beginPath();
        ctx.moveTo(x, padding.top);
        ctx.lineTo(x, padding.top + chartHeight);
        ctx.stroke();

        // 水平线
        ctx.beginPath();
        ctx.moveTo(padding.left, y);
        ctx.lineTo(padding.left + chartWidth, y);
        ctx.stroke();

        ctx.restore();
    }

    clearSimpleCrosshair(canvas) {
        // 验证Canvas元素有效性
        if (!canvas || !canvas.getContext) {
            console.log('⚠️ 传入的canvas参数无效，跳过简单十字光标清除');
            return;
        }

        // 清除叠加Canvas上的十字光标
        const crosshairCanvas = this.getMainCrosshairCanvas(canvas);
        if (crosshairCanvas && crosshairCanvas.getContext) {
            const ctx = crosshairCanvas.getContext('2d');
            ctx.clearRect(0, 0, crosshairCanvas.width, crosshairCanvas.height);
        }
    }

    renderFallbackChart(klineData, indicatorData) {
        console.log('使用专业备用图表渲染');
        const container = document.getElementById('price-chart');

        if (!container) {
            console.error('图表容器未找到');
            return;
        }

        // 创建响应式Canvas图表
        const containerWidth = container.clientWidth || 800;
        const containerHeight = 400;

        container.innerHTML = `<canvas id="fallback-chart" width="${containerWidth}" height="${containerHeight}" style="width: 100%; height: 100%;"></canvas>`;
        const canvas = document.getElementById('fallback-chart');

        if (!canvas) {
            console.error('Canvas元素创建失败');
            return;
        }

        const ctx = canvas.getContext('2d');

        if (!ctx) {
            console.error('Canvas上下文获取失败');
            return;
        }

        if (!klineData || !klineData.klines || klineData.klines.length === 0) {
            console.warn('K线数据为空');
            this.drawNoDataMessage(ctx, canvas);
            return;
        }

        console.log('开始绘制专业K线图，数据条数:', klineData.klines.length);

        try {
            // 绘制专业K线图
            this.drawProfessionalKlineChart(ctx, canvas, klineData, indicatorData);
            console.log('图表绘制完成');
        } catch (error) {
            console.error('图表绘制失败:', error);
            this.drawErrorMessage(ctx, canvas, error.message);
        }
    }

    drawErrorMessage(ctx, canvas, errorMsg) {
        ctx.fillStyle = '#1e2329';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        ctx.fillStyle = '#ff4757';
        ctx.font = '16px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('⚠️ 图表渲染错误', canvas.width / 2, canvas.height / 2 - 20);

        ctx.font = '12px Arial';
        ctx.fillStyle = '#d1d4dc';
        ctx.fillText(errorMsg, canvas.width / 2, canvas.height / 2 + 10);
    }

    drawNoDataMessage(ctx, canvas) {
        ctx.fillStyle = '#1e2329';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        ctx.fillStyle = '#d1d4dc';
        ctx.font = '18px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('📊 暂无图表数据', canvas.width / 2, canvas.height / 2 - 10);

        ctx.font = '14px Arial';
        ctx.fillStyle = '#888';
        ctx.fillText('请搜索股票代码查看K线图', canvas.width / 2, canvas.height / 2 + 20);
    }

    drawProfessionalKlineChart(ctx, canvas, klineData, indicatorData) {
        console.log('绘制专业K线图开始');

        const klines = this.getStandardKlineData(klineData.klines); // 使用统一的数据切片
        console.log('处理K线数据条数:', klines.length);

        if (klines.length === 0) {
            console.warn('没有K线数据可绘制');
            this.drawNoDataMessage(ctx, canvas);
            return;
        }

        // 使用统一的padding，确保与子图对齐
        const padding = this.getStandardPadding();
        const chartWidth = canvas.width - padding.left - padding.right;
        const chartHeight = canvas.height - padding.top - padding.bottom;

        console.log('图表尺寸:', { width: chartWidth, height: chartHeight });

        // 计算价格范围
        const prices = klines.map(k => [k.high, k.low, k.open, k.close]).flat();
        const maxPrice = Math.max(...prices);
        const minPrice = Math.min(...prices);
        const priceRange = maxPrice - minPrice || 1; // 防止除零
        const priceBuffer = priceRange * 0.1; // 10%缓冲区

        const adjustedMax = maxPrice + priceBuffer;
        const adjustedMin = minPrice - priceBuffer;
        const adjustedRange = adjustedMax - adjustedMin;

        console.log('价格范围:', { min: minPrice, max: maxPrice, range: priceRange });

        // 设置背景
        ctx.fillStyle = '#1e2329';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        try {
            // 绘制简化的主图
            this.drawSimpleMainChart(ctx, canvas, klines, indicatorData, padding, chartWidth, chartHeight, adjustedMin, adjustedRange);
            console.log('主图绘制完成');
        } catch (error) {
            console.error('主图绘制失败:', error);
            throw error;
        }
    }

    drawSimpleMainChart(ctx, canvas, klines, indicatorData, padding, chartWidth, chartHeight, adjustedMin, adjustedRange) {
        console.log('绘制简化主图');

        // 绘制网格
        this.drawGrid(ctx, padding, chartWidth, chartHeight);

        // 绘制K线
        this.drawCandlesticks(ctx, klines, padding, chartWidth, chartHeight, adjustedMin, adjustedRange);

        // 只绘制移动平均线，暂时跳过其他指标
        if (this.activeIndicators.includes('ma') && indicatorData && indicatorData.indicators && indicatorData.indicators.ma) {
            console.log('绘制移动平均线');
            this.drawMovingAveragesInMainChart(ctx, klines, indicatorData.indicators.ma, padding, chartWidth, chartHeight, adjustedMin, adjustedRange);
        }

        // 绘制坐标轴标签
        this.drawAxisLabels(ctx, canvas, klines, padding, chartWidth, chartHeight, adjustedMin, adjustedMin + adjustedRange);

        // 绘制图例
        this.drawMainLegend(ctx, canvas, klines[klines.length - 1], indicatorData);
    }

    drawMainChart(ctx, canvas, klines, indicatorData, padding, chartWidth, chartHeight, adjustedMin, adjustedRange) {
        // 绘制网格
        this.drawGrid(ctx, padding, chartWidth, chartHeight);

        // 绘制K线
        this.drawCandlesticks(ctx, klines, padding, chartWidth, chartHeight, adjustedMin, adjustedRange);

        // 绘制移动平均线
        if (this.activeIndicators.includes('ma') && indicatorData && indicatorData.indicators && indicatorData.indicators.ma) {
            this.drawMovingAveragesInMainChart(ctx, klines, indicatorData.indicators.ma, padding, chartWidth, chartHeight, adjustedMin, adjustedRange);
        }

        // 绘制布林带
        if (this.activeIndicators.includes('boll') && indicatorData && indicatorData.indicators && indicatorData.indicators.boll) {
            try {
                console.log('绘制布林带指标');
                this.drawBollingerBands(ctx, klines, indicatorData.indicators.boll, padding, chartWidth, chartHeight, adjustedMin, adjustedRange);
            } catch (error) {
                console.error('布林带绘制失败:', error);
            }
        }

        // 绘制坐标轴标签
        this.drawAxisLabels(ctx, canvas, klines, padding, chartWidth, chartHeight, adjustedMin, adjustedMin + adjustedRange);

        // 绘制图例和均线说明
        this.drawMainLegend(ctx, canvas, klines[klines.length - 1], indicatorData);
    }

    drawSubCharts(ctx, canvas, klines, indicatorData, padding, chartWidth, subChartHeight, mainChartBottom) {
        const subChartTop = mainChartBottom + 20;
        const indicators = this.activeIndicators.filter(ind => ['macd', 'kdj', 'rsi', 'volume'].includes(ind));
        const chartPerIndicator = subChartHeight / indicators.length;

        indicators.forEach((indicator, index) => {
            const yOffset = subChartTop + index * chartPerIndicator;
            this.drawSubChart(ctx, canvas, klines, indicatorData, indicator, padding, chartWidth, chartPerIndicator - 10, yOffset);
        });
    }

    addMouseInteraction(canvas, klines, padding, chartWidth, chartHeight, adjustedMin, adjustedRange) {
        // 移除之前的事件监听器
        const newCanvas = canvas.cloneNode(true);
        canvas.parentNode.replaceChild(newCanvas, canvas);

        // 创建信息显示面板
        this.createInfoPanel(newCanvas.parentNode);

        let isMouseOver = false;

        newCanvas.addEventListener('mousemove', (e) => {
            const rect = newCanvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            // 转换为画布坐标
            const canvasX = x * (newCanvas.width / rect.width);
            const canvasY = y * (newCanvas.height / rect.height);

            // 检查是否在主图区域
            if (canvasX >= padding.left && canvasX <= padding.left + chartWidth &&
                canvasY >= padding.top && canvasY <= padding.top + chartHeight) {

                // 计算对应的K线索引
                const dataIndex = Math.round((canvasX - padding.left) / chartWidth * (klines.length - 1));

                if (dataIndex >= 0 && dataIndex < klines.length) {
                    this.showKlineInfo(klines[dataIndex], canvasX, canvasY);
                    this.drawLegacyCrosshair(newCanvas, canvasX, canvasY, padding, chartWidth, chartHeight);
                    isMouseOver = true;
                }
            } else if (isMouseOver) {
                this.hideKlineInfo();
                this.clearLegacyCrosshair(newCanvas);
                isMouseOver = false;
            }
        });

        newCanvas.addEventListener('mouseleave', () => {
            this.hideKlineInfo();
            this.clearLegacyCrosshair(newCanvas);
            isMouseOver = false;
        });
    }

    createInfoPanel(container) {
        // 移除旧的信息面板
        const oldPanel = container.querySelector('.kline-info-panel');
        if (oldPanel) {
            oldPanel.remove();
        }

        const infoPanel = document.createElement('div');
        infoPanel.className = 'kline-info-panel';
        infoPanel.style.cssText = `
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(30, 35, 41, 0.9);
            border: 1px solid #2b3139;
            border-radius: 6px;
            padding: 10px;
            color: #d1d4dc;
            font-size: 12px;
            font-family: monospace;
            display: none;
            z-index: 1000;
            min-width: 200px;
        `;
        container.style.position = 'relative';
        container.appendChild(infoPanel);
    }

    showKlineInfo(kline, x, y) {
        const panel = document.querySelector('.kline-info-panel');
        if (!panel) return;

        const change = kline.close - kline.open;
        const changePercent = (change / kline.open * 100).toFixed(2);
        const isUp = change >= 0;
        const changeColor = isUp ? '#ff4757' : '#2ed573';

        // 计算换手率（模拟）
        const turnoverRate = (Math.random() * 5).toFixed(2);

        panel.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 8px; color: #ffffff;">
                ${kline.date}
            </div>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px;">
                <div>开盘: <span style="color: #ffffff;">${kline.open}</span></div>
                <div>收盘: <span style="color: ${changeColor};">${kline.close}</span></div>
                <div>最高: <span style="color: #ffffff;">${kline.high}</span></div>
                <div>最低: <span style="color: #ffffff;">${kline.low}</span></div>
                <div>涨跌: <span style="color: ${changeColor};">${isUp ? '+' : ''}${change.toFixed(2)}</span></div>
                <div>涨幅: <span style="color: ${changeColor};">${isUp ? '+' : ''}${changePercent}%</span></div>
            </div>
            <div style="margin-top: 8px; padding-top: 8px; border-top: 1px solid #2b3139;">
                <div>成交量: <span style="color: #ffffff;">${this.formatVolume(kline.volume)}</span></div>
                <div>成交额: <span style="color: #ffffff;">${this.formatTurnover(kline.turnover)}</span></div>
                <div>换手率: <span style="color: #ffffff;">${turnoverRate}%</span></div>
            </div>
        `;

        panel.style.display = 'block';
    }

    hideKlineInfo() {
        const panel = document.querySelector('.kline-info-panel');
        if (panel) {
            panel.style.display = 'none';
        }
    }

    drawLegacyCrosshair(canvas, x, y, padding, chartWidth, chartHeight) {
        // 验证Canvas元素有效性
        if (!canvas || !canvas.getContext) {
            console.log('⚠️ 传入的canvas参数无效，跳过旧版十字光标绘制');
            return;
        }

        const ctx = canvas.getContext('2d');

        // 保存当前状态
        ctx.save();

        // 设置十字线样式
        ctx.strokeStyle = '#666666';
        ctx.lineWidth = 1;
        ctx.setLineDash([5, 5]);

        // 绘制垂直线
        ctx.beginPath();
        ctx.moveTo(x, padding.top);
        ctx.lineTo(x, padding.top + chartHeight);
        ctx.stroke();

        // 绘制水平线
        ctx.beginPath();
        ctx.moveTo(padding.left, y);
        ctx.lineTo(padding.left + chartWidth, y);
        ctx.stroke();

        // 恢复状态
        ctx.restore();
    }

    clearLegacyCrosshair(canvas) {
        // 验证Canvas元素有效性
        if (!canvas || !canvas.getContext) {
            console.log('⚠️ 传入的canvas参数无效，跳过旧版十字光标清除');
            return;
        }

        // 重新绘制整个图表来清除十字线
        if (this.currentStock && this.lastKlineData && this.lastIndicatorData) {
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            this.drawProfessionalKlineChart(ctx, canvas, this.lastKlineData, this.lastIndicatorData);
        }
    }

    formatVolume(volume) {
        if (typeof volume === 'string') return volume;
        if (volume >= 100000000) {
            return (volume / 100000000).toFixed(2) + '亿';
        } else if (volume >= 10000) {
            return (volume / 10000).toFixed(0) + '万';
        }
        return volume.toString();
    }

    formatTurnover(turnover) {
        if (typeof turnover === 'string') return turnover;
        if (turnover >= 100000000) {
            return (turnover / 100000000).toFixed(2) + '亿';
        } else if (turnover >= 10000) {
            return (turnover / 10000).toFixed(0) + '万';
        }
        return turnover.toString();
    }

    formatMoney(amount) {
        if (typeof amount === 'string') return amount;
        if (amount >= 100000000) {
            return (amount / 100000000).toFixed(2) + '亿';
        } else if (amount >= 10000) {
            return (amount / 10000).toFixed(0) + '万';
        }
        return amount.toString();
    }

    drawMainLegend(ctx, canvas, latestKline, indicatorData) {
        if (!latestKline) return;

        ctx.fillStyle = '#d1d4dc';
        ctx.font = '12px Arial';
        ctx.textAlign = 'left';

        const legendY = 20;
        let legendX = 20;

        // K线信息
        ctx.fillText(`开: ${latestKline.open}`, legendX, legendY);
        legendX += 80;
        ctx.fillText(`高: ${latestKline.high}`, legendX, legendY);
        legendX += 80;
        ctx.fillText(`低: ${latestKline.low}`, legendX, legendY);
        legendX += 80;
        ctx.fillText(`收: ${latestKline.close}`, legendX, legendY);
        legendX += 80;

        if (latestKline.volume) {
            const volume = this.formatVolume(latestKline.volume);
            ctx.fillText(`量: ${volume}`, legendX, legendY);
        }

        // 均线图例
        if (this.activeIndicators.includes('ma') && indicatorData && indicatorData.indicators && indicatorData.indicators.ma) {
            this.drawMALegend(ctx, indicatorData.indicators.ma);
        }
    }

    drawMALegend(ctx, maData) {
        const colors = {
            ma5: '#ff9800',
            ma10: '#9c27b0',
            ma20: '#2196f3',
            ma60: '#4caf50'
        };

        const names = {
            ma5: 'MA5',
            ma10: 'MA10',
            ma20: 'MA20',
            ma60: 'MA60'
        };

        let legendX = 20;
        const legendY = 40;

        Object.entries(maData).forEach(([period, values]) => {
            if (values && values.length > 0) {
                const latestValue = values[values.length - 1];
                if (latestValue !== null && latestValue !== undefined) {
                    // 绘制颜色方块
                    ctx.fillStyle = colors[period];
                    ctx.fillRect(legendX, legendY - 8, 12, 8);

                    // 绘制文字
                    ctx.fillStyle = colors[period];
                    ctx.font = '11px Arial';
                    ctx.fillText(`${names[period]}: ${latestValue.toFixed(2)}`, legendX + 16, legendY);

                    legendX += 100;
                }
            }
        });
    }

    drawSubChart(ctx, canvas, klines, indicatorData, indicator, padding, chartWidth, chartHeight, yOffset) {
        if (!indicatorData || !indicatorData.indicators) return;

        // 绘制子图背景
        ctx.fillStyle = '#1a1e24';
        ctx.fillRect(padding.left, yOffset, chartWidth, chartHeight);

        // 绘制子图边框
        ctx.strokeStyle = '#2b3139';
        ctx.lineWidth = 1;
        ctx.strokeRect(padding.left, yOffset, chartWidth, chartHeight);

        switch (indicator) {
            case 'macd':
                this.drawMACDChart(ctx, klines, indicatorData.indicators.macd, padding, chartWidth, chartHeight, yOffset);
                break;
            case 'kdj':
                this.drawKDJChart(ctx, klines, indicatorData.indicators.kdj, padding, chartWidth, chartHeight, yOffset);
                break;
            case 'rsi':
                this.drawRSIChart(ctx, klines, indicatorData.indicators.rsi, padding, chartWidth, chartHeight, yOffset);
                break;
            case 'volume':
                this.drawVolumeChart(ctx, klines, padding, chartWidth, chartHeight, yOffset);
                break;
        }

        // 绘制子图标题
        ctx.fillStyle = '#d1d4dc';
        ctx.font = 'bold 12px Arial';
        ctx.textAlign = 'left';
        ctx.fillText(indicator.toUpperCase(), padding.left + 10, yOffset + 15);
    }

    drawMACDChart(ctx, klines, macdData, padding, chartWidth, chartHeight, yOffset) {
        if (!macdData || !macdData.macd) return;

        const macdValues = macdData.macd.slice(-klines.length);
        const maxValue = Math.max(...macdValues.filter(v => v !== null));
        const minValue = Math.min(...macdValues.filter(v => v !== null));
        const range = maxValue - minValue || 1;

        // 绘制MACD柱状图
        macdValues.forEach((value, index) => {
            if (value !== null) {
                // 使用统一的x轴坐标计算，确保与主图K线完全对齐
                const x = this.calculateXPosition(index, klines.length, padding, chartWidth);
                const barHeight = Math.abs(value) / range * chartHeight * 0.8;
                const barY = value >= 0 ?
                    yOffset + chartHeight/2 - barHeight :
                    yOffset + chartHeight/2;

                ctx.fillStyle = value >= 0 ? '#ff4757' : '#2ed573';
                ctx.fillRect(x - 2, barY, 4, barHeight);
            }
        });

        // 绘制DIF和DEA线
        if (macdData.dif && macdData.dea) {
            this.drawIndicatorLine(ctx, klines, macdData.dif, padding, chartWidth, chartHeight, yOffset, '#ffc107', minValue, range);
            this.drawIndicatorLine(ctx, klines, macdData.dea, padding, chartWidth, chartHeight, yOffset, '#2196f3', minValue, range);
        }
    }

    drawKDJChart(ctx, klines, kdjData, padding, chartWidth, chartHeight, yOffset) {
        if (!kdjData) return;

        const colors = { k: '#ff9800', d: '#2196f3', j: '#4caf50' };

        Object.entries(kdjData).forEach(([line, values]) => {
            if (values && colors[line]) {
                this.drawIndicatorLine(ctx, klines, values, padding, chartWidth, chartHeight, yOffset, colors[line], 0, 100);
            }
        });

        // 绘制参考线
        ctx.strokeStyle = '#444';
        ctx.lineWidth = 1;
        ctx.setLineDash([2, 2]);

        // 20线
        const y20 = yOffset + chartHeight * 0.8;
        ctx.beginPath();
        ctx.moveTo(padding.left, y20);
        ctx.lineTo(padding.left + chartWidth, y20);
        ctx.stroke();

        // 80线
        const y80 = yOffset + chartHeight * 0.2;
        ctx.beginPath();
        ctx.moveTo(padding.left, y80);
        ctx.lineTo(padding.left + chartWidth, y80);
        ctx.stroke();

        ctx.setLineDash([]);
    }

    drawRSIChart(ctx, klines, rsiData, padding, chartWidth, chartHeight, yOffset) {
        if (!rsiData) return;

        const colors = { rsi6: '#ff9800', rsi12: '#2196f3', rsi24: '#4caf50' };

        Object.entries(rsiData).forEach(([line, values]) => {
            if (values && colors[line]) {
                this.drawIndicatorLine(ctx, klines, values, padding, chartWidth, chartHeight, yOffset, colors[line], 0, 100);
            }
        });

        // 绘制参考线
        ctx.strokeStyle = '#444';
        ctx.lineWidth = 1;
        ctx.setLineDash([2, 2]);

        [30, 70].forEach(level => {
            const y = yOffset + chartHeight * (1 - level/100);
            ctx.beginPath();
            ctx.moveTo(padding.left, y);
            ctx.lineTo(padding.left + chartWidth, y);
            ctx.stroke();
        });

        ctx.setLineDash([]);
    }

    drawVolumeChart(ctx, klines, padding, chartWidth, chartHeight, yOffset) {
        const volumes = klines.map(k => k.volume || 0);
        const maxVolume = Math.max(...volumes);

        volumes.forEach((volume, index) => {
            // 使用统一的x轴坐标计算，确保与主图K线完全对齐
            const x = this.calculateXPosition(index, klines.length, padding, chartWidth);
            const barHeight = (volume / maxVolume) * chartHeight * 0.9;
            const barY = yOffset + chartHeight - barHeight;

            const kline = klines[index];
            const isUp = kline.close >= kline.open;
            ctx.fillStyle = isUp ? '#ff4757' : '#2ed573';
            ctx.fillRect(x - 2, barY, 4, barHeight);
        });
    }

    drawIndicatorLine(ctx, klines, values, padding, chartWidth, chartHeight, yOffset, color, minValue, range, lineWidth = 1.5) {
        ctx.strokeStyle = color;
        ctx.lineWidth = lineWidth; // 支持自定义线条粗细，默认1.5px更清晰
        ctx.beginPath();

        let started = false;
        values.slice(-klines.length).forEach((value, index) => {
            if (value !== null && value !== undefined) {
                // 使用统一的x轴坐标计算，确保与主图K线完全对齐
                const x = this.calculateXPosition(index, klines.length, padding, chartWidth);
                const y = yOffset + chartHeight - ((value - minValue) / range) * chartHeight;

                if (!started) {
                    ctx.moveTo(x, y);
                    started = true;
                } else {
                    ctx.lineTo(x, y);
                }
            }
        });

        ctx.stroke();
    }

    drawBollingerBands(ctx, klines, bollData, padding, chartWidth, chartHeight, adjustedMin, adjustedRange) {
        if (!bollData || !bollData.upper || !bollData.middle || !bollData.lower) {
            console.warn('布林带数据不完整');
            return;
        }

        // 上轨
        this.drawIndicatorLineInMainChart(ctx, klines, bollData.upper, padding, chartWidth, chartHeight, '#ff5722', adjustedMin, adjustedRange, [5, 5]);

        // 中轨
        this.drawIndicatorLineInMainChart(ctx, klines, bollData.middle, padding, chartWidth, chartHeight, '#ffc107', adjustedMin, adjustedRange);

        // 下轨
        this.drawIndicatorLineInMainChart(ctx, klines, bollData.lower, padding, chartWidth, chartHeight, '#ff5722', adjustedMin, adjustedRange, [5, 5]);
    }

    drawIndicatorLineInMainChart(ctx, klines, values, padding, chartWidth, chartHeight, color, minValue, range, dashPattern = null) {
        ctx.strokeStyle = color;
        ctx.lineWidth = 1;

        if (dashPattern) {
            ctx.setLineDash(dashPattern);
        }

        ctx.beginPath();

        let started = false;
        values.slice(-klines.length).forEach((value, index) => {
            if (value !== null && value !== undefined) {
                // 使用统一的x轴坐标计算，确保与主图K线完全对齐
                const x = this.calculateXPosition(index, klines.length, padding, chartWidth);
                const y = padding.top + chartHeight - ((value - minValue) / range) * chartHeight;

                if (!started) {
                    ctx.moveTo(x, y);
                    started = true;
                } else {
                    ctx.lineTo(x, y);
                }
            }
        });

        ctx.stroke();

        if (dashPattern) {
            ctx.setLineDash([]);
        }
    }

    drawMovingAveragesInMainChart(ctx, klines, maData, padding, chartWidth, chartHeight, adjustedMin, adjustedRange) {
        const colors = {
            ma5: '#ff9800',
            ma10: '#9c27b0',
            ma20: '#2196f3',
            ma60: '#4caf50'
        };

        Object.entries(maData).forEach(([period, values]) => {
            if (values && values.some(v => v !== null)) {
                this.drawIndicatorLineInMainChart(ctx, klines, values, padding, chartWidth, chartHeight, colors[period], adjustedMin, adjustedRange);
            }
        });
    }

    drawGrid(ctx, canvas, padding, chartWidth, chartHeight) {
        ctx.strokeStyle = '#2b3139';
        ctx.lineWidth = 1;

        // 垂直网格线
        for (let i = 0; i <= 10; i++) {
            const x = padding.left + (chartWidth / 10) * i;
            ctx.beginPath();
            ctx.moveTo(x, padding.top);
            ctx.lineTo(x, padding.top + chartHeight);
            ctx.stroke();
        }

        // 水平网格线
        for (let i = 0; i <= 8; i++) {
            const y = padding.top + (chartHeight / 8) * i;
            ctx.beginPath();
            ctx.moveTo(padding.left, y);
            ctx.lineTo(padding.left + chartWidth, y);
            ctx.stroke();
        }
    }

    // 重复的drawCandlesticks方法已删除，使用上面统一的版本

    drawMovingAverages(ctx, klines, maData, padding, chartWidth, chartHeight, minPrice, priceRange) {
        const colors = {
            ma5: '#ff9800',
            ma10: '#9c27b0',
            ma20: '#2196f3',
            ma60: '#4caf50'
        };

        Object.entries(maData).forEach(([period, values]) => {
            if (!values || values.length === 0) return;

            ctx.strokeStyle = colors[period] || '#ffffff';
            ctx.lineWidth = 1;
            ctx.beginPath();

            let started = false;
            values.slice(-klines.length).forEach((value, index) => {
                if (value !== null && value !== undefined) {
                    // 使用统一的x轴坐标计算，确保与主图K线完全对齐
                    const x = this.calculateXPosition(index, klines.length, padding, chartWidth);
                    const y = padding.top + chartHeight - ((value - minPrice) / priceRange) * chartHeight;

                    if (!started) {
                        ctx.moveTo(x, y);
                        started = true;
                    } else {
                        ctx.lineTo(x, y);
                    }
                }
            });

            ctx.stroke();
        });
    }

    drawAxisLabels(ctx, canvas, klines, padding, chartWidth, chartHeight, minPrice, maxPrice) {
        ctx.fillStyle = '#d1d4dc';
        ctx.font = '11px Arial';
        ctx.textAlign = 'right';

        // Y轴价格标签
        for (let i = 0; i <= 8; i++) {
            const price = minPrice + (maxPrice - minPrice) * (1 - i / 8);
            const y = padding.top + (chartHeight / 8) * i;
            ctx.fillText(price.toFixed(2), padding.left - 10, y + 4);
        }

        // X轴日期标签
        ctx.textAlign = 'center';
        const labelCount = Math.min(6, klines.length);
        for (let i = 0; i < labelCount; i++) {
            const index = Math.floor((klines.length - 1) * i / (labelCount - 1));
            const kline = klines[index];
            // 使用统一的x轴坐标计算，确保与主图K线完全对齐
            const x = this.calculateXPosition(index, klines.length, padding, chartWidth);
            const date = new Date(kline.date);
            const label = `${date.getMonth() + 1}/${date.getDate()}`;
            ctx.fillText(label, x, canvas.height - 20);
        }
    }

    drawLegend(ctx, canvas, latestKline) {
        if (!latestKline) return;

        ctx.fillStyle = '#d1d4dc';
        ctx.font = '12px Arial';
        ctx.textAlign = 'left';

        const legendY = 20;
        const legendX = 20;

        ctx.fillText(`开: ${latestKline.open}`, legendX, legendY);
        ctx.fillText(`高: ${latestKline.high}`, legendX + 80, legendY);
        ctx.fillText(`低: ${latestKline.low}`, legendX + 160, legendY);
        ctx.fillText(`收: ${latestKline.close}`, legendX + 240, legendY);

        if (latestKline.volume) {
            const volume = typeof latestKline.volume === 'number' ?
                (latestKline.volume / 100000000).toFixed(2) + '亿' :
                latestKline.volume;
            ctx.fillText(`量: ${volume}`, legendX + 320, legendY);
        }
    }

    // 这个方法已经在上面重新实现了，移除重复的LightweightCharts版本

    // 这个方法已经在上面重新实现了，移除重复的LightweightCharts版本

    // LightweightCharts 相关方法已移除，使用Canvas自绘图表系统

    generateChartData(period) {
        const periods = {
            '1d': { count: 24, unit: 'hour' },
            '5d': { count: 5, unit: 'day' },
            '1m': { count: 30, unit: 'day' },
            '3m': { count: 90, unit: 'day' },
            '1y': { count: 12, unit: 'month' }
        };

        const config = periods[period] || periods['1d'];
        const labels = [];
        const prices = [];
        let basePrice = 50 + Math.random() * 50;

        for (let i = 0; i < config.count; i++) {
            if (config.unit === 'hour') {
                labels.push(`${i}:00`);
            } else if (config.unit === 'day') {
                const date = new Date();
                date.setDate(date.getDate() - (config.count - i));
                labels.push(date.toLocaleDateString());
            } else {
                const date = new Date();
                date.setMonth(date.getMonth() - (config.count - i));
                labels.push(date.toLocaleDateString());
            }

            basePrice += (Math.random() - 0.5) * 5;
            prices.push(basePrice.toFixed(2));
        }

        return { labels, prices };
    }

    generateAnalysis(stockData) {
        console.log('📝 开始生成分析...');

        // 检查分析面板是否存在（专业版界面可能没有）
        const analysisScore = document.getElementById('analysis-score');
        if (analysisScore) {
            const score = (Math.random() * 4 + 6).toFixed(1);
            const scoreValueEl = analysisScore.querySelector('.score-value');
            if (scoreValueEl) {
                scoreValueEl.textContent = score;
                console.log('📝 ✅ 更新分析评分:', score);
            }
        } else {
            console.log('📝 ⚠️ 分析评分元素不存在，跳过');
        }

        // 生成技术分析
        const technicalAnalyses = [
            "股价突破20日均线，MACD金叉形成，技术面偏强。建议关注量能配合情况。",
            "RSI指标显示超买状态，短期可能面临调整压力。建议谨慎操作。",
            "布林带收窄，股价在中轨附近震荡，等待方向选择。",
            "成交量放大，股价创新高，多头趋势明确。"
        ];

        const fundamentalAnalyses = [
            "公司基本面稳健，ROE保持在合理水平，但需关注行业政策变化影响。",
            "最新财报显示营收增长放缓，但利润率有所提升。",
            "行业景气度较高，公司市场份额稳定，长期投资价值较好。",
            "估值水平合理，股息率较为稳定，适合价值投资者关注。"
        ];

        const sentimentAnalyses = [
            "近期资金流入明显，机构持仓比例上升，市场情绪偏乐观。",
            "散户情绪较为谨慎，机构资金小幅流出，市场观望情绪浓厚。",
            "社交媒体讨论热度上升，投资者关注度较高。",
            "北向资金持续流入，外资看好该股长期前景。"
        ];

        // 安全更新分析内容
        const technicalEl = document.getElementById('technical-analysis');
        if (technicalEl) {
            technicalEl.textContent = technicalAnalyses[Math.floor(Math.random() * technicalAnalyses.length)];
            console.log('📝 ✅ 更新技术分析');
        } else {
            console.log('📝 ⚠️ 技术分析元素不存在，跳过');
        }

        const fundamentalEl = document.getElementById('fundamental-analysis');
        if (fundamentalEl) {
            fundamentalEl.textContent = fundamentalAnalyses[Math.floor(Math.random() * fundamentalAnalyses.length)];
            console.log('📝 ✅ 更新基本面分析');
        } else {
            console.log('📝 ⚠️ 基本面分析元素不存在，跳过');
        }

        const sentimentEl = document.getElementById('sentiment-analysis');
        if (sentimentEl) {
            sentimentEl.textContent = sentimentAnalyses[Math.floor(Math.random() * sentimentAnalyses.length)];
            console.log('📝 ✅ 更新情绪分析');
        } else {
            console.log('📝 ⚠️ 情绪分析元素不存在，跳过');
        }

        console.log('📝 ✅ 分析生成完成');
    }

    async loadMarketData() {
        console.log('📊 市场数据加载已跳过（市场概况功能已移除）');
        // 市场概况功能已移除，不再需要加载市场数据
    }

    // 市场概况功能已移除

    // updateIndexDisplay方法已移除

    updateMarketStatus() {
        const now = new Date();
        const timeEl = document.getElementById('market-time');
        const statusEl = document.getElementById('trading-status');

        if (timeEl) {
            timeEl.textContent = now.toLocaleTimeString('zh-CN', { hour12: false });
        }

        if (statusEl) {
            const isTrading = this.isMarketOpen(now);
            statusEl.textContent = isTrading ? '交易中' : '休市';
            statusEl.className = 'trading-status';
            statusEl.classList.add(isTrading ? 'open' : 'closed');
        }
    }

    isMarketOpen(now = new Date()) {
        const day = now.getDay(); // 0=周日, 1=周一, ..., 6=周六
        const hour = now.getHours();
        const minute = now.getMinutes();
        const time = hour * 100 + minute;

        // 周末不开市
        if (day === 0 || day === 6) {
            return false;
        }

        // 工作日交易时间: 9:30-11:30, 13:00-15:00
        return (time >= 930 && time <= 1130) || (time >= 1300 && time <= 1500);
    }

    async loadHotStocks(type = 'gainers') {
        console.log('🔥 开始加载热门股票:', type);
        try {
            console.log('🔥 查找热门股票列表容器...');
            const stocksList = document.getElementById('hot-stocks-list');
            if (!stocksList) {
                console.log('🔥 ⚠️ 热门股票列表容器未找到 - 专业版界面不包含此功能');
                return; // 专业版界面不包含热门股票功能，直接返回
            }
            console.log('🔥 ✅ 找到热门股票列表容器');

            stocksList.innerHTML = '<div style="text-align: center; padding: 20px; color: var(--text-muted);">正在加载热门股票...</div>';

            // 调用真实的热门股票API
            console.log('🔥 调用API:', `http://localhost:5001/api/hot-stocks?type=${type}`);
            const response = await fetch(`http://localhost:5001/api/hot-stocks?type=${type}`);
            console.log('🔥 API响应状态:', response.status);

            if (!response.ok) {
                throw new Error(`热门股票API错误: ${response.status} ${response.statusText}`);
            }

            const result = await response.json();
            console.log('🔥 API响应数据:', result);

            if (!result.success || !result.data || result.data.length === 0) {
                throw new Error('API返回数据为空或失败');
            }

            const stocks = result.data;
            console.log('🔥 ✅ 获取到真实热门股票数据:', stocks.length, '条');

            // 清空加载提示
            console.log('🔥 清空加载提示，开始渲染股票列表...');
            stocksList.innerHTML = '';

            stocks.forEach((stock, i) => {
                console.log(`🔥 渲染股票 ${i+1}:`, stock.name);
                const stockItem = this.createStockItem(stock);
                if (stockItem && stocksList) {
                    stocksList.appendChild(stockItem);
                } else {
                    console.error('🔥 ❌ 股票项目或列表容器为空:', { stockItem, stocksList });
                }
            });
            console.log('🔥 ✅ 热门股票渲染完成');

            console.log('🔥 ✅ 热门股票加载完成，共', stocks.length, '条');
        } catch (error) {
            console.error('🔥 ❌ 热门股票加载失败:', error);
            console.error('🔥 ❌ 错误堆栈:', error.stack);

            // 显示错误信息（仅在原版界面中）
            const stocksList = document.getElementById('hot-stocks-list');
            if (stocksList) {
                stocksList.innerHTML = `
                    <div style="text-align: center; padding: 20px; color: var(--danger-color);">
                        <i class="fas fa-exclamation-triangle" style="font-size: 24px; margin-bottom: 10px;"></i>
                        <div>热门股票数据加载失败</div>
                        <div style="font-size: 12px; color: var(--text-muted); margin-top: 5px;">
                            ${error.message}
                        </div>
                    </div>
                `;
            }

            throw error;
        }
    }



    createStockItem(stock) {
        const item = document.createElement('div');
        item.className = 'stock-item';
        item.addEventListener('click', () => {
            console.log('🔥 点击热门股票:', stock.name, stock.code);
            this.loadStockData(stock.code);
        });

        const isPositive = parseFloat(stock.change) > 0;
        const changeColor = isPositive ? 'var(--color-up)' : 'var(--color-down)';

        item.innerHTML = `
            <div class="stock-info">
                <div class="name">${stock.name}</div>
                <div class="code">${stock.code}</div>
            </div>
            <div class="stock-price">${stock.price}</div>
            <div class="stock-change">
                <div class="amount" style="color: ${changeColor}">
                    ${isPositive ? '+' : ''}${stock.change}
                </div>
                <div class="percent" style="color: ${changeColor}">
                    ${isPositive ? '+' : ''}${stock.changePercent}%
                </div>
            </div>
            <div class="stock-volume">${stock.volume}</div>
        `;

        return item;
    }



    startRealTimeUpdates() {
        console.log('🔄 启动专业版实时更新...');

        // 跳过市场概况更新（已移除）

        // 设置定时更新
        this.updateInterval = setInterval(() => {
            // 更新当前股票数据（如果有选中的股票）
            if (this.currentStock) {
                console.log('🔄 实时更新股票数据:', this.currentStock);
                this.updateCurrentStockDataPartial();
            }
            // 更新指数数据
            this.updateIndicesData();
        }, 5000); // 每5秒更新一次

        // 设置时间更新（每秒更新）
        this.timeUpdateInterval = setInterval(() => {
            this.updateMarketStatus();
        }, 1000);

        console.log('✅ 专业版实时更新已启动');
    }

    async updateCurrentStockDataPartial() {
        if (!this.currentStock) return;

        try {
            // 转换显示代码为查询代码
            const queryCode = this.convertToQueryCode(this.currentStock);

            // 获取最新股票数据
            const response = await fetch(`http://localhost:5001/api/stock/${queryCode}`);
            const result = await response.json();

            if (result.success) {
                const data = result.data;
                // 保持显示代码不变
                data.code = this.currentStock;

                // 更新价格显示
                this.updatePriceDisplay(data);
                this.updateStatsDisplay(data);

                // 如果是交易时间，也更新图表数据（但不重新渲染整个图表）
                if (data.is_trading) {
                    console.log('🔄 交易时间，更新股票数据:', this.currentStock);
                }
            }
        } catch (error) {
            console.warn('股票数据实时更新失败:', error);
        }
    }

    updatePriceDisplay(data) {
        // 只更新价格相关的DOM元素
        const priceEl = document.getElementById('current-price');
        const changeAmountEl = document.getElementById('change-amount');
        const changePercentEl = document.getElementById('change-percent');

        if (priceEl) {
            priceEl.textContent = data.price;
            const isPositive = parseFloat(data.change) > 0;
            priceEl.style.color = isPositive ? 'var(--color-up)' : 'var(--color-down)';
        }

        if (changeAmountEl && changePercentEl) {
            const isPositive = parseFloat(data.change) > 0;
            const color = isPositive ? 'var(--color-up)' : 'var(--color-down)';

            changeAmountEl.textContent = data.change > 0 ? `+${data.change}` : data.change;
            changeAmountEl.style.color = color;

            changePercentEl.textContent = data.changePercent > 0 ? `+${data.changePercent}%` : `${data.changePercent}%`;
            changePercentEl.style.color = color;
        }
    }

    updateStatsDisplay(data) {
        // 更新统计数据
        const elements = {
            'high-price': data.high,
            'low-price': data.low,
            'volume': typeof data.volume === 'string' ? data.volume : `${data.volume}亿`,
            'turnover': typeof data.turnover === 'string' ? data.turnover : `${data.turnover}亿`
        };

        Object.entries(elements).forEach(([id, value]) => {
            const el = document.getElementById(id);
            if (el) el.textContent = value;
        });
    }

    async updateRealTimeData() {
        // 市场概况功能已移除，专业版不再需要实时更新市场概况
        console.log('🔄 专业版实时数据更新（市场概况已移除）');
    }

    // updateMarketOverviewRealTime方法已移除

    updateCurrentStockData() {
        if (!this.currentStock) return;

        // 只在交易时间才重新获取数据，否则不更新价格
        this.loadStockData(this.currentStock);
    }

    refreshData() {
        this.showLoading();

        setTimeout(() => {
            // 刷新当前股票数据和指数数据
            if (this.currentStock) {
                this.loadStockData(this.currentStock);
            }
            this.loadIndicesData();
            this.hideLoading();
            this.showMessage('数据已更新', 'success');
        }, 1000);
    }

    // 加载指数数据
    async loadIndicesData() {
        // 检查是否为专业版界面（没有指数显示元素）
        const hasIndexElements = document.getElementById('sh-value') ||
                                document.getElementById('sz-value') ||
                                document.getElementById('cy-value');

        if (!hasIndexElements) {
            console.log('⚠️ 专业版界面不包含指数显示功能，跳过指数数据加载');
            return;
        }

        const indices = [
            { code: '399300', id: 'sh' },    // 沪深300 (真实数据)
            { code: '399001', id: 'sz' },    // 深证成指 (真实数据)
            { code: '399006', id: 'cy' },    // 创业板指 (真实数据)
            { code: '399905', id: 'kc' }     // 中证500 (真实数据)
        ];

        for (const index of indices) {
            try {
                const response = await fetch(`/api/stock/${index.code}`);
                const data = await response.json();

                if (data.success) {
                    this.updateIndexDisplay(index.id, data.data);
                }
            } catch (error) {
                console.error(`加载指数 ${index.code} 失败:`, error);
            }
        }
    }

    // 更新指数数据（实时更新用）
    async updateIndicesData() {
        await this.loadIndicesData();
    }

    // 更新指数显示
    updateIndexDisplay(indexId, data) {
        console.log(`🔄 更新指数显示: ${indexId}`, data);

        const valueElement = document.getElementById(`${indexId}-value`);
        const changeElement = document.getElementById(`${indexId}-change`);

        console.log(`📍 查找元素: ${indexId}-value`, valueElement);
        console.log(`📍 查找元素: ${indexId}-change`, changeElement);

        if (!valueElement || !changeElement) {
            console.log(`⚠️ 指数显示元素未找到: ${indexId} - 专业版界面不包含此功能`);
            return; // 专业版界面不包含指数显示功能，直接返回
        }

        if (valueElement && changeElement && data) {
            // 更新价格
            const price = data.price ? data.price.toFixed(2) : '--';
            valueElement.textContent = price;
            console.log(`💰 更新价格: ${indexId} = ${price}`);

            // 更新涨跌幅
            if (data.changePercent !== undefined) {
                const changePercent = data.changePercent;
                const changeAmount = data.change || 0;

                const changeText = `${changeAmount >= 0 ? '+' : ''}${changeAmount.toFixed(2)} (${changePercent >= 0 ? '+' : ''}${changePercent.toFixed(2)}%)`;
                changeElement.textContent = changeText;
                console.log(`📈 更新涨跌: ${indexId} = ${changeText}`);

                // 设置颜色样式
                changeElement.className = 'index-change';
                if (changePercent > 0) {
                    changeElement.classList.add('up');
                } else if (changePercent < 0) {
                    changeElement.classList.add('down');
                } else {
                    changeElement.classList.add('flat');
                }
            } else {
                changeElement.textContent = '--';
                changeElement.className = 'index-change flat';
                console.log(`❓ 无涨跌数据: ${indexId}`);
            }
        } else {
            console.error(`❌ 指数显示更新失败: ${indexId}`, {
                valueElement: !!valueElement,
                changeElement: !!changeElement,
                data: !!data
            });
        }
    }

    showLoading() {
        const loadingOverlay = document.getElementById('loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.classList.add('show');
        }
    }

    hideLoading() {
        console.log('🔄 隐藏加载状态');
        const loadingOverlay = document.getElementById('loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.classList.remove('show');
            console.log('✅ 加载状态已隐藏');
        }
    }

    showMessage(message, type = 'info') {
        // 创建消息提示
        const messageEl = document.createElement('div');
        messageEl.className = `message message-${type}`;
        messageEl.textContent = message;
        messageEl.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            background: var(--bg-card);
            color: var(--text-primary);
            padding: 12px 20px;
            border-radius: 8px;
            border-left: 4px solid var(--primary-color);
            box-shadow: var(--shadow-lg);
            z-index: 1001;
            animation: slideIn 0.3s ease-out;
        `;

        if (type === 'error') {
            messageEl.style.borderLeftColor = 'var(--danger-color)';
        } else if (type === 'success') {
            messageEl.style.borderLeftColor = 'var(--secondary-color)';
        } else if (type === 'warning') {
            messageEl.style.borderLeftColor = 'var(--warning-color)';
        }

        if (document.body) {
            document.body.appendChild(messageEl);

            // 3秒后自动移除
            setTimeout(() => {
                messageEl.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => {
                    if (document.body && document.body.contains(messageEl)) {
                        document.body.removeChild(messageEl);
                    }
                }, 300);
            }, 3000);
        } else {
            console.error('❌ document.body 不存在，无法显示消息');
        }
    }
}

// 添加动画样式
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            opacity: 0;
            transform: translateX(100%);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @keyframes slideOut {
        from {
            opacity: 1;
            transform: translateX(0);
        }
        to {
            opacity: 0;
            transform: translateX(100%);
        }
    }
`;
if (document.head) {
    document.head.appendChild(style);
} else {
    console.error('❌ document.head 不存在，无法添加样式');
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM加载完成，开始初始化');

    try {
        const analyzer = new StockAnalyzer();

        // 延迟初始化，确保所有资源加载完成
        setTimeout(async () => {
            console.log('开始延迟初始化');
            await analyzer.init();
        }, 100);

    } catch (error) {
        console.error('初始化失败:', error);

        // 显示错误信息
        const loadingOverlay = document.getElementById('loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.innerHTML = `
                <div style="text-align: center; color: var(--color-down);">
                    <div style="font-size: 24px; margin-bottom: 10px;">⚠️</div>
                    <div>应用初始化失败</div>
                    <div style="font-size: 12px; margin-top: 10px;">${error.message}</div>
                    <button onclick="location.reload()" style="margin-top: 20px; padding: 10px 20px; background: var(--primary-color); color: white; border: none; border-radius: 6px; cursor: pointer;">
                        重新加载
                    </button>
                </div>
            `;
        }
    }
});
