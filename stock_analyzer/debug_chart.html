<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图表调试测试</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .debug-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .debug-section {
            background: var(--bg-card);
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        .chart-container {
            height: 500px;
            background: var(--bg-tertiary);
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid var(--border-color);
            position: relative;
        }
        .debug-controls {
            display: flex;
            gap: 10px;
            margin: 15px 0;
            flex-wrap: wrap;
        }
        .debug-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
        }
        .debug-log {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            color: var(--text-secondary);
        }
        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
        }
        .status.success {
            background: rgba(76, 175, 80, 0.1);
            border: 1px solid var(--color-up);
            color: var(--color-up);
        }
        .status.error {
            background: rgba(244, 67, 54, 0.1);
            border: 1px solid var(--color-down);
            color: var(--color-down);
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1 style="text-align: center; color: var(--primary-color);">🔧 图表调试测试</h1>
        
        <div class="debug-section">
            <h3>📊 图表渲染测试</h3>
            
            <div class="debug-controls">
                <input type="text" id="stock-input" placeholder="股票代码 (如: 000001)" 
                       style="padding: 10px; background: var(--bg-tertiary); border: 1px solid var(--border-color); border-radius: 6px; color: var(--text-primary);">
                <button class="debug-btn" onclick="testBasicChart()">基础图表</button>
                <button class="debug-btn" onclick="testWithMA()">测试均线</button>
                <button class="debug-btn" onclick="testWithBoll()">测试布林带</button>
                <button class="debug-btn" onclick="testWithMACD()">测试MACD</button>
                <button class="debug-btn" onclick="clearLog()">清空日志</button>
            </div>
            
            <div id="status-display"></div>
            <div id="chart-container" class="chart-container"></div>
            <div id="debug-log" class="debug-log"></div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="index.html" style="color: var(--primary-color); text-decoration: none;">← 返回主应用</a>
        </div>
    </div>

    <script>
        // 重定向console.log到调试面板
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToLog(message, type = 'log') {
            const logEl = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#ff4757' : type === 'warn' ? '#ffa502' : '#d1d4dc';
            logEl.innerHTML += `<div style="color: ${color};">[${timestamp}] ${message}</div>`;
            logEl.scrollTop = logEl.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToLog(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToLog('ERROR: ' + args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToLog('WARN: ' + args.join(' '), 'warn');
        };

        // 简化的图表渲染器
        class DebugChartRenderer {
            constructor() {
                this.activeIndicators = [];
                this.lastKlineData = null;
                this.lastIndicatorData = null;
            }
            
            async loadData(stockCode) {
                try {
                    console.log(`开始获取 ${stockCode} 的数据`);
                    
                    const [klineResponse, indicatorResponse] = await Promise.all([
                        fetch(`http://localhost:5001/api/kline/${stockCode}?period=1d&count=30`),
                        fetch(`http://localhost:5001/api/indicators/${stockCode}?period=1d`)
                    ]);

                    const klineResult = await klineResponse.json();
                    const indicatorResult = await indicatorResponse.json();

                    console.log('K线数据结果:', klineResult.success);
                    console.log('技术指标结果:', indicatorResult.success);

                    if (klineResult.success && indicatorResult.success) {
                        this.lastKlineData = klineResult.data;
                        this.lastIndicatorData = indicatorResult.data;
                        
                        console.log('K线数据条数:', this.lastKlineData.klines.length);
                        console.log('技术指标:', Object.keys(this.lastIndicatorData.indicators));
                        
                        return true;
                    } else {
                        throw new Error('数据获取失败');
                    }
                } catch (error) {
                    console.error('数据加载失败:', error.message);
                    return false;
                }
            }
            
            renderChart() {
                if (!this.lastKlineData || !this.lastIndicatorData) {
                    console.error('没有数据可渲染');
                    return;
                }
                
                try {
                    console.log('开始渲染图表');
                    console.log('活跃指标:', this.activeIndicators);
                    
                    const container = document.getElementById('chart-container');
                    const containerWidth = container.clientWidth || 800;
                    const containerHeight = 500;
                    
                    container.innerHTML = `<canvas id="debug-chart" width="${containerWidth}" height="${containerHeight}" style="width: 100%; height: 100%;"></canvas>`;
                    const canvas = document.getElementById('debug-chart');
                    const ctx = canvas.getContext('2d');
                    
                    this.drawSimpleChart(ctx, canvas);
                    console.log('图表渲染完成');
                    
                } catch (error) {
                    console.error('图表渲染失败:', error.message);
                    console.error('错误堆栈:', error.stack);
                }
            }
            
            drawSimpleChart(ctx, canvas) {
                const klines = this.lastKlineData.klines;
                const padding = { top: 60, right: 80, bottom: 60, left: 60 };
                const chartWidth = canvas.width - padding.left - padding.right;
                const chartHeight = canvas.height - padding.top - padding.bottom;
                
                // 计算价格范围
                const prices = klines.map(k => [k.high, k.low, k.open, k.close]).flat();
                const maxPrice = Math.max(...prices);
                const minPrice = Math.min(...prices);
                const priceRange = maxPrice - minPrice;
                const priceBuffer = priceRange * 0.1;
                
                const adjustedMax = maxPrice + priceBuffer;
                const adjustedMin = minPrice - priceBuffer;
                const adjustedRange = adjustedMax - adjustedMin;
                
                console.log(`价格范围: ${minPrice.toFixed(2)} - ${maxPrice.toFixed(2)}`);
                
                // 背景
                ctx.fillStyle = '#1e2329';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // 网格
                this.drawGrid(ctx, padding, chartWidth, chartHeight);
                
                // K线
                this.drawKlines(ctx, klines, padding, chartWidth, chartHeight, adjustedMin, adjustedRange);
                
                // 技术指标
                if (this.activeIndicators.includes('ma')) {
                    console.log('绘制移动平均线');
                    this.drawMA(ctx, klines, padding, chartWidth, chartHeight, adjustedMin, adjustedRange);
                }
                
                if (this.activeIndicators.includes('boll')) {
                    console.log('绘制布林带');
                    this.drawBoll(ctx, klines, padding, chartWidth, chartHeight, adjustedMin, adjustedRange);
                }
                
                // 坐标轴
                this.drawAxis(ctx, canvas, klines, padding, chartWidth, chartHeight, adjustedMin, adjustedMax);
                
                // 图例
                this.drawLegend(ctx, klines[klines.length - 1]);
            }
            
            drawGrid(ctx, padding, chartWidth, chartHeight) {
                ctx.strokeStyle = '#2b3139';
                ctx.lineWidth = 1;
                
                for (let i = 0; i <= 10; i++) {
                    const x = padding.left + (chartWidth / 10) * i;
                    ctx.beginPath();
                    ctx.moveTo(x, padding.top);
                    ctx.lineTo(x, padding.top + chartHeight);
                    ctx.stroke();
                }
                
                for (let i = 0; i <= 8; i++) {
                    const y = padding.top + (chartHeight / 8) * i;
                    ctx.beginPath();
                    ctx.moveTo(padding.left, y);
                    ctx.lineTo(padding.left + chartWidth, y);
                    ctx.stroke();
                }
            }
            
            drawKlines(ctx, klines, padding, chartWidth, chartHeight, minPrice, priceRange) {
                const candleWidth = Math.max(3, chartWidth / klines.length * 0.7);
                
                klines.forEach((kline, index) => {
                    const x = padding.left + (chartWidth / (klines.length - 1)) * index;
                    const openY = padding.top + chartHeight - ((kline.open - minPrice) / priceRange) * chartHeight;
                    const closeY = padding.top + chartHeight - ((kline.close - minPrice) / priceRange) * chartHeight;
                    const highY = padding.top + chartHeight - ((kline.high - minPrice) / priceRange) * chartHeight;
                    const lowY = padding.top + chartHeight - ((kline.low - minPrice) / priceRange) * chartHeight;
                    
                    const isUp = kline.close >= kline.open;
                    const color = isUp ? '#ff4757' : '#2ed573';
                    
                    // 影线
                    ctx.strokeStyle = color;
                    ctx.lineWidth = 1;
                    ctx.beginPath();
                    ctx.moveTo(x, highY);
                    ctx.lineTo(x, lowY);
                    ctx.stroke();
                    
                    // 实体
                    ctx.fillStyle = color;
                    const bodyTop = Math.min(openY, closeY);
                    const bodyHeight = Math.max(1, Math.abs(closeY - openY));
                    ctx.fillRect(x - candleWidth/2, bodyTop, candleWidth, bodyHeight);
                });
            }
            
            drawMA(ctx, klines, padding, chartWidth, chartHeight, minPrice, priceRange) {
                const maData = this.lastIndicatorData.indicators.ma;
                const colors = { ma5: '#ff9800', ma10: '#9c27b0', ma20: '#2196f3', ma60: '#4caf50' };
                
                Object.entries(maData).forEach(([period, values]) => {
                    if (!values) return;
                    
                    ctx.strokeStyle = colors[period] || '#ffffff';
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    
                    let started = false;
                    values.slice(-klines.length).forEach((value, index) => {
                        if (value !== null) {
                            const x = padding.left + (chartWidth / (klines.length - 1)) * index;
                            const y = padding.top + chartHeight - ((value - minPrice) / priceRange) * chartHeight;
                            
                            if (!started) {
                                ctx.moveTo(x, y);
                                started = true;
                            } else {
                                ctx.lineTo(x, y);
                            }
                        }
                    });
                    
                    ctx.stroke();
                });
            }
            
            drawBoll(ctx, klines, padding, chartWidth, chartHeight, minPrice, priceRange) {
                const bollData = this.lastIndicatorData.indicators.boll;
                if (!bollData) return;
                
                // 上轨
                this.drawLine(ctx, klines, bollData.upper, padding, chartWidth, chartHeight, '#ff5722', minPrice, priceRange, [5, 5]);
                
                // 中轨
                this.drawLine(ctx, klines, bollData.middle, padding, chartWidth, chartHeight, '#ffc107', minPrice, priceRange);
                
                // 下轨
                this.drawLine(ctx, klines, bollData.lower, padding, chartWidth, chartHeight, '#ff5722', minPrice, priceRange, [5, 5]);
            }
            
            drawLine(ctx, klines, values, padding, chartWidth, chartHeight, color, minPrice, priceRange, dashPattern = null) {
                if (!values) return;
                
                ctx.strokeStyle = color;
                ctx.lineWidth = 1;
                
                if (dashPattern) {
                    ctx.setLineDash(dashPattern);
                }
                
                ctx.beginPath();
                
                let started = false;
                values.slice(-klines.length).forEach((value, index) => {
                    if (value !== null) {
                        const x = padding.left + (chartWidth / (klines.length - 1)) * index;
                        const y = padding.top + chartHeight - ((value - minPrice) / priceRange) * chartHeight;
                        
                        if (!started) {
                            ctx.moveTo(x, y);
                            started = true;
                        } else {
                            ctx.lineTo(x, y);
                        }
                    }
                });
                
                ctx.stroke();
                
                if (dashPattern) {
                    ctx.setLineDash([]);
                }
            }
            
            drawAxis(ctx, canvas, klines, padding, chartWidth, chartHeight, minPrice, maxPrice) {
                ctx.fillStyle = '#d1d4dc';
                ctx.font = '11px Arial';
                ctx.textAlign = 'right';
                
                // 价格标签
                for (let i = 0; i <= 8; i++) {
                    const price = minPrice + (maxPrice - minPrice) * (1 - i / 8);
                    const y = padding.top + (chartHeight / 8) * i;
                    ctx.fillText(price.toFixed(2), padding.left - 10, y + 4);
                }
                
                // 日期标签
                ctx.textAlign = 'center';
                const step = Math.max(1, Math.floor(klines.length / 6));
                for (let i = 0; i < klines.length; i += step) {
                    const kline = klines[i];
                    const x = padding.left + (chartWidth / (klines.length - 1)) * i;
                    const date = new Date(kline.date);
                    ctx.fillText(`${date.getMonth() + 1}/${date.getDate()}`, x, canvas.height - 20);
                }
            }
            
            drawLegend(ctx, latestKline) {
                ctx.fillStyle = '#d1d4dc';
                ctx.font = '12px Arial';
                ctx.textAlign = 'left';
                
                const y = 20;
                ctx.fillText(`开: ${latestKline.open}`, 20, y);
                ctx.fillText(`高: ${latestKline.high}`, 100, y);
                ctx.fillText(`低: ${latestKline.low}`, 180, y);
                ctx.fillText(`收: ${latestKline.close}`, 260, y);
            }
        }

        const renderer = new DebugChartRenderer();

        function showStatus(message, type = 'info') {
            const statusEl = document.getElementById('status-display');
            const className = type === 'error' ? 'error' : 'success';
            statusEl.innerHTML = `<div class="status ${className}">${message}</div>`;
        }

        async function testBasicChart() {
            const stockCode = document.getElementById('stock-input').value || '000001';
            console.log('=== 开始基础图表测试 ===');
            
            renderer.activeIndicators = [];
            
            if (await renderer.loadData(stockCode)) {
                renderer.renderChart();
                showStatus('✅ 基础图表测试成功', 'success');
            } else {
                showStatus('❌ 基础图表测试失败', 'error');
            }
        }

        async function testWithMA() {
            const stockCode = document.getElementById('stock-input').value || '000001';
            console.log('=== 开始均线测试 ===');
            
            renderer.activeIndicators = ['ma'];
            
            if (await renderer.loadData(stockCode)) {
                renderer.renderChart();
                showStatus('✅ 均线测试成功', 'success');
            } else {
                showStatus('❌ 均线测试失败', 'error');
            }
        }

        async function testWithBoll() {
            const stockCode = document.getElementById('stock-input').value || '000001';
            console.log('=== 开始布林带测试 ===');
            
            renderer.activeIndicators = ['boll'];
            
            if (await renderer.loadData(stockCode)) {
                renderer.renderChart();
                showStatus('✅ 布林带测试成功', 'success');
            } else {
                showStatus('❌ 布林带测试失败', 'error');
            }
        }

        async function testWithMACD() {
            const stockCode = document.getElementById('stock-input').value || '000001';
            console.log('=== 开始MACD测试 ===');
            
            renderer.activeIndicators = ['ma', 'macd'];
            
            if (await renderer.loadData(stockCode)) {
                renderer.renderChart();
                showStatus('✅ MACD测试成功', 'success');
            } else {
                showStatus('❌ MACD测试失败', 'error');
            }
        }

        function clearLog() {
            document.getElementById('debug-log').innerHTML = '';
        }

        // 页面加载时自动测试
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(testBasicChart, 1000);
        });
    </script>
</body>
</html>
