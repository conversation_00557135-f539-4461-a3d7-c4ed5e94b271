<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速修复测试</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .chart-container {
            height: 400px;
            background: var(--bg-tertiary);
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid var(--border-color);
            position: relative;
        }
        .test-controls {
            display: flex;
            gap: 10px;
            margin: 15px 0;
            flex-wrap: wrap;
        }
        .test-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
        }
        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        .status.success {
            background: rgba(76, 175, 80, 0.1);
            border: 1px solid var(--color-up);
            color: var(--color-up);
        }
        .status.error {
            background: rgba(244, 67, 54, 0.1);
            border: 1px solid var(--color-down);
            color: var(--color-down);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center; color: var(--primary-color);">🔧 快速修复测试</h1>
        
        <div style="background: var(--bg-card); border-radius: 12px; padding: 20px; margin: 20px 0;">
            <h3>📊 图表渲染测试</h3>
            
            <div class="test-controls">
                <input type="text" id="stock-code" placeholder="股票代码 (如: 000858)" 
                       style="padding: 10px; background: var(--bg-tertiary); border: 1px solid var(--border-color); border-radius: 6px; color: var(--text-primary);">
                <button class="test-btn" onclick="testChart()">测试图表</button>
                <button class="test-btn" onclick="testAPI()">测试API</button>
                <button class="test-btn" onclick="clearChart()">清空图表</button>
            </div>
            
            <div id="status-display"></div>
            <div id="chart-container" class="chart-container"></div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="index.html" style="color: var(--primary-color); text-decoration: none;">← 返回主应用</a>
        </div>
    </div>

    <script>
        // 超级简化的图表渲染器
        class QuickChartRenderer {
            constructor() {
                this.activeIndicators = ['ma'];
            }
            
            async testAPI(stockCode) {
                try {
                    showStatus('正在测试API...', 'info');
                    
                    const response = await fetch(`http://localhost:5001/api/kline/${stockCode}?period=1d&count=10`);
                    const result = await response.json();
                    
                    if (result.success) {
                        showStatus(`✅ API测试成功，获取到 ${result.data.klines.length} 条K线数据`, 'success');
                        return result.data;
                    } else {
                        throw new Error(result.error || 'API返回失败');
                    }
                } catch (error) {
                    showStatus(`❌ API测试失败: ${error.message}`, 'error');
                    return null;
                }
            }
            
            async renderChart(stockCode) {
                try {
                    showStatus('正在获取数据...', 'info');
                    
                    // 获取K线数据
                    const klineData = await this.testAPI(stockCode);
                    if (!klineData) return;
                    
                    showStatus('正在渲染图表...', 'info');
                    
                    // 渲染图表
                    const container = document.getElementById('chart-container');
                    const containerWidth = container.clientWidth || 800;
                    const containerHeight = 400;
                    
                    container.innerHTML = `<canvas id="test-chart" width="${containerWidth}" height="${containerHeight}" style="width: 100%; height: 100%;"></canvas>`;
                    const canvas = document.getElementById('test-chart');
                    const ctx = canvas.getContext('2d');
                    
                    this.drawChart(ctx, canvas, klineData);
                    
                    showStatus(`✅ 图表渲染成功 (${stockCode})`, 'success');
                    
                } catch (error) {
                    showStatus(`❌ 图表渲染失败: ${error.message}`, 'error');
                    console.error('渲染错误:', error);
                }
            }
            
            drawChart(ctx, canvas, klineData) {
                const klines = klineData.klines;
                const padding = { top: 40, right: 60, bottom: 40, left: 60 };
                const chartWidth = canvas.width - padding.left - padding.right;
                const chartHeight = canvas.height - padding.top - padding.bottom;
                
                // 计算价格范围
                const prices = klines.map(k => [k.high, k.low, k.open, k.close]).flat();
                const maxPrice = Math.max(...prices);
                const minPrice = Math.min(...prices);
                const priceRange = maxPrice - minPrice || 1;
                const priceBuffer = priceRange * 0.1;
                
                const adjustedMax = maxPrice + priceBuffer;
                const adjustedMin = minPrice - priceBuffer;
                const adjustedRange = adjustedMax - adjustedMin;
                
                // 背景
                ctx.fillStyle = '#1e2329';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // 网格
                this.drawGrid(ctx, padding, chartWidth, chartHeight);
                
                // K线
                this.drawKlines(ctx, klines, padding, chartWidth, chartHeight, adjustedMin, adjustedRange);
                
                // 坐标轴
                this.drawAxis(ctx, canvas, klines, padding, chartWidth, chartHeight, adjustedMin, adjustedMax);
                
                // 图例
                this.drawLegend(ctx, klines[klines.length - 1]);
            }
            
            drawGrid(ctx, padding, chartWidth, chartHeight) {
                ctx.strokeStyle = '#2b3139';
                ctx.lineWidth = 1;
                
                // 垂直线
                for (let i = 0; i <= 8; i++) {
                    const x = padding.left + (chartWidth / 8) * i;
                    ctx.beginPath();
                    ctx.moveTo(x, padding.top);
                    ctx.lineTo(x, padding.top + chartHeight);
                    ctx.stroke();
                }
                
                // 水平线
                for (let i = 0; i <= 6; i++) {
                    const y = padding.top + (chartHeight / 6) * i;
                    ctx.beginPath();
                    ctx.moveTo(padding.left, y);
                    ctx.lineTo(padding.left + chartWidth, y);
                    ctx.stroke();
                }
            }
            
            drawKlines(ctx, klines, padding, chartWidth, chartHeight, minPrice, priceRange) {
                const candleWidth = Math.max(4, chartWidth / klines.length * 0.8);
                
                klines.forEach((kline, index) => {
                    const x = padding.left + (chartWidth / (klines.length - 1)) * index;
                    const openY = padding.top + chartHeight - ((kline.open - minPrice) / priceRange) * chartHeight;
                    const closeY = padding.top + chartHeight - ((kline.close - minPrice) / priceRange) * chartHeight;
                    const highY = padding.top + chartHeight - ((kline.high - minPrice) / priceRange) * chartHeight;
                    const lowY = padding.top + chartHeight - ((kline.low - minPrice) / priceRange) * chartHeight;
                    
                    const isUp = kline.close >= kline.open;
                    const color = isUp ? '#ff4757' : '#2ed573';
                    
                    // 影线
                    ctx.strokeStyle = color;
                    ctx.lineWidth = 1;
                    ctx.beginPath();
                    ctx.moveTo(x, highY);
                    ctx.lineTo(x, lowY);
                    ctx.stroke();
                    
                    // 实体
                    ctx.fillStyle = color;
                    const bodyTop = Math.min(openY, closeY);
                    const bodyHeight = Math.max(2, Math.abs(closeY - openY));
                    ctx.fillRect(x - candleWidth/2, bodyTop, candleWidth, bodyHeight);
                });
            }
            
            drawAxis(ctx, canvas, klines, padding, chartWidth, chartHeight, minPrice, maxPrice) {
                ctx.fillStyle = '#d1d4dc';
                ctx.font = '11px Arial';
                ctx.textAlign = 'right';
                
                // 价格标签
                for (let i = 0; i <= 6; i++) {
                    const price = minPrice + (maxPrice - minPrice) * (1 - i / 6);
                    const y = padding.top + (chartHeight / 6) * i;
                    ctx.fillText(price.toFixed(2), padding.left - 10, y + 4);
                }
                
                // 日期标签
                ctx.textAlign = 'center';
                const step = Math.max(1, Math.floor(klines.length / 4));
                for (let i = 0; i < klines.length; i += step) {
                    const kline = klines[i];
                    const x = padding.left + (chartWidth / (klines.length - 1)) * i;
                    const date = new Date(kline.date);
                    ctx.fillText(`${date.getMonth() + 1}/${date.getDate()}`, x, canvas.height - 10);
                }
            }
            
            drawLegend(ctx, latestKline) {
                ctx.fillStyle = '#d1d4dc';
                ctx.font = '12px Arial';
                ctx.textAlign = 'left';
                
                const y = 20;
                ctx.fillText(`开: ${latestKline.open}`, 20, y);
                ctx.fillText(`高: ${latestKline.high}`, 100, y);
                ctx.fillText(`低: ${latestKline.low}`, 180, y);
                ctx.fillText(`收: ${latestKline.close}`, 260, y);
            }
        }

        const renderer = new QuickChartRenderer();

        function showStatus(message, type = 'info') {
            const statusEl = document.getElementById('status-display');
            const className = type === 'error' ? 'error' : 'success';
            statusEl.innerHTML = `<div class="status ${className}">${message}</div>`;
        }

        async function testChart() {
            const stockCode = document.getElementById('stock-code').value || '000858';
            await renderer.renderChart(stockCode);
        }

        async function testAPI() {
            const stockCode = document.getElementById('stock-code').value || '000858';
            await renderer.testAPI(stockCode);
        }

        function clearChart() {
            const container = document.getElementById('chart-container');
            container.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: var(--text-muted);">图表已清空</div>';
            showStatus('图表已清空', 'info');
        }

        // 页面加载时自动测试
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(testChart, 1000);
        });
    </script>
</body>
</html>
