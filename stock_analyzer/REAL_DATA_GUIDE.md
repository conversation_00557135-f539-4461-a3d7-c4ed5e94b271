# 🚀 真实股票数据使用指南

## ✅ 已实现的真实数据功能

### 📊 **多数据源支持**
系统现在支持从以下真实数据源获取股票数据：

1. **新浪财经** (主要数据源)
   - 接口稳定，数据准确
   - 支持A股全市场
   - 实时更新频率高

2. **腾讯财经** (备用数据源)
   - 数据丰富，格式标准
   - 响应速度快
   - 覆盖面广

3. **东方财富** (备用数据源)
   - 官方数据源
   - 数据权威性高
   - 支持详细指标

4. **网易财经** (备用数据源)
   - 数据格式清晰
   - 历史悠久，稳定性好

### 🔄 **智能数据源切换**
- 系统会按优先级尝试各个数据源
- 如果主数据源失败，自动切换到备用源
- 确保数据获取的可靠性

## 🎯 **真实数据特性**

### 📈 **实时价格数据**
- ✅ 当前价格
- ✅ 涨跌额和涨跌幅
- ✅ 开盘价、最高价、最低价
- ✅ 昨日收盘价
- ✅ 成交量和成交额
- ✅ 实时更新时间戳

### 🏢 **支持的股票市场**
- ✅ **上海证券交易所** (6开头股票)
- ✅ **深圳证券交易所** (0开头股票)
- ✅ **创业板** (3开头股票)

### 📱 **数据更新机制**
- **缓存时间**: 5秒（减少API调用频率）
- **自动刷新**: 每3秒更新一次显示
- **手动刷新**: 支持用户手动刷新

## 🔍 **使用方法**

### 1. **搜索股票**
支持多种搜索方式：

```
# 股票代码搜索
000001  # 平安银行
600519  # 贵州茅台
002594  # 比亚迪

# 股票名称搜索
平安银行
贵州茅台
比亚迪
茅台      # 支持简称
平安      # 支持模糊匹配
```

### 2. **查看实时数据**
输入股票代码后，系统会：
1. 验证股票代码格式
2. 从多个数据源获取实时数据
3. 显示完整的股票信息
4. 标注数据来源

### 3. **数据源标识**
在股票信息中会显示数据来源：
- `深A (sina)` - 来自新浪财经
- `沪A (tencent)` - 来自腾讯财经
- `深A (eastmoney)` - 来自东方财富

## 📊 **真实数据示例**

### 平安银行 (000001)
```json
{
  "code": "000001",
  "name": "平安银行",
  "price": 11.84,
  "change": 0.14,
  "changePercent": 1.2,
  "open": 11.7,
  "prevClose": 11.7,
  "high": 11.86,
  "low": 11.67,
  "volume": 1.31,      // 亿股
  "turnover": 15.45,   // 亿元
  "market": "深A",
  "source": "tencent",
  "timestamp": 1750415122
}
```

### 贵州茅台 (600519)
```json
{
  "code": "600519",
  "name": "贵州茅台",
  "price": 1428.66,
  "change": 2.66,
  "changePercent": 0.19,
  "open": 1423.58,
  "prevClose": 1426.0,
  "high": 1441.14,
  "low": 1420.2,
  "volume": 0.03,      // 亿股
  "turnover": 49.91,   // 亿元
  "market": "沪A",
  "source": "tencent",
  "timestamp": 1750415133
}
```

## 🔧 **API接口说明**

### 获取单只股票数据
```bash
GET http://localhost:5001/api/stock/{stock_code}
```

**示例请求:**
```bash
curl "http://localhost:5001/api/stock/000001"
```

**响应格式:**
```json
{
  "success": true,
  "data": {
    "code": "000001",
    "name": "平安银行",
    "price": 11.84,
    "change": 0.14,
    "changePercent": 1.2,
    // ... 其他字段
  }
}
```

### 搜索股票
```bash
GET http://localhost:5001/api/search?q={keyword}
```

**示例:**
```bash
curl "http://localhost:5001/api/search?q=平安银行"
curl "http://localhost:5001/api/search?q=000001"
```

## ⚡ **性能优化**

### 1. **数据缓存**
- API响应缓存5秒
- 减少重复请求
- 提高响应速度

### 2. **智能重试**
- 数据源失败自动切换
- 最多尝试4个数据源
- 确保数据获取成功率

### 3. **异步加载**
- 前端异步请求数据
- 不阻塞用户界面
- 支持加载状态显示

## 🛠️ **故障排除**

### 常见问题

#### 1. **数据获取失败**
**现象**: 显示"获取数据失败"
**解决方案**:
```bash
# 检查API服务状态
curl http://localhost:5001/api/stock/000001

# 重启API服务
python3 api.py
```

#### 2. **数据不是最新的**
**现象**: 价格数据延迟
**原因**: 
- 缓存机制（5秒缓存）
- 数据源本身的延迟
- 非交易时间

#### 3. **某些股票查不到**
**现象**: 返回"股票代码不存在"
**可能原因**:
- 股票代码输入错误
- 股票已退市或停牌
- 数据源暂时不可用

### 调试方法

#### 1. **查看API日志**
```bash
# API服务会输出详细日志
python3 api.py
```

#### 2. **测试数据源**
```bash
# 测试新浪财经
curl "http://hq.sinajs.cn/list=sz000001"

# 测试腾讯财经
curl "http://qt.gtimg.cn/q=sz000001"
```

#### 3. **验证数据格式**
```bash
# 获取格式化的JSON数据
curl "http://localhost:5001/api/stock/000001" | python3 -m json.tool
```

## 📈 **数据准确性说明**

### 1. **数据来源权威性**
- 所有数据源都是知名财经网站
- 数据经过多重验证
- 与官方数据基本一致

### 2. **更新频率**
- **交易时间**: 实时更新（秒级）
- **非交易时间**: 显示最后交易数据
- **节假日**: 显示上一交易日数据

### 3. **数据延迟**
- **新浪财经**: 几乎实时
- **腾讯财经**: 1-3秒延迟
- **东方财富**: 1-5秒延迟
- **网易财经**: 3-10秒延迟

## ⚠️ **免责声明**

1. **数据仅供参考**: 不构成投资建议
2. **延迟风险**: 数据可能存在延迟
3. **准确性**: 尽力保证准确性，但不承担责任
4. **投资风险**: 投资有风险，决策需谨慎

## 🔗 **相关链接**

- **主应用**: http://localhost:8080
- **数据演示**: http://localhost:8080/demo.html
- **颜色测试**: http://localhost:8080/test.html
- **API文档**: http://localhost:5001

---

**现在你拥有了一个真正可用的股票数据系统！** 🎉📈
