<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交互功能测试</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            background: var(--bg-card);
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        .chart-container {
            height: 400px;
            background: var(--bg-tertiary);
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid var(--border-color);
            position: relative;
        }
        .controls-demo {
            display: flex;
            gap: 15px;
            margin: 15px 0;
            flex-wrap: wrap;
        }
        .control-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        .control-group label {
            font-size: 12px;
            color: var(--text-secondary);
            font-weight: bold;
        }
        .btn-group {
            display: flex;
            gap: 5px;
        }
        .demo-btn {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            color: var(--text-secondary);
            border-radius: 6px;
            padding: 8px 16px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .demo-btn.active,
        .demo-btn:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        .test-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        .status.success {
            background: rgba(76, 175, 80, 0.1);
            border: 1px solid var(--color-up);
            color: var(--color-up);
        }
        .status.info {
            background: rgba(33, 150, 243, 0.1);
            border: 1px solid var(--primary-color);
            color: var(--primary-color);
        }
        .interaction-hint {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid #ffc107;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            color: #ffc107;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center; color: var(--primary-color);">🖱️ 交互功能测试</h1>
        
        <div class="test-section">
            <h3>🎮 按钮交互测试</h3>
            
            <div class="controls-demo">
                <div class="control-group">
                    <label>图表类型</label>
                    <div class="btn-group">
                        <button class="demo-btn chart-type-btn active" data-type="kline">K线</button>
                        <button class="demo-btn chart-type-btn" data-type="timeline">分时</button>
                    </div>
                </div>
                
                <div class="control-group">
                    <label>时间周期</label>
                    <div class="btn-group">
                        <button class="demo-btn chart-btn active" data-period="1d">日K</button>
                        <button class="demo-btn chart-btn" data-period="1w">周K</button>
                        <button class="demo-btn chart-btn" data-period="1m">月K</button>
                        <button class="demo-btn chart-btn" data-period="5m">5分</button>
                    </div>
                </div>
                
                <div class="control-group">
                    <label>技术指标</label>
                    <div class="btn-group">
                        <button class="demo-btn indicator-btn active" data-indicator="ma">均线</button>
                        <button class="demo-btn indicator-btn" data-indicator="boll">布林带</button>
                        <button class="demo-btn indicator-btn" data-indicator="macd">MACD</button>
                        <button class="demo-btn indicator-btn" data-indicator="kdj">KDJ</button>
                    </div>
                </div>
            </div>
            
            <div style="margin: 15px 0;">
                <input type="text" id="stock-input" placeholder="股票代码 (如: 000858)" 
                       style="padding: 10px; background: var(--bg-tertiary); border: 1px solid var(--border-color); border-radius: 6px; color: var(--text-primary); margin-right: 10px;">
                <button class="test-btn" onclick="loadTestStock()">加载股票</button>
                <button class="test-btn" onclick="testButtonEvents()">测试按钮事件</button>
            </div>
            
            <div class="interaction-hint">
                💡 <strong>交互提示</strong>：在图表上移动鼠标查看十字光标和K线详细信息
            </div>
            
            <div id="chart-container" class="chart-container"></div>
            <div id="status-display"></div>
        </div>

        <div class="test-section">
            <h3>📊 功能状态检查</h3>
            <div id="function-status">
                <div class="status info">正在检查功能状态...</div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="index.html" style="color: var(--primary-color); text-decoration: none;">← 返回主应用</a>
        </div>
    </div>

    <script src="script.js"></script>
    <script>
        // 创建测试实例
        let testAnalyzer = null;
        
        function showStatus(message, type = 'info') {
            const statusEl = document.getElementById('status-display');
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            statusEl.innerHTML = `<div class="status ${className}">${message}</div>`;
        }
        
        function showFunctionStatus(message, type = 'info') {
            const statusEl = document.getElementById('function-status');
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            statusEl.innerHTML = `<div class="status ${className}">${message}</div>`;
        }
        
        async function loadTestStock() {
            const stockCode = document.getElementById('stock-input').value || '000858';
            
            if (!testAnalyzer) {
                // 创建测试分析器实例
                testAnalyzer = new StockAnalyzer();
                
                // 重新绑定到测试容器
                testAnalyzer.container = document.getElementById('chart-container');
            }
            
            showStatus(`正在加载股票 ${stockCode}...`, 'info');
            
            try {
                // 设置当前股票
                testAnalyzer.currentStock = stockCode;
                
                // 更新图表
                await testAnalyzer.updateChart(testAnalyzer.currentPeriod);
                
                showStatus(`✅ 股票 ${stockCode} 加载成功`, 'success');
            } catch (error) {
                showStatus(`❌ 股票加载失败: ${error.message}`, 'error');
            }
        }
        
        function testButtonEvents() {
            showFunctionStatus('正在测试按钮事件...', 'info');
            
            let results = [];
            
            // 测试图表类型按钮
            const chartTypeBtns = document.querySelectorAll('.chart-type-btn');
            results.push(`图表类型按钮: ${chartTypeBtns.length} 个`);
            
            // 测试周期按钮
            const chartBtns = document.querySelectorAll('.chart-btn');
            results.push(`周期按钮: ${chartBtns.length} 个`);
            
            // 测试技术指标按钮
            const indicatorBtns = document.querySelectorAll('.indicator-btn');
            results.push(`技术指标按钮: ${indicatorBtns.length} 个`);
            
            // 检查事件绑定
            let hasEvents = true;
            chartTypeBtns.forEach(btn => {
                if (!btn.onclick && !btn.addEventListener) {
                    hasEvents = false;
                }
            });
            
            results.push(`事件绑定状态: ${hasEvents ? '✅ 正常' : '❌ 异常'}`);
            
            // 检查分析器实例
            if (testAnalyzer) {
                results.push(`分析器实例: ✅ 已创建`);
                results.push(`当前股票: ${testAnalyzer.currentStock || '未设置'}`);
                results.push(`当前周期: ${testAnalyzer.currentPeriod}`);
                results.push(`活跃指标: ${testAnalyzer.activeIndicators.join(', ')}`);
            } else {
                results.push(`分析器实例: ❌ 未创建`);
            }
            
            showFunctionStatus(results.join('<br>'), 'success');
        }
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('交互测试页面加载完成');
            
            // 延迟初始化
            setTimeout(() => {
                testButtonEvents();
                loadTestStock();
            }, 1000);
        });
        
        // 手动绑定按钮事件（作为备用）
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('chart-type-btn')) {
                console.log('手动处理图表类型切换:', e.target.dataset.type);
                document.querySelectorAll('.chart-type-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                
                if (testAnalyzer) {
                    testAnalyzer.currentChartType = e.target.dataset.type;
                    testAnalyzer.updateChart(testAnalyzer.currentPeriod);
                }
            }
            
            if (e.target.classList.contains('chart-btn')) {
                console.log('手动处理周期切换:', e.target.dataset.period);
                document.querySelectorAll('.chart-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                
                if (testAnalyzer) {
                    testAnalyzer.currentPeriod = e.target.dataset.period;
                    testAnalyzer.updateChart(testAnalyzer.currentPeriod);
                }
            }
            
            if (e.target.classList.contains('indicator-btn')) {
                const indicator = e.target.dataset.indicator;
                console.log('手动处理指标切换:', indicator);
                
                if (e.target.classList.contains('active')) {
                    e.target.classList.remove('active');
                    if (testAnalyzer) {
                        testAnalyzer.activeIndicators = testAnalyzer.activeIndicators.filter(i => i !== indicator);
                    }
                } else {
                    e.target.classList.add('active');
                    if (testAnalyzer && !testAnalyzer.activeIndicators.includes(indicator)) {
                        testAnalyzer.activeIndicators.push(indicator);
                    }
                }
                
                if (testAnalyzer) {
                    testAnalyzer.updateChart(testAnalyzer.currentPeriod);
                }
            }
        });
    </script>
</body>
</html>
