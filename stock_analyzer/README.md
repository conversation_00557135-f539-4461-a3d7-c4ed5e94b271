# 智能股票分析系统

一个现代化的中国股票实时查看和分析web应用，数据来源于东方财富网，具有专业的股票操作界面风格和智能分析功能。

## 🌟 主要特性

### 📊 实时数据
- **实时股价**: 从东方财富网获取实时股票数据
- **市场指数**: 上证指数、深证成指、创业板指实时更新
- **数据流**: 实时数据流显示，模拟专业交易终端

### 🎨 现代化界面
- **暗色主题**: 专业的股票交易界面风格
- **响应式设计**: 支持桌面和移动设备
- **流畅动画**: 现代化的交互体验
- **专业配色**: 红涨绿跌的中国股市配色方案

### 🔍 智能搜索
- **股票搜索**: 支持股票代码和名称搜索
- **快速选择**: 热门股票快速访问
- **自动补全**: 智能搜索建议

### 📈 数据可视化
- **价格走势图**: 基于Chart.js的交互式图表
- **多时间周期**: 日K、周K、月K、季K、年K线图
- **技术指标**: 支持多种技术分析指标

### 🤖 智能分析
- **技术分析**: 基于技术指标的智能分析
- **基本面分析**: 公司基本面评估
- **市场情绪**: 资金流向和市场情绪分析
- **综合评分**: 多维度综合评分系统

### 📱 功能模块
- **热门股票**: 涨幅榜、跌幅榜、成交量榜
- **实时更新**: 自动刷新股价和市场数据
- **数据缓存**: 智能缓存减少API调用

## 🚀 快速开始

### 方法一：一键启动（推荐）

```bash
# 进入项目目录
cd stock_analyzer

# 运行启动脚本
./start.sh
```

启动脚本会自动：
1. 检查Python环境
2. 安装必要依赖
3. 启动后端API服务
4. 启动前端web服务
5. 打开浏览器访问应用

### 方法二：手动启动

#### 1. 安装依赖
```bash
# 安装Python依赖
pip3 install flask flask-cors requests
```

#### 2. 启动后端API
```bash
# 启动API服务
python3 api.py
```

#### 3. 启动前端服务
```bash
# 使用Python内置服务器
python3 -m http.server 8080

# 或使用Node.js serve
npx serve -p 8080

# 或直接用浏览器打开 index.html
```

#### 4. 访问应用
打开浏览器访问：http://localhost:8080

## 📁 项目结构

```
stock_analyzer/
├── index.html          # 主页面
├── style.css           # 样式文件
├── script.js           # 前端JavaScript
├── api.py              # 后端API服务
├── start.sh            # 一键启动脚本
└── README.md           # 项目说明
```

## 🔌 API接口

### 获取股票数据
```
GET /api/stock/<stock_code>
```

示例：
```bash
curl http://localhost:5000/api/stock/000001
```

响应：
```json
{
  "success": true,
  "data": {
    "code": "000001",
    "name": "平安银行",
    "price": 13.45,
    "change": 0.23,
    "changePercent": 1.74,
    "open": 13.22,
    "prevClose": 13.22,
    "high": 13.56,
    "low": 13.18,
    "volume": 123000000,
    "turnover": 1645000000,
    "market": "深A",
    "timestamp": 1640995200
  }
}
```

### 获取市场指数
```
GET /api/market
```

### 获取热门股票
```
GET /api/hot-stocks?type=gainers|losers|volume
```

### 搜索股票
```
GET /api/search?q=<keyword>
```

## 🎯 使用说明

### 1. 搜索股票
- 在搜索框中输入股票代码（如：000001）或股票名称（如：平安银行）
- 点击搜索按钮或按回车键
- 或点击快速搜索按钮选择热门股票

### 2. 查看股票信息
- **基本信息**: 股票名称、代码、市场
- **价格信息**: 当前价、涨跌额、涨跌幅
- **统计数据**: 开盘价、最高价、最低价、成交量等

### 3. 分析功能
- **价格走势图**: 选择不同时间周期查看K线图
- **智能分析**: 查看技术分析、基本面分析、市场情绪
- **综合评分**: 多维度评分参考

### 4. 实时数据
- 系统每3秒自动更新股价数据
- 实时数据流显示市场动态
- 热门股票列表实时更新

## ⚙️ 配置说明

### 数据源配置
项目默认从东方财富网获取数据，如果网络访问受限，系统会自动切换到模拟数据模式。

### 缓存设置
- API数据缓存时间：10秒
- 前端数据更新间隔：3秒
- 图表数据缓存：5分钟

### 自定义配置
可以在 `api.py` 中修改以下配置：
```python
self.cache_timeout = 10  # 缓存时间（秒）
```

可以在 `script.js` 中修改：
```javascript
this.updateInterval = 3000;  # 更新间隔（毫秒）
```

## 🛠️ 技术栈

### 前端
- **HTML5**: 语义化标签
- **CSS3**: 现代化样式，CSS Grid/Flexbox
- **JavaScript ES6+**: 原生JavaScript，无框架依赖
- **Chart.js**: 图表可视化
- **Font Awesome**: 图标库

### 后端
- **Python 3.7+**: 后端语言
- **Flask**: Web框架
- **Requests**: HTTP客户端
- **Flask-CORS**: 跨域支持

### 数据源
- **东方财富网API**: 实时股票数据
- **模拟数据**: 备用数据源

## 📱 响应式设计

应用支持多种设备：
- **桌面端**: 1200px+ 完整功能
- **平板端**: 768px-1200px 适配布局
- **手机端**: <768px 移动优化

## 🔒 注意事项

### 数据使用
- 数据仅供参考，不构成投资建议
- 实时数据可能有延迟
- 投资有风险，决策需谨慎

### 技术限制
- 需要网络连接获取实时数据
- 部分功能依赖现代浏览器
- API调用频率有限制

### 法律声明
- 本项目仅用于技术学习和演示
- 不承担任何投资损失责任
- 请遵守相关法律法规

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

### 开发环境
```bash
# 克隆项目
git clone <repository-url>
cd stock_analyzer

# 安装依赖
pip3 install -r requirements.txt

# 启动开发服务器
python3 api.py
```

### 提交规范
- 功能开发：`feat: 添加新功能`
- 问题修复：`fix: 修复bug`
- 文档更新：`docs: 更新文档`
- 样式调整：`style: 样式优化`

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 📞 联系方式

- 项目地址：[GitHub Repository]
- 问题反馈：[Issues]
- 邮箱：<EMAIL>

---

**免责声明**: 本项目提供的股票数据和分析仅供参考，不构成投资建议。投资有风险，入市需谨慎。
