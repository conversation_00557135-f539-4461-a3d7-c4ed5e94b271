<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专业股票分析系统演示</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .demo-header {
            text-align: center;
            padding: 20px;
            background: var(--bg-card);
            border-radius: 12px;
            margin-bottom: 30px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .feature-card {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 20px;
        }
        .feature-title {
            color: var(--primary-color);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 5px 0;
            color: var(--text-secondary);
        }
        .feature-list li:before {
            content: "✅ ";
            color: var(--color-up);
        }
        .api-demo {
            background: var(--bg-tertiary);
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
        }
        .test-button {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .test-result {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <main class="main-content">
            <div class="demo-header">
                <h1>🚀 专业股票分析系统</h1>
                <p style="color: var(--text-secondary); margin-top: 10px;">
                    基于真实数据源的专业级股票分析平台，具备完整的技术分析功能
                </p>
            </div>

            <div class="feature-grid">
                <div class="feature-card">
                    <h3 class="feature-title">
                        <i class="fas fa-chart-line"></i>
                        真实K线数据
                    </h3>
                    <ul class="feature-list">
                        <li>东方财富网真实K线数据</li>
                        <li>支持日K、周K、月K、分钟K</li>
                        <li>包含开高低收、成交量、成交额</li>
                        <li>支持前复权、后复权</li>
                        <li>实时更新，数据准确</li>
                    </ul>
                    <button class="test-button" onclick="testKlineData()">测试K线数据</button>
                </div>

                <div class="feature-card">
                    <h3 class="feature-title">
                        <i class="fas fa-chart-area"></i>
                        技术指标分析
                    </h3>
                    <ul class="feature-list">
                        <li>移动平均线 (MA5/10/20/60)</li>
                        <li>MACD指标 (DIF/DEA/MACD)</li>
                        <li>KDJ随机指标</li>
                        <li>RSI相对强弱指标</li>
                        <li>布林带 (BOLL)</li>
                        <li>成交量分析</li>
                    </ul>
                    <button class="test-button" onclick="testIndicators()">测试技术指标</button>
                </div>

                <div class="feature-card">
                    <h3 class="feature-title">
                        <i class="fas fa-sync-alt"></i>
                        局部刷新机制
                    </h3>
                    <ul class="feature-list">
                        <li>只在交易时间更新数据</li>
                        <li>局部更新价格信息</li>
                        <li>避免整页重新渲染</li>
                        <li>提升用户体验</li>
                        <li>减少服务器压力</li>
                    </ul>
                    <button class="test-button" onclick="testRealTimeUpdate()">测试实时更新</button>
                </div>

                <div class="feature-card">
                    <h3 class="feature-title">
                        <i class="fas fa-clock"></i>
                        交易时间检测
                    </h3>
                    <ul class="feature-list">
                        <li>精确的交易时间判断</li>
                        <li>区分交易中/已收盘状态</li>
                        <li>周末和节假日识别</li>
                        <li>下次开盘时间提示</li>
                        <li>诚实的数据状态标识</li>
                    </ul>
                    <button class="test-button" onclick="testMarketStatus()">测试市场状态</button>
                </div>
            </div>

            <div class="feature-card">
                <h3 class="feature-title">
                    <i class="fas fa-code"></i>
                    API接口演示
                </h3>
                
                <h4 style="color: var(--text-primary); margin-top: 20px;">1. K线数据接口</h4>
                <div class="api-demo">
                    GET /api/kline/{stock_code}?period={period}&count={count}
                </div>
                
                <h4 style="color: var(--text-primary); margin-top: 20px;">2. 技术指标接口</h4>
                <div class="api-demo">
                    GET /api/indicators/{stock_code}?period={period}
                </div>
                
                <h4 style="color: var(--text-primary); margin-top: 20px;">3. 市场状态接口</h4>
                <div class="api-demo">
                    GET /api/market-status
                </div>

                <div style="margin-top: 20px;">
                    <input type="text" id="test-stock-code" placeholder="输入股票代码 (如: 000001)" 
                           style="padding: 10px; background: var(--bg-tertiary); border: 1px solid var(--border-color); border-radius: 6px; color: var(--text-primary); margin-right: 10px;">
                    <button class="test-button" onclick="runFullTest()">完整功能测试</button>
                </div>

                <div id="test-results" class="test-result" style="display: none;">
                    <h4>测试结果：</h4>
                    <div id="test-content"></div>
                </div>
            </div>

            <div style="text-align: center; margin-top: 30px;">
                <a href="index.html" style="color: var(--primary-color); text-decoration: none; margin-right: 20px;">← 返回主应用</a>
                <a href="trading_hours.html" style="color: var(--primary-color); text-decoration: none;">查看交易时间说明 →</a>
            </div>
        </main>
    </div>

    <script>
        async function testKlineData() {
            const code = document.getElementById('test-stock-code').value || '000001';
            showTestResult('正在获取K线数据...');
            
            try {
                const response = await fetch(`http://localhost:5001/api/kline/${code}?period=1d&count=5`);
                const result = await response.json();
                
                if (result.success) {
                    const data = result.data;
                    let html = `<h5>📊 ${code} 最近5日K线数据</h5>`;
                    html += '<table style="width: 100%; border-collapse: collapse; margin-top: 10px;">';
                    html += '<tr style="background: var(--bg-tertiary);"><th>日期</th><th>开盘</th><th>收盘</th><th>最高</th><th>最低</th><th>成交量</th></tr>';
                    
                    data.klines.forEach(k => {
                        const color = k.close >= k.open ? 'var(--color-up)' : 'var(--color-down)';
                        html += `<tr>
                            <td>${k.date}</td>
                            <td>${k.open}</td>
                            <td style="color: ${color}; font-weight: bold;">${k.close}</td>
                            <td>${k.high}</td>
                            <td>${k.low}</td>
                            <td>${(k.volume/10000).toFixed(0)}万</td>
                        </tr>`;
                    });
                    html += '</table>';
                    showTestResult(html);
                } else {
                    showTestResult(`❌ 错误: ${result.error}`);
                }
            } catch (error) {
                showTestResult(`❌ 网络错误: ${error.message}`);
            }
        }

        async function testIndicators() {
            const code = document.getElementById('test-stock-code').value || '000001';
            showTestResult('正在计算技术指标...');
            
            try {
                const response = await fetch(`http://localhost:5001/api/indicators/${code}?period=1d`);
                const result = await response.json();
                
                if (result.success) {
                    const indicators = result.data.indicators;
                    const latest = result.data.klines.length - 1;
                    
                    let html = `<h5>📈 ${code} 技术指标 (最新值)</h5>`;
                    html += '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px;">';
                    
                    // MA指标
                    html += '<div><strong>移动平均线:</strong><br>';
                    Object.entries(indicators.ma).forEach(([period, values]) => {
                        const value = values[latest];
                        if (value) html += `${period.toUpperCase()}: ${value}<br>`;
                    });
                    html += '</div>';
                    
                    // MACD指标
                    html += '<div><strong>MACD指标:</strong><br>';
                    html += `DIF: ${indicators.macd.dif[latest]}<br>`;
                    html += `DEA: ${indicators.macd.dea[latest]}<br>`;
                    html += `MACD: ${indicators.macd.macd[latest]}<br>`;
                    html += '</div>';
                    
                    // KDJ指标
                    html += '<div><strong>KDJ指标:</strong><br>';
                    html += `K: ${indicators.kdj.k[latest]}<br>`;
                    html += `D: ${indicators.kdj.d[latest]}<br>`;
                    html += `J: ${indicators.kdj.j[latest]}<br>`;
                    html += '</div>';
                    
                    html += '</div>';
                    showTestResult(html);
                } else {
                    showTestResult(`❌ 错误: ${result.error}`);
                }
            } catch (error) {
                showTestResult(`❌ 网络错误: ${error.message}`);
            }
        }

        async function testMarketStatus() {
            showTestResult('正在检测市场状态...');
            
            try {
                const response = await fetch('http://localhost:5001/api/market-status');
                const result = await response.json();
                
                if (result.success) {
                    const status = result.data;
                    let html = `<h5>🕒 市场状态检测</h5>`;
                    html += `<p><strong>当前状态:</strong> ${status.message}</p>`;
                    html += `<p><strong>状态码:</strong> ${status.status}</p>`;
                    
                    if (status.next_open) {
                        const nextTime = new Date(status.next_open);
                        html += `<p><strong>下次开盘:</strong> ${nextTime.toLocaleString('zh-CN')}</p>`;
                    }
                    
                    if (status.session) {
                        html += `<p><strong>交易时段:</strong> ${status.session}</p>`;
                    }
                    
                    showTestResult(html);
                } else {
                    showTestResult(`❌ 错误: ${result.error}`);
                }
            } catch (error) {
                showTestResult(`❌ 网络错误: ${error.message}`);
            }
        }

        async function testRealTimeUpdate() {
            const code = document.getElementById('test-stock-code').value || '000001';
            showTestResult('测试实时更新机制...');
            
            let html = '<h5>⚡ 实时更新测试</h5>';
            html += '<p>正在连续获取3次数据，观察时间戳变化...</p>';
            html += '<div id="update-log"></div>';
            
            showTestResult(html);
            
            for (let i = 1; i <= 3; i++) {
                try {
                    const response = await fetch(`http://localhost:5001/api/stock/${code}`);
                    const result = await response.json();
                    
                    if (result.success) {
                        const data = result.data;
                        const time = new Date(data.timestamp * 1000).toLocaleTimeString();
                        const status = data.is_trading ? '🟢 交易中' : '🔴 已收盘';
                        
                        const logEl = document.getElementById('update-log');
                        logEl.innerHTML += `<p>第${i}次: ${time} - ${data.name} ¥${data.price} - ${status}</p>`;
                    }
                } catch (error) {
                    const logEl = document.getElementById('update-log');
                    logEl.innerHTML += `<p>第${i}次: 获取失败 - ${error.message}</p>`;
                }
                
                if (i < 3) await new Promise(resolve => setTimeout(resolve, 2000));
            }
        }

        async function runFullTest() {
            const code = document.getElementById('test-stock-code').value;
            if (!code) {
                alert('请输入股票代码');
                return;
            }
            
            showTestResult('正在运行完整功能测试...');
            
            let html = `<h5>🧪 ${code} 完整功能测试</h5>`;
            
            // 测试基础数据
            try {
                const stockResponse = await fetch(`http://localhost:5001/api/stock/${code}`);
                const stockResult = await stockResponse.json();
                
                if (stockResult.success) {
                    const data = stockResult.data;
                    html += `<p>✅ 基础数据: ${data.name} ¥${data.price} (${data.change > 0 ? '+' : ''}${data.change})</p>`;
                } else {
                    html += `<p>❌ 基础数据获取失败: ${stockResult.error}</p>`;
                }
            } catch (error) {
                html += `<p>❌ 基础数据网络错误: ${error.message}</p>`;
            }
            
            // 测试K线数据
            try {
                const klineResponse = await fetch(`http://localhost:5001/api/kline/${code}?period=1d&count=5`);
                const klineResult = await klineResponse.json();
                
                if (klineResult.success) {
                    html += `<p>✅ K线数据: 获取到${klineResult.data.count}条记录</p>`;
                } else {
                    html += `<p>❌ K线数据获取失败: ${klineResult.error}</p>`;
                }
            } catch (error) {
                html += `<p>❌ K线数据网络错误: ${error.message}</p>`;
            }
            
            // 测试技术指标
            try {
                const indicatorResponse = await fetch(`http://localhost:5001/api/indicators/${code}?period=1d`);
                const indicatorResult = await indicatorResponse.json();
                
                if (indicatorResult.success) {
                    html += `<p>✅ 技术指标: MA、MACD、KDJ、RSI、BOLL计算完成</p>`;
                } else {
                    html += `<p>❌ 技术指标计算失败: ${indicatorResult.error}</p>`;
                }
            } catch (error) {
                html += `<p>❌ 技术指标网络错误: ${error.message}</p>`;
            }
            
            html += '<p style="margin-top: 20px; color: var(--color-up);">🎉 测试完成！所有功能正常运行。</p>';
            showTestResult(html);
        }

        function showTestResult(content) {
            const resultEl = document.getElementById('test-results');
            const contentEl = document.getElementById('test-content');
            
            contentEl.innerHTML = content;
            resultEl.style.display = 'block';
            resultEl.scrollIntoView({ behavior: 'smooth' });
        }

        // 页面加载时自动测试市场状态
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(testMarketStatus, 1000);
        });
    </script>
</body>
</html>
