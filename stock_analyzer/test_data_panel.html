<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 数据面板测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
            margin: 0;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
        }
        .test-panel {
            background: #333;
            padding: 15px;
            margin: 15px 0;
            border-radius: 6px;
            border-left: 4px solid #2196f3;
        }
        .btn {
            background: #4caf50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #45a049; }
        .data-display {
            background: #444;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .macd-panel {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid #ffc107;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .macd-values {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 8px;
            margin-top: 8px;
        }
        .value-item {
            background: #555;
            padding: 5px;
            border-radius: 3px;
            text-align: center;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 数据面板测试工具</h1>
        <p>测试MACD数据在数据面板中的显示效果</p>
        
        <button class="btn" onclick="loadAndTestData()">🔄 加载并测试数据</button>
        <button class="btn" onclick="testSpecificIndex()">🎯 测试特定索引</button>
        <button class="btn" onclick="simulateDataPanel()">📊 模拟数据面板</button>
        
        <div class="test-panel">
            <h3>📡 数据加载状态</h3>
            <div id="loadStatus"></div>
        </div>
        
        <div class="test-panel">
            <h3>🎯 索引测试结果</h3>
            <div id="indexTest"></div>
        </div>
        
        <div class="test-panel">
            <h3>📊 模拟数据面板</h3>
            <div id="simulatedPanel"></div>
        </div>
    </div>

    <script>
        let testData = null;

        function addLog(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `data-display ${type}`;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            container.appendChild(div);
        }

        async function loadAndTestData() {
            addLog('loadStatus', '🔄 开始加载数据...', 'info');
            
            try {
                const response = await fetch('http://localhost:5001/api/indicators/000001?period=1d&indicators=macd,kdj&mas=5,10,20&count=100');
                const result = await response.json();
                
                if (result.success) {
                    testData = result.data;
                    addLog('loadStatus', '✅ 数据加载成功', 'success');
                    
                    const indicators = testData.indicators;
                    addLog('loadStatus', `📊 可用指标: ${Object.keys(indicators).join(', ')}`, 'info');
                    
                    if (indicators.macd) {
                        const macdData = indicators.macd;
                        addLog('loadStatus', `📊 MACD数据: DIF(${macdData.dif.length}) DEA(${macdData.dea.length}) MACD(${macdData.macd.length})`, 'info');
                        
                        // 统计有效数据
                        const validMacd = macdData.macd.filter(v => v !== null).length;
                        const validDif = macdData.dif.filter(v => v !== null).length;
                        const validDea = macdData.dea.filter(v => v !== null).length;
                        
                        addLog('loadStatus', `📊 有效数据: DIF(${validDif}) DEA(${validDea}) MACD(${validMacd})`, 'info');
                        
                        // 找到第一个有效数据的位置
                        let firstValidIndex = -1;
                        for (let i = 0; i < macdData.macd.length; i++) {
                            if (macdData.macd[i] !== null) {
                                firstValidIndex = i;
                                break;
                            }
                        }
                        
                        if (firstValidIndex >= 0) {
                            addLog('loadStatus', `📊 第一个有效MACD值在索引: ${firstValidIndex}`, 'info');
                        }
                        
                    } else {
                        addLog('loadStatus', '❌ 未找到MACD数据', 'error');
                    }
                    
                } else {
                    addLog('loadStatus', `❌ 数据加载失败: ${result.error}`, 'error');
                }
                
            } catch (error) {
                addLog('loadStatus', `❌ 网络错误: ${error.message}`, 'error');
            }
        }

        function testSpecificIndex() {
            if (!testData) {
                addLog('indexTest', '❌ 请先加载数据', 'error');
                return;
            }
            
            addLog('indexTest', '🎯 开始测试特定索引...', 'info');
            
            const macdData = testData.indicators.macd;
            const testIndices = [25, 29, 30, 50, 99];
            
            testIndices.forEach(index => {
                if (index < macdData.dif.length) {
                    const dif = macdData.dif[index];
                    const dea = macdData.dea[index];
                    const macd = macdData.macd[index];
                    
                    const difStr = dif !== null ? dif.toFixed(4) : 'null';
                    const deaStr = dea !== null ? dea.toFixed(4) : 'null';
                    const macdStr = macd !== null ? macd.toFixed(4) : 'null';
                    
                    const hasData = dif !== null && dea !== null && macd !== null;
                    addLog('indexTest', `索引${index}: DIF=${difStr} DEA=${deaStr} MACD=${macdStr} ${hasData ? '✅' : '⚠️'}`, hasData ? 'success' : 'warning');
                } else {
                    addLog('indexTest', `索引${index}: 超出范围 (最大${macdData.dif.length - 1})`, 'error');
                }
            });
        }

        function simulateDataPanel() {
            if (!testData) {
                addLog('simulatedPanel', '❌ 请先加载数据', 'error');
                return;
            }
            
            addLog('simulatedPanel', '📊 模拟数据面板显示...', 'info');
            
            const container = document.getElementById('simulatedPanel');
            
            // 模拟前端的getIndicatorValueAtIndex函数
            function getIndicatorValueAtIndex(values, index) {
                if (!values || !Array.isArray(values)) return '--';
                if (index < 0 || index >= values.length) return '--';
                const value = values[index];
                if (value === null || value === undefined) return '--';
                if (isNaN(value)) return '--';
                return typeof value === 'number' ? value.toFixed(4) : value;
            }
            
            // 测试不同索引的显示效果
            const testIndices = [25, 29, 30, 50, 99];
            const macdData = testData.indicators.macd;
            
            testIndices.forEach(index => {
                const macdValue = getIndicatorValueAtIndex(macdData.macd, index);
                const difValue = getIndicatorValueAtIndex(macdData.dif, index);
                const deaValue = getIndicatorValueAtIndex(macdData.dea, index);
                
                const hasValidData = macdValue !== '--' || difValue !== '--' || deaValue !== '--';
                
                const panelHtml = `
                    <div class="macd-panel">
                        <div style="color: #ffc107; font-weight: bold; margin-bottom: 4px;">
                            MACD ${hasValidData ? '✅' : '⏳'} (索引:${index})
                        </div>
                        <div class="macd-values">
                            <div class="value-item">DIF: <span style="color: #ffc107;">${difValue}</span></div>
                            <div class="value-item">DEA: <span style="color: #2196f3;">${deaValue}</span></div>
                            <div class="value-item">MACD: <span style="color: ${macdValue !== '--' && parseFloat(macdValue) >= 0 ? '#ff4757' : '#2ed573'};">${macdValue}</span></div>
                        </div>
                        ${!hasValidData ? '<div style="color: #ff9800; font-size: 9px; margin-top: 2px;">⏳ 数据计算中...</div>' : ''}
                    </div>
                `;
                
                container.innerHTML += panelHtml;
            });
        }

        // 页面加载完成后自动加载数据
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(loadAndTestData, 1000);
        });
    </script>
</body>
</html>
