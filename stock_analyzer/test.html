<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>颜色测试</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div style="padding: 20px; background: var(--bg-primary); color: var(--text-primary);">
        <h2>中国股市颜色测试</h2>
        <div style="margin: 20px 0;">
            <div style="color: var(--color-up); font-size: 24px; margin: 10px 0;">
                ↑ 上涨 +2.35% (红色)
            </div>
            <div style="color: var(--color-down); font-size: 24px; margin: 10px 0;">
                ↓ 下跌 -1.28% (绿色)
            </div>
            <div style="color: var(--color-neutral); font-size: 24px; margin: 10px 0;">
                → 平盘 0.00% (灰色)
            </div>
        </div>
        
        <h3>股票示例</h3>
        <div style="background: var(--bg-card); padding: 15px; border-radius: 8px; margin: 10px 0;">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <span>平安银行 (000001)</span>
                <div>
                    <span style="color: var(--color-up); font-weight: bold;">13.45</span>
                    <span style="color: var(--color-up); margin-left: 10px;">+0.23 (+1.74%)</span>
                </div>
            </div>
        </div>
        
        <div style="background: var(--bg-card); padding: 15px; border-radius: 8px; margin: 10px 0;">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <span>万科A (000002)</span>
                <div>
                    <span style="color: var(--color-down); font-weight: bold;">8.15</span>
                    <span style="color: var(--color-down); margin-left: 10px;">-0.12 (-1.45%)</span>
                </div>
            </div>
        </div>
        
        <p style="margin-top: 30px; color: var(--text-muted);">
            ✅ 红色表示上涨 (符合中国股市习惯)<br>
            ✅ 绿色表示下跌 (符合中国股市习惯)
        </p>
        
        <a href="index.html" style="color: var(--primary-color); text-decoration: none;">
            ← 返回主页面
        </a>
    </div>
</body>
</html>
