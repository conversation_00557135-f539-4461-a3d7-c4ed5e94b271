// DOM修复脚本 - 添加缺失的元素
console.log('🔧 开始DOM修复');

// 检查并创建缺失的元素
function createMissingElements() {
    const missingElements = [
        {
            id: 'stock-info',
            parent: 'stock-panel',
            html: '<div id="stock-info" style="display: none;">股票信息容器</div>'
        },

    ];

    missingElements.forEach(element => {
        if (!document.getElementById(element.id)) {
            console.log(`创建缺失元素: ${element.id}`);

            if (element.insertBefore) {
                const beforeElement = document.getElementById(element.insertBefore);
                if (beforeElement && beforeElement.parentNode) {
                    const newElement = document.createElement('div');
                    newElement.innerHTML = element.html;
                    beforeElement.parentNode.insertBefore(newElement.firstElementChild, beforeElement);
                } else {
                    // 如果找不到insertBefore元素，尝试使用class选择器
                    const infoPanelElement = document.querySelector('.info-panel');
                    if (infoPanelElement) {
                        const newElement = document.createElement('div');
                        newElement.innerHTML = element.html;
                        infoPanelElement.insertBefore(newElement.firstElementChild, infoPanelElement.firstChild);
                    }
                }
            } else {
                let parent = document.getElementById(element.parent);
                if (!parent) {
                    // 如果找不到ID，尝试使用class选择器
                    parent = document.querySelector(`.${element.parent}`);
                }
                if (parent) {
                    const newElement = document.createElement('div');
                    newElement.innerHTML = element.html;
                    parent.appendChild(newElement.firstElementChild);
                }
            }
        } else {
            console.log(`元素已存在: ${element.id}`);
        }
    });
}

// 修复按钮事件绑定
function fixButtonEvents() {
    console.log('🔧 修复按钮事件');
    
    // 图表类型按钮
    document.querySelectorAll('.chart-type-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            console.log('图表类型切换:', this.dataset.type);
            document.querySelectorAll('.chart-type-btn').forEach(b => b.classList.remove('active'));
            this.classList.add('active');
        });
    });
    
    // 周期按钮
    document.querySelectorAll('.chart-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            console.log('周期切换:', this.dataset.period);
            document.querySelectorAll('.chart-btn').forEach(b => b.classList.remove('active'));
            this.classList.add('active');
        });
    });
    
    // 技术指标按钮
    document.querySelectorAll('.indicator-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            console.log('指标切换:', this.dataset.indicator);
            this.classList.toggle('active');
        });
    });
}

// 显示修复状态
function showFixStatus() {
    const elements = [
        'loading-overlay',
        'main-chart',
        'hot-stocks-list',
        'sh-index',
        'sz-index',
        'cy-index',
        'stock-info',
        'stock-panel',
        'stock-name',
        'stock-code',
        'current-price',
        'change-amount',
        'change-percent'
    ];
    
    let found = 0;
    elements.forEach(id => {
        if (document.getElementById(id)) {
            found++;
            console.log(`✅ ${id}: 存在`);
        } else {
            console.log(`❌ ${id}: 缺失`);
        }
    });
    
    console.log(`DOM修复完成: ${found}/${elements.length} 元素正常`);
    
    // 显示修复结果
    const statusDiv = document.createElement('div');
    statusDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--bg-card);
        border: 1px solid var(--border-color);
        border-radius: 8px;
        padding: 15px;
        color: var(--text-primary);
        font-size: 12px;
        z-index: 10000;
        max-width: 250px;
    `;
    statusDiv.innerHTML = `
        <div style="font-weight: bold; margin-bottom: 10px;">🔧 DOM修复状态</div>
        <div>元素检查: ${found}/${elements.length} 正常</div>
        <div>按钮数量: ${document.querySelectorAll('.chart-type-btn').length + document.querySelectorAll('.chart-btn').length + document.querySelectorAll('.indicator-btn').length}</div>
        <div style="margin-top: 10px;">
            <button onclick="this.parentNode.parentNode.remove()" style="background: var(--primary-color); color: white; border: none; padding: 5px 10px; border-radius: 4px; cursor: pointer; font-size: 11px;">
                关闭
            </button>
        </div>
    `;
    document.body.appendChild(statusDiv);
    
    // 5秒后自动关闭
    setTimeout(() => {
        if (statusDiv.parentNode) {
            statusDiv.remove();
        }
    }, 5000);
}

// 执行修复
function executeFix() {
    try {
        createMissingElements();
        fixButtonEvents();
        showFixStatus();
        
        // 隐藏加载状态
        const loadingOverlay = document.getElementById('loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }
        
        console.log('✅ DOM修复完成');
    } catch (error) {
        console.error('❌ DOM修复失败:', error);
    }
}

// 页面加载完成后执行修复
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', executeFix);
} else {
    executeFix();
}

// 导出修复函数供手动调用
window.domFix = {
    execute: executeFix,
    createMissingElements,
    fixButtonEvents,
    showFixStatus
};
