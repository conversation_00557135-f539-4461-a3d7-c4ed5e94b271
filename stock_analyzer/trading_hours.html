<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交易时间说明</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .info-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        .status-card {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }
        .trading { background: var(--color-up); }
        .closed { background: var(--text-muted); }
        .warning-box {
            background: rgba(251, 188, 4, 0.1);
            border: 1px solid var(--warning-color);
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        .schedule-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .schedule-table th,
        .schedule-table td {
            border: 1px solid var(--border-color);
            padding: 10px;
            text-align: left;
        }
        .schedule-table th {
            background: var(--bg-tertiary);
            color: var(--text-primary);
        }
        .test-section {
            background: var(--bg-tertiary);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="info-container">
        <h1 style="text-align: center; color: var(--primary-color);">📊 中国股市交易时间说明</h1>
        
        <div class="status-card">
            <div class="status-indicator">
                <div class="status-dot" id="market-dot"></div>
                <h2 id="market-status">正在检测市场状态...</h2>
            </div>
            <p id="market-message">正在获取市场状态信息...</p>
            <p id="next-session" style="color: var(--text-muted); font-size: 14px;"></p>
        </div>

        <div class="warning-box">
            <h3 style="color: var(--warning-color); margin-top: 0;">⚠️ 重要说明</h3>
            <p><strong>感谢用户指出的问题！</strong> 之前的系统确实存在虚假的"实时刷新"效果。现在已经修复：</p>
            <ul>
                <li>✅ <strong>交易时间内</strong>：显示真实的实时数据，价格会根据市场变动</li>
                <li>✅ <strong>非交易时间</strong>：显示静态的收盘数据，价格不会变动</li>
                <li>✅ <strong>市场状态</strong>：明确标识当前是"实时数据"还是"静态数据"</li>
                <li>✅ <strong>诚实透明</strong>：不再有任何虚假的价格变动效果</li>
            </ul>
        </div>

        <h3>🕒 中国股市交易时间</h3>
        <table class="schedule-table">
            <thead>
                <tr>
                    <th>时间段</th>
                    <th>状态</th>
                    <th>说明</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>09:30 - 11:30</td>
                    <td style="color: var(--color-up);">交易中</td>
                    <td>上午交易时间，数据实时更新</td>
                </tr>
                <tr>
                    <td>11:30 - 13:00</td>
                    <td style="color: var(--text-muted);">午休</td>
                    <td>中午休市，显示上午收盘数据</td>
                </tr>
                <tr>
                    <td>13:00 - 15:00</td>
                    <td style="color: var(--color-up);">交易中</td>
                    <td>下午交易时间，数据实时更新</td>
                </tr>
                <tr>
                    <td>15:00 - 次日09:30</td>
                    <td style="color: var(--text-muted);">已收盘</td>
                    <td>收盘后，显示当日收盘数据</td>
                </tr>
                <tr>
                    <td>周末及节假日</td>
                    <td style="color: var(--text-muted);">休市</td>
                    <td>显示最后交易日收盘数据</td>
                </tr>
            </tbody>
        </table>

        <div class="test-section">
            <h3>🧪 实时测试</h3>
            <p>输入股票代码测试当前的真实数据行为：</p>
            <div style="display: flex; gap: 10px; margin: 15px 0;">
                <input type="text" id="test-code" placeholder="输入股票代码，如：600570" 
                       style="flex: 1; padding: 10px; background: var(--bg-card); border: 1px solid var(--border-color); border-radius: 6px; color: var(--text-primary);">
                <button onclick="testStock()" style="padding: 10px 20px; background: var(--primary-color); color: white; border: none; border-radius: 6px; cursor: pointer;">测试</button>
            </div>
            <div id="test-result" style="margin-top: 15px;"></div>
        </div>

        <h3>📋 数据来源</h3>
        <ul>
            <li><strong>新浪财经</strong> - 主要数据源，最稳定</li>
            <li><strong>腾讯财经</strong> - 备用数据源，响应快速</li>
            <li><strong>东方财富</strong> - 官方数据源，权威性高</li>
            <li><strong>网易财经</strong> - 备用数据源，历史悠久</li>
        </ul>

        <div style="text-align: center; margin-top: 30px;">
            <a href="index.html" style="color: var(--primary-color); text-decoration: none; margin-right: 20px;">← 返回主应用</a>
            <a href="demo.html" style="color: var(--primary-color); text-decoration: none;">查看数据演示 →</a>
        </div>
    </div>

    <script>
        async function loadMarketStatus() {
            try {
                const response = await fetch('http://localhost:5001/api/market-status');
                const result = await response.json();
                
                if (result.success) {
                    const status = result.data;
                    const dot = document.getElementById('market-dot');
                    const statusEl = document.getElementById('market-status');
                    const messageEl = document.getElementById('market-message');
                    const nextEl = document.getElementById('next-session');
                    
                    // 更新状态指示器
                    if (status.status === 'trading') {
                        dot.className = 'status-dot trading';
                        statusEl.textContent = '🟢 市场开放中';
                        statusEl.style.color = 'var(--color-up)';
                    } else {
                        dot.className = 'status-dot closed';
                        statusEl.textContent = '🔴 市场关闭中';
                        statusEl.style.color = 'var(--text-muted)';
                    }
                    
                    messageEl.textContent = `当前状态：${status.message}`;
                    
                    if (status.next_open) {
                        const nextTime = new Date(status.next_open);
                        nextEl.textContent = `下次开盘时间：${nextTime.toLocaleString('zh-CN')}`;
                    }
                }
            } catch (error) {
                console.error('获取市场状态失败:', error);
                document.getElementById('market-status').textContent = '❌ 无法获取市场状态';
            }
        }

        async function testStock() {
            const code = document.getElementById('test-code').value.trim();
            const resultEl = document.getElementById('test-result');
            
            if (!code) {
                resultEl.innerHTML = '<p style="color: var(--warning-color);">请输入股票代码</p>';
                return;
            }
            
            resultEl.innerHTML = '<p style="color: var(--text-muted);">正在获取数据...</p>';
            
            try {
                const response = await fetch(`http://localhost:5001/api/stock/${code}`);
                const result = await response.json();
                
                if (result.success) {
                    const data = result.data;
                    const statusColor = data.is_trading ? 'var(--color-up)' : 'var(--text-muted)';
                    const statusText = data.is_trading ? '实时数据' : '静态数据';
                    
                    resultEl.innerHTML = `
                        <div style="background: var(--bg-card); padding: 15px; border-radius: 8px; border: 1px solid var(--border-color);">
                            <h4>${data.name} (${data.code})</h4>
                            <p><strong>当前价格:</strong> ¥${data.price}</p>
                            <p><strong>涨跌:</strong> <span style="color: ${data.change > 0 ? 'var(--color-up)' : 'var(--color-down)'}">
                                ${data.change > 0 ? '+' : ''}${data.change} (${data.change > 0 ? '+' : ''}${data.changePercent}%)
                            </span></p>
                            <p><strong>数据状态:</strong> <span style="color: ${statusColor}">${statusText}</span></p>
                            <p><strong>市场状态:</strong> ${data.market_status.message}</p>
                            <p><strong>数据来源:</strong> ${data.source}</p>
                            <p style="font-size: 12px; color: var(--text-muted);">
                                ${data.is_trading ? '✅ 交易时间内，价格会实时变动' : '⏸️ 非交易时间，价格保持静态'}
                            </p>
                        </div>
                    `;
                } else {
                    resultEl.innerHTML = `<p style="color: var(--danger-color);">❌ ${result.error}</p>`;
                }
            } catch (error) {
                resultEl.innerHTML = `<p style="color: var(--danger-color);">❌ 网络错误: ${error.message}</p>`;
            }
        }

        // 页面加载时获取市场状态
        document.addEventListener('DOMContentLoaded', loadMarketStatus);
        
        // 每分钟更新一次市场状态
        setInterval(loadMarketStatus, 60000);
        
        // 回车键触发测试
        document.getElementById('test-code').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                testStock();
            }
        });
    </script>
</body>
</html>
