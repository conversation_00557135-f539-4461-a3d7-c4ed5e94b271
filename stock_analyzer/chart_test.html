<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图表功能测试</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            background: var(--bg-card);
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        .chart-container {
            height: 400px;
            background: var(--bg-tertiary);
            border-radius: 8px;
            margin: 20px 0;
        }
        .test-controls {
            display: flex;
            gap: 10px;
            margin: 15px 0;
            flex-wrap: wrap;
        }
        .test-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
        }
        .test-btn:hover {
            opacity: 0.8;
        }
        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
        }
        .status.success {
            background: rgba(76, 175, 80, 0.1);
            border: 1px solid var(--color-up);
            color: var(--color-up);
        }
        .status.error {
            background: rgba(244, 67, 54, 0.1);
            border: 1px solid var(--color-down);
            color: var(--color-down);
        }
        .status.info {
            background: rgba(33, 150, 243, 0.1);
            border: 1px solid var(--primary-color);
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center; color: var(--primary-color);">📊 图表功能测试</h1>
        
        <div class="test-section">
            <h3>🔧 环境检测</h3>
            <div id="env-status"></div>
            <button class="test-btn" onclick="checkEnvironment()">检测环境</button>
        </div>

        <div class="test-section">
            <h3>📡 API测试</h3>
            <div class="test-controls">
                <input type="text" id="test-stock" placeholder="股票代码 (如: 000001)" 
                       style="padding: 10px; background: var(--bg-tertiary); border: 1px solid var(--border-color); border-radius: 6px; color: var(--text-primary);">
                <button class="test-btn" onclick="testKlineAPI()">测试K线API</button>
                <button class="test-btn" onclick="testIndicatorAPI()">测试指标API</button>
            </div>
            <div id="api-status"></div>
        </div>

        <div class="test-section">
            <h3>📈 图表渲染测试</h3>
            <div class="test-controls">
                <button class="test-btn" onclick="testLightweightChart()">测试专业图表</button>
                <button class="test-btn" onclick="testFallbackChart()">测试备用图表</button>
                <button class="test-btn" onclick="testChartWithRealData()">测试真实数据图表</button>
            </div>
            <div id="chart-container" class="chart-container"></div>
            <div id="chart-status"></div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="index.html" style="color: var(--primary-color); text-decoration: none;">← 返回主应用</a>
        </div>
    </div>

    <!-- 图表库 -->
    <script src="https://unpkg.com/lightweight-charts/dist/lightweight-charts.standalone.production.js"></script>
    
    <script>
        function showStatus(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            container.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function checkEnvironment() {
            let status = '<h4>环境检测结果:</h4>';
            
            // 检查LightweightCharts
            if (typeof LightweightCharts !== 'undefined') {
                status += '<div class="status success">✅ LightweightCharts 已加载</div>';
            } else {
                status += '<div class="status error">❌ LightweightCharts 未加载</div>';
            }
            
            // 检查API服务
            fetch('http://localhost:5001/api/market-status')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        status += '<div class="status success">✅ API服务正常</div>';
                    } else {
                        status += '<div class="status error">❌ API服务异常</div>';
                    }
                    document.getElementById('env-status').innerHTML = status;
                })
                .catch(error => {
                    status += '<div class="status error">❌ API服务连接失败</div>';
                    document.getElementById('env-status').innerHTML = status;
                });
            
            document.getElementById('env-status').innerHTML = status;
        }

        async function testKlineAPI() {
            const stockCode = document.getElementById('test-stock').value || '000001';
            showStatus('api-status', '正在测试K线API...', 'info');
            
            try {
                const response = await fetch(`http://localhost:5001/api/kline/${stockCode}?period=1d&count=5`);
                const data = await response.json();
                
                if (data.success && data.data.klines.length > 0) {
                    showStatus('api-status', `✅ K线API测试成功，获取到 ${data.data.klines.length} 条数据`, 'success');
                } else {
                    showStatus('api-status', `❌ K线API测试失败: ${data.error || '无数据'}`, 'error');
                }
            } catch (error) {
                showStatus('api-status', `❌ K线API测试失败: ${error.message}`, 'error');
            }
        }

        async function testIndicatorAPI() {
            const stockCode = document.getElementById('test-stock').value || '000001';
            showStatus('api-status', '正在测试技术指标API...', 'info');
            
            try {
                const response = await fetch(`http://localhost:5001/api/indicators/${stockCode}?period=1d`);
                const data = await response.json();
                
                if (data.success && data.data.indicators) {
                    const indicators = Object.keys(data.data.indicators);
                    showStatus('api-status', `✅ 技术指标API测试成功，包含指标: ${indicators.join(', ')}`, 'success');
                } else {
                    showStatus('api-status', `❌ 技术指标API测试失败: ${data.error || '无数据'}`, 'error');
                }
            } catch (error) {
                showStatus('api-status', `❌ 技术指标API测试失败: ${error.message}`, 'error');
            }
        }

        function testLightweightChart() {
            showStatus('chart-status', '正在测试专业图表...', 'info');
            
            const container = document.getElementById('chart-container');
            container.innerHTML = '';
            
            if (typeof LightweightCharts === 'undefined') {
                showStatus('chart-status', '❌ LightweightCharts库未加载', 'error');
                return;
            }
            
            try {
                const chart = LightweightCharts.createChart(container, {
                    width: container.clientWidth,
                    height: 400,
                    layout: {
                        backgroundColor: '#1e2329',
                        textColor: '#d1d4dc',
                    }
                });
                
                const candlestickSeries = chart.addCandlestickSeries({
                    upColor: '#ff4757',
                    downColor: '#2ed573',
                    borderDownColor: '#2ed573',
                    borderUpColor: '#ff4757',
                    wickDownColor: '#2ed573',
                    wickUpColor: '#ff4757',
                });
                
                // 生成测试数据
                const testData = generateTestData();
                candlestickSeries.setData(testData);
                
                showStatus('chart-status', '✅ 专业图表测试成功', 'success');
            } catch (error) {
                showStatus('chart-status', `❌ 专业图表测试失败: ${error.message}`, 'error');
            }
        }

        function testFallbackChart() {
            showStatus('chart-status', '正在测试备用图表...', 'info');
            
            const container = document.getElementById('chart-container');
            container.innerHTML = '<canvas id="test-canvas" width="800" height="400"></canvas>';
            
            try {
                const canvas = document.getElementById('test-canvas');
                const ctx = canvas.getContext('2d');
                
                // 绘制简单图表
                ctx.fillStyle = '#1e2329';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                ctx.strokeStyle = '#2196F3';
                ctx.lineWidth = 2;
                ctx.beginPath();
                
                for (let i = 0; i < 50; i++) {
                    const x = (i / 49) * (canvas.width - 40) + 20;
                    const y = canvas.height / 2 + Math.sin(i * 0.2) * 50;
                    
                    if (i === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                }
                
                ctx.stroke();
                
                ctx.fillStyle = '#ffffff';
                ctx.font = '16px Arial';
                ctx.fillText('备用图表测试', 20, 30);
                
                showStatus('chart-status', '✅ 备用图表测试成功', 'success');
            } catch (error) {
                showStatus('chart-status', `❌ 备用图表测试失败: ${error.message}`, 'error');
            }
        }

        async function testChartWithRealData() {
            const stockCode = document.getElementById('test-stock').value || '000001';
            showStatus('chart-status', '正在测试真实数据图表...', 'info');
            
            try {
                const [klineResponse, indicatorResponse] = await Promise.all([
                    fetch(`http://localhost:5001/api/kline/${stockCode}?period=1d&count=30`),
                    fetch(`http://localhost:5001/api/indicators/${stockCode}?period=1d`)
                ]);
                
                const klineData = await klineResponse.json();
                const indicatorData = await indicatorResponse.json();
                
                if (!klineData.success || !indicatorData.success) {
                    throw new Error('API数据获取失败');
                }
                
                const container = document.getElementById('chart-container');
                container.innerHTML = '';
                
                if (typeof LightweightCharts !== 'undefined') {
                    // 使用专业图表
                    const chart = LightweightCharts.createChart(container, {
                        width: container.clientWidth,
                        height: 400,
                        layout: {
                            backgroundColor: '#1e2329',
                            textColor: '#d1d4dc',
                        }
                    });
                    
                    const candlestickSeries = chart.addCandlestickSeries({
                        upColor: '#ff4757',
                        downColor: '#2ed573',
                        borderDownColor: '#2ed573',
                        borderUpColor: '#ff4757',
                        wickDownColor: '#2ed573',
                        wickUpColor: '#ff4757',
                    });
                    
                    const candleData = klineData.data.klines.map(k => ({
                        time: k.date,
                        open: k.open,
                        high: k.high,
                        low: k.low,
                        close: k.close
                    }));
                    
                    candlestickSeries.setData(candleData);
                    
                    showStatus('chart-status', `✅ 真实数据图表测试成功 (${stockCode})`, 'success');
                } else {
                    // 使用备用图表
                    container.innerHTML = '<canvas id="real-data-canvas" width="800" height="400"></canvas>';
                    const canvas = document.getElementById('real-data-canvas');
                    const ctx = canvas.getContext('2d');
                    
                    const prices = klineData.data.klines.map(k => k.close);
                    const maxPrice = Math.max(...prices);
                    const minPrice = Math.min(...prices);
                    const priceRange = maxPrice - minPrice;
                    
                    ctx.fillStyle = '#1e2329';
                    ctx.fillRect(0, 0, canvas.width, canvas.height);
                    
                    ctx.strokeStyle = '#2196F3';
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    
                    prices.forEach((price, index) => {
                        const x = (index / (prices.length - 1)) * (canvas.width - 40) + 20;
                        const y = canvas.height - 40 - ((price - minPrice) / priceRange) * (canvas.height - 80);
                        
                        if (index === 0) {
                            ctx.moveTo(x, y);
                        } else {
                            ctx.lineTo(x, y);
                        }
                    });
                    
                    ctx.stroke();
                    
                    ctx.fillStyle = '#ffffff';
                    ctx.font = '12px Arial';
                    ctx.fillText(`${stockCode} - 最高: ${maxPrice.toFixed(2)}`, 10, 20);
                    
                    showStatus('chart-status', `✅ 真实数据备用图表测试成功 (${stockCode})`, 'success');
                }
                
            } catch (error) {
                showStatus('chart-status', `❌ 真实数据图表测试失败: ${error.message}`, 'error');
            }
        }

        function generateTestData() {
            const data = [];
            const basePrice = 100;
            const today = new Date();
            
            for (let i = 0; i < 30; i++) {
                const date = new Date(today);
                date.setDate(date.getDate() - (29 - i));
                
                const variation = (Math.random() - 0.5) * 0.1;
                const price = basePrice * (1 + variation);
                
                data.push({
                    time: date.toISOString().split('T')[0],
                    open: price * 0.99,
                    high: price * 1.02,
                    low: price * 0.98,
                    close: price
                });
            }
            
            return data;
        }

        // 页面加载时自动检测环境
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(checkEnvironment, 500);
        });
    </script>
</body>
</html>
