<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>左右键测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
        }
        .key-display {
            background: #333;
            padding: 20px;
            margin: 10px 0;
            border-radius: 4px;
            font-size: 18px;
            font-weight: bold;
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .instructions {
            background: #444;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎹 左右键测试页面</h1>
        
        <div class="instructions">
            <h3>测试说明：</h3>
            <p>1. 点击页面任意位置确保焦点在页面上</p>
            <p>2. 按下左右键、WASD键测试</p>
            <p>3. 观察下方的按键日志</p>
        </div>

        <div class="key-display" id="keyDisplay">
            等待按键输入...
        </div>

        <div class="log" id="keyLog">
            === 按键日志 ===<br>
        </div>

        <button onclick="clearLog()">清除日志</button>
        <button onclick="testKeyboard()">测试键盘事件</button>
    </div>

    <script>
        const keyDisplay = document.getElementById('keyDisplay');
        const keyLog = document.getElementById('keyLog');
        let logCount = 0;

        function addLog(message) {
            logCount++;
            const timestamp = new Date().toLocaleTimeString();
            keyLog.innerHTML += `[${timestamp}] ${logCount}: ${message}<br>`;
            keyLog.scrollTop = keyLog.scrollHeight;
        }

        function clearLog() {
            keyLog.innerHTML = '=== 按键日志 ===<br>';
            logCount = 0;
        }

        function testKeyboard() {
            addLog('🧪 开始键盘事件测试');
            addLog('📝 请按下左右键、WASD键进行测试');
        }

        // 键盘事件监听
        document.addEventListener('keydown', (e) => {
            const key = e.key;
            const code = e.code;
            const isCtrl = e.ctrlKey;
            const isShift = e.shiftKey;
            const isAlt = e.altKey;

            // 更新显示
            keyDisplay.innerHTML = `
                按键: ${key} | 代码: ${code}<br>
                Ctrl: ${isCtrl} | Shift: ${isShift} | Alt: ${isAlt}
            `;

            // 记录日志
            let message = `🎹 按键: "${key}" (${code})`;
            if (isCtrl || isShift || isAlt) {
                message += ` [修饰键: ${isCtrl?'Ctrl ':''}${isShift?'Shift ':''}${isAlt?'Alt':''}]`;
            }

            // 特别标记左右键
            if (key === 'ArrowLeft') {
                message += ' ⬅️ 左键检测到!';
                e.preventDefault();
            } else if (key === 'ArrowRight') {
                message += ' ➡️ 右键检测到!';
                e.preventDefault();
            } else if (key === 'a' || key === 'A') {
                message += ' 🅰️ A键检测到!';
            } else if (key === 'd' || key === 'D') {
                message += ' 🅳 D键检测到!';
            }

            addLog(message);

            // 阻止某些默认行为
            if (['ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'].includes(key)) {
                e.preventDefault();
                addLog(`🚫 已阻止 ${key} 的默认行为`);
            }
        });

        // 页面加载完成
        document.addEventListener('DOMContentLoaded', () => {
            addLog('✅ 页面加载完成，键盘事件监听器已激活');
            addLog('💡 请点击页面确保焦点，然后按下左右键测试');
        });

        // 点击事件确保焦点
        document.addEventListener('click', () => {
            addLog('🖱️ 页面获得焦点，可以开始测试按键');
        });
    </script>
</body>
</html>
