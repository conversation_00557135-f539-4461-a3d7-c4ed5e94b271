<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 MACD指标测试验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
            margin: 0;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
        }
        .test-section {
            background: #333;
            margin: 15px 0;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #ffc107;
        }
        .test-section h3 {
            margin: 0 0 10px 0;
            color: #ffc107;
        }
        .macd-display {
            background: #000;
            color: #0f0;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            border-radius: 4px;
            margin: 10px 0;
            max-height: 400px;
            overflow-y: auto;
        }
        .test-button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 12px;
        }
        .test-button:hover {
            background: #1976d2;
        }
        .test-button.success {
            background: #4caf50;
        }
        .test-button.warning {
            background: #ff9800;
        }
        .test-button.danger {
            background: #f44336;
        }
        .macd-value {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            margin: 2px;
        }
        .macd-positive { background: #ff4757; color: white; }
        .macd-negative { background: #2ed573; color: white; }
        .macd-null { background: #666; color: #ccc; }
        .formula-box {
            background: #444;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
            border-left: 4px solid #2196f3;
        }
        .formula-box h4 {
            color: #2196f3;
            margin: 0 0 10px 0;
        }
        .formula {
            font-family: monospace;
            background: #222;
            padding: 8px;
            border-radius: 4px;
            color: #ffc107;
        }
        .validation-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .validation-pass { background: #1b5e20; color: #4caf50; }
        .validation-fail { background: #b71c1c; color: #f44336; }
        .validation-warning { background: #e65100; color: #ff9800; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>📊 MACD指标专业验证工具</h1>
        
        <div class="test-section">
            <h3>📐 MACD计算公式验证</h3>
            <div class="formula-box">
                <h4>标准MACD计算公式</h4>
                <div class="formula">
                    EMA12 = 12日指数移动平均线<br>
                    EMA26 = 26日指数移动平均线<br>
                    DIF = EMA12 - EMA26<br>
                    DEA = DIF的9日指数移动平均线<br>
                    MACD = (DIF - DEA) × 2
                </div>
            </div>
            <button class="test-button success" onclick="testMACDFormula()">🧮 验证MACD公式</button>
            <button class="test-button" onclick="testMACDRange()">📏 验证数值范围</button>
            <button class="test-button warning" onclick="testMACDAlignment()">🎯 验证数据对齐</button>
        </div>

        <div class="test-section">
            <h3>📊 MACD数据获取测试</h3>
            <button class="test-button" onclick="fetchMACDData()">获取MACD数据</button>
            <button class="test-button" onclick="analyzeMACDData()">分析MACD数据</button>
            <button class="test-button danger" onclick="clearResults()">清除结果</button>
            <div id="macd-raw-data" class="macd-display">
                等待获取MACD数据...
            </div>
        </div>

        <div class="test-section">
            <h3>🔍 MACD数值验证</h3>
            <div id="macd-validation" class="macd-display">
                等待验证...
            </div>
        </div>

        <div class="test-section">
            <h3>📈 MACD可视化显示</h3>
            <div id="macd-visualization" class="macd-display">
                等待可视化...
            </div>
        </div>

        <div class="test-section">
            <h3>🎯 专业操作员验证结果</h3>
            <div id="professional-validation">
                <!-- 验证结果将在这里显示 -->
            </div>
            <button class="test-button success" onclick="runFullValidation()">🚀 运行完整验证</button>
            <button class="test-button warning" onclick="openMainApp()">打开主应用</button>
        </div>
    </div>

    <script>
        let macdData = null;
        let klineData = null;

        function addLog(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const timestamp = new Date().toLocaleTimeString();
            const typeIcon = {
                'success': '✅',
                'error': '❌', 
                'warning': '⚠️',
                'info': 'ℹ️'
            };
            container.innerHTML += `[${timestamp}] ${typeIcon[type] || 'ℹ️'} ${message}<br>`;
            container.scrollTop = container.scrollHeight;
        }

        function clearResults() {
            ['macd-raw-data', 'macd-validation', 'macd-visualization'].forEach(id => {
                document.getElementById(id).innerHTML = '已清除结果...<br>';
            });
            document.getElementById('professional-validation').innerHTML = '';
        }

        async function fetchMACDData() {
            addLog('macd-raw-data', '开始获取MACD数据...', 'info');
            
            try {
                // 获取K线数据
                const klineResponse = await fetch('http://localhost:5001/api/kline/000001/1d');
                if (!klineResponse.ok) throw new Error(`K线数据获取失败: ${klineResponse.status}`);
                
                const klineResult = await klineResponse.json();
                if (!klineResult.success) throw new Error(klineResult.error);
                
                klineData = klineResult.data.klines;
                addLog('macd-raw-data', `K线数据获取成功: ${klineData.length}条记录`, 'success');

                // 获取MACD指标数据
                const macdResponse = await fetch('http://localhost:5001/api/indicators/000001?period=1d&indicators=macd');
                if (!macdResponse.ok) throw new Error(`MACD数据获取失败: ${macdResponse.status}`);
                
                const macdResult = await macdResponse.json();
                if (!macdResult.success) throw new Error(macdResult.error);
                
                macdData = macdResult.data.indicators.macd;
                addLog('macd-raw-data', `MACD数据获取成功`, 'success');
                addLog('macd-raw-data', `DIF数组长度: ${macdData.dif.length}`, 'info');
                addLog('macd-raw-data', `DEA数组长度: ${macdData.dea.length}`, 'info');
                addLog('macd-raw-data', `MACD数组长度: ${macdData.macd.length}`, 'info');

                // 显示原始数据样本
                displayMACDSample();
                
            } catch (error) {
                addLog('macd-raw-data', `数据获取失败: ${error.message}`, 'error');
            }
        }

        function displayMACDSample() {
            if (!macdData) return;
            
            addLog('macd-raw-data', '=== MACD数据样本 (最后10个数据点) ===', 'info');
            
            const length = macdData.dif.length;
            const startIndex = Math.max(0, length - 10);
            
            for (let i = startIndex; i < length; i++) {
                const dif = macdData.dif[i];
                const dea = macdData.dea[i];
                const macd = macdData.macd[i];
                
                const difStr = dif !== null ? dif.toFixed(4) : 'null';
                const deaStr = dea !== null ? dea.toFixed(4) : 'null';
                const macdStr = macd !== null ? macd.toFixed(4) : 'null';
                
                addLog('macd-raw-data', `[${i}] DIF:${difStr} DEA:${deaStr} MACD:${macdStr}`, 'info');
            }
        }

        function analyzeMACDData() {
            if (!macdData) {
                addLog('macd-validation', '请先获取MACD数据', 'warning');
                return;
            }

            addLog('macd-validation', '开始分析MACD数据...', 'info');
            
            // 统计有效数据
            const difValid = macdData.dif.filter(v => v !== null && !isNaN(v));
            const deaValid = macdData.dea.filter(v => v !== null && !isNaN(v));
            const macdValid = macdData.macd.filter(v => v !== null && !isNaN(v));
            
            addLog('macd-validation', `DIF有效数据: ${difValid.length}/${macdData.dif.length}`, 'info');
            addLog('macd-validation', `DEA有效数据: ${deaValid.length}/${macdData.dea.length}`, 'info');
            addLog('macd-validation', `MACD有效数据: ${macdValid.length}/${macdData.macd.length}`, 'info');

            // 数值范围分析
            if (difValid.length > 0) {
                const difMin = Math.min(...difValid);
                const difMax = Math.max(...difValid);
                addLog('macd-validation', `DIF范围: ${difMin.toFixed(4)} ~ ${difMax.toFixed(4)}`, 'info');
            }

            if (deaValid.length > 0) {
                const deaMin = Math.min(...deaValid);
                const deaMax = Math.max(...deaValid);
                addLog('macd-validation', `DEA范围: ${deaMin.toFixed(4)} ~ ${deaMax.toFixed(4)}`, 'info');
            }

            if (macdValid.length > 0) {
                const macdMin = Math.min(...macdValid);
                const macdMax = Math.max(...macdValid);
                addLog('macd-validation', `MACD范围: ${macdMin.toFixed(4)} ~ ${macdMax.toFixed(4)}`, 'info');
            }

            // 最新数值
            const lastIndex = macdData.dif.length - 1;
            const lastDif = macdData.dif[lastIndex];
            const lastDea = macdData.dea[lastIndex];
            const lastMacd = macdData.macd[lastIndex];
            
            addLog('macd-validation', `最新MACD值:`, 'info');
            addLog('macd-validation', `  DIF: ${lastDif !== null ? lastDif.toFixed(4) : 'null'}`, 'info');
            addLog('macd-validation', `  DEA: ${lastDea !== null ? lastDea.toFixed(4) : 'null'}`, 'info');
            addLog('macd-validation', `  MACD: ${lastMacd !== null ? lastMacd.toFixed(4) : 'null'}`, 'info');
        }

        function testMACDFormula() {
            if (!macdData) {
                addLog('macd-validation', '请先获取MACD数据', 'warning');
                return;
            }

            addLog('macd-validation', '开始验证MACD计算公式...', 'info');
            
            let formulaErrors = 0;
            const testCount = Math.min(10, macdData.dif.length);
            
            for (let i = macdData.dif.length - testCount; i < macdData.dif.length; i++) {
                const dif = macdData.dif[i];
                const dea = macdData.dea[i];
                const macd = macdData.macd[i];
                
                if (dif !== null && dea !== null && macd !== null) {
                    const expectedMacd = (dif - dea) * 2;
                    const diff = Math.abs(macd - expectedMacd);
                    
                    if (diff > 0.0001) { // 允许小的浮点误差
                        formulaErrors++;
                        addLog('macd-validation', `公式错误[${i}]: 期望${expectedMacd.toFixed(4)}, 实际${macd.toFixed(4)}`, 'error');
                    }
                }
            }
            
            if (formulaErrors === 0) {
                addLog('macd-validation', '✅ MACD公式验证通过', 'success');
            } else {
                addLog('macd-validation', `❌ MACD公式验证失败: ${formulaErrors}个错误`, 'error');
            }
        }

        function testMACDRange() {
            if (!macdData) {
                addLog('macd-validation', '请先获取MACD数据', 'warning');
                return;
            }

            addLog('macd-validation', '开始验证MACD数值范围...', 'info');
            
            const validValues = macdData.macd.filter(v => v !== null && !isNaN(v));
            let rangeWarnings = 0;
            
            validValues.forEach((value, index) => {
                if (Math.abs(value) > 10) { // MACD值通常不会超过±10
                    rangeWarnings++;
                    if (rangeWarnings <= 5) { // 只显示前5个警告
                        addLog('macd-validation', `数值异常: MACD[${index}] = ${value.toFixed(4)}`, 'warning');
                    }
                }
            });
            
            if (rangeWarnings === 0) {
                addLog('macd-validation', '✅ MACD数值范围正常', 'success');
            } else {
                addLog('macd-validation', `⚠️ 发现${rangeWarnings}个异常数值`, 'warning');
            }
        }

        function testMACDAlignment() {
            if (!macdData || !klineData) {
                addLog('macd-validation', '请先获取MACD和K线数据', 'warning');
                return;
            }

            addLog('macd-validation', '开始验证数据对齐...', 'info');
            
            const klineLength = klineData.length;
            const difLength = macdData.dif.length;
            const deaLength = macdData.dea.length;
            const macdLength = macdData.macd.length;
            
            addLog('macd-validation', `K线数据长度: ${klineLength}`, 'info');
            addLog('macd-validation', `DIF数据长度: ${difLength}`, 'info');
            addLog('macd-validation', `DEA数据长度: ${deaLength}`, 'info');
            addLog('macd-validation', `MACD数据长度: ${macdLength}`, 'info');
            
            if (difLength === deaLength && deaLength === macdLength && macdLength === klineLength) {
                addLog('macd-validation', '✅ 数据长度对齐正确', 'success');
            } else {
                addLog('macd-validation', '❌ 数据长度不对齐', 'error');
            }
        }

        function runFullValidation() {
            const validationContainer = document.getElementById('professional-validation');
            validationContainer.innerHTML = '<h4>🔍 专业操作员验证报告</h4>';
            
            if (!macdData) {
                validationContainer.innerHTML += '<div class="validation-fail">❌ 缺少MACD数据，请先获取数据</div>';
                return;
            }

            // 验证1: 数据完整性
            const totalLength = macdData.dif.length;
            const validDif = macdData.dif.filter(v => v !== null).length;
            const validDea = macdData.dea.filter(v => v !== null).length;
            const validMacd = macdData.macd.filter(v => v !== null).length;
            
            const completeness = Math.min(validDif, validDea, validMacd) / totalLength;
            
            if (completeness > 0.8) {
                validationContainer.innerHTML += '<div class="validation-pass">✅ 数据完整性: 良好</div>';
            } else if (completeness > 0.5) {
                validationContainer.innerHTML += '<div class="validation-warning">⚠️ 数据完整性: 一般</div>';
            } else {
                validationContainer.innerHTML += '<div class="validation-fail">❌ 数据完整性: 差</div>';
            }

            // 验证2: 公式正确性
            let formulaCorrect = true;
            for (let i = Math.max(0, totalLength - 10); i < totalLength; i++) {
                const dif = macdData.dif[i];
                const dea = macdData.dea[i];
                const macd = macdData.macd[i];
                
                if (dif !== null && dea !== null && macd !== null) {
                    const expected = (dif - dea) * 2;
                    if (Math.abs(macd - expected) > 0.0001) {
                        formulaCorrect = false;
                        break;
                    }
                }
            }
            
            if (formulaCorrect) {
                validationContainer.innerHTML += '<div class="validation-pass">✅ 计算公式: 正确</div>';
            } else {
                validationContainer.innerHTML += '<div class="validation-fail">❌ 计算公式: 错误</div>';
            }

            // 验证3: 数值合理性
            const validValues = macdData.macd.filter(v => v !== null && !isNaN(v));
            const extremeValues = validValues.filter(v => Math.abs(v) > 5).length;
            const reasonableRatio = 1 - (extremeValues / validValues.length);
            
            if (reasonableRatio > 0.95) {
                validationContainer.innerHTML += '<div class="validation-pass">✅ 数值合理性: 优秀</div>';
            } else if (reasonableRatio > 0.8) {
                validationContainer.innerHTML += '<div class="validation-warning">⚠️ 数值合理性: 一般</div>';
            } else {
                validationContainer.innerHTML += '<div class="validation-fail">❌ 数值合理性: 差</div>';
            }

            // 最终评估
            const passCount = validationContainer.querySelectorAll('.validation-pass').length;
            const totalTests = 3;
            
            validationContainer.innerHTML += '<hr>';
            if (passCount === totalTests) {
                validationContainer.innerHTML += '<div class="validation-pass"><strong>🎉 MACD指标验证通过！数据质量优秀</strong></div>';
            } else if (passCount >= totalTests * 0.6) {
                validationContainer.innerHTML += '<div class="validation-warning"><strong>⚠️ MACD指标基本正常，但需要注意部分问题</strong></div>';
            } else {
                validationContainer.innerHTML += '<div class="validation-fail"><strong>❌ MACD指标存在严重问题，需要修复</strong></div>';
            }
        }

        function openMainApp() {
            window.open('http://localhost:5001/professional.html', '_blank');
        }

        // 页面加载完成后自动获取数据
        document.addEventListener('DOMContentLoaded', () => {
            addLog('macd-raw-data', '📊 MACD专业验证工具已加载', 'info');
            addLog('macd-raw-data', '点击"获取MACD数据"开始测试...', 'info');
        });
    </script>
</body>
</html>
