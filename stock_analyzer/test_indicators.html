<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 技术指标数据测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
            margin: 0;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
        }
        .test-section {
            background: #333;
            margin: 15px 0;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #ffc107;
        }
        .test-section h3 {
            margin: 0 0 10px 0;
            color: #ffc107;
        }
        .indicator-test {
            background: #444;
            margin: 10px 0;
            padding: 12px;
            border-radius: 4px;
            border: 1px solid #555;
        }
        .indicator-test h4 {
            margin: 0 0 8px 0;
            color: #2196f3;
            font-size: 14px;
        }
        .data-display {
            background: #000;
            color: #0f0;
            padding: 10px;
            font-family: monospace;
            font-size: 11px;
            border-radius: 4px;
            margin: 8px 0;
            max-height: 200px;
            overflow-y: auto;
        }
        .test-button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 12px;
        }
        .test-button:hover {
            background: #1976d2;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-online { background: #4caf50; }
        .status-offline { background: #f44336; }
        .status-warning { background: #ff9800; }
        .error { color: #f44336; }
        .success { color: #4caf50; }
        .warning { color: #ff9800; }
        .info { color: #2196f3; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>📊 技术指标数据测试工具</h1>
        
        <div class="test-section">
            <h3>🔧 API数据获取测试</h3>
            <button class="test-button" onclick="testStockAPI()">测试股票数据API</button>
            <button class="test-button" onclick="testKlineAPI()">测试K线数据API</button>
            <button class="test-button" onclick="testIndicatorAPI()">测试技术指标API</button>
            <div id="api-results" class="data-display">
                等待API测试...
            </div>
        </div>

        <div class="test-section">
            <h3>📈 技术指标数据结构测试</h3>
            
            <div class="indicator-test">
                <h4>MACD指标</h4>
                <button class="test-button" onclick="testMACDData()">测试MACD数据</button>
                <div id="macd-results" class="data-display">
                    等待MACD测试...
                </div>
            </div>

            <div class="indicator-test">
                <h4>KDJ指标</h4>
                <button class="test-button" onclick="testKDJData()">测试KDJ数据</button>
                <div id="kdj-results" class="data-display">
                    等待KDJ测试...
                </div>
            </div>

            <div class="indicator-test">
                <h4>RSI指标</h4>
                <button class="test-button" onclick="testRSIData()">测试RSI数据</button>
                <div id="rsi-results" class="data-display">
                    等待RSI测试...
                </div>
            </div>

            <div class="indicator-test">
                <h4>均线数据</h4>
                <button class="test-button" onclick="testMAData()">测试均线数据</button>
                <div id="ma-results" class="data-display">
                    等待均线测试...
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🎯 数据对齐测试</h3>
            <button class="test-button" onclick="testDataAlignment()">测试数据对齐</button>
            <div id="alignment-results" class="data-display">
                等待数据对齐测试...
            </div>
        </div>

        <div class="test-section">
            <h3>🚀 综合测试</h3>
            <button class="test-button" onclick="runAllIndicatorTests()" style="background: #4caf50;">运行全部测试</button>
            <button class="test-button" onclick="clearAllResults()">清除结果</button>
            <button class="test-button" onclick="openMainApp()" style="background: #ff9800;">打开主应用</button>
        </div>
    </div>

    <script>
        let testData = {
            stockData: null,
            klineData: null,
            indicatorData: null
        };

        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const timestamp = new Date().toLocaleTimeString();
            const typeIcon = {
                'success': '✅',
                'error': '❌', 
                'warning': '⚠️',
                'info': 'ℹ️'
            };
            container.innerHTML += `[${timestamp}] ${typeIcon[type] || 'ℹ️'} ${message}<br>`;
            container.scrollTop = container.scrollHeight;
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        function clearAllResults() {
            ['api-results', 'macd-results', 'kdj-results', 'rsi-results', 'ma-results', 'alignment-results'].forEach(id => {
                clearResults(id);
            });
        }

        // API测试函数
        async function testStockAPI() {
            addResult('api-results', '开始测试股票数据API...', 'info');
            try {
                const response = await fetch('http://localhost:5001/api/stock/000001');
                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.data) {
                        testData.stockData = data.data;
                        addResult('api-results', `股票数据获取成功: ${data.data.name} (${data.data.code})`, 'success');
                        addResult('api-results', `价格: ${data.data.price}, 涨跌: ${data.data.change}`, 'info');
                    } else {
                        throw new Error(data.error || '数据格式错误');
                    }
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                addResult('api-results', `股票数据获取失败: ${error.message}`, 'error');
            }
        }

        async function testKlineAPI() {
            addResult('api-results', '开始测试K线数据API...', 'info');
            try {
                const response = await fetch('http://localhost:5001/api/kline/000001/1d');
                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.data && data.data.klines) {
                        testData.klineData = data.data;
                        addResult('api-results', `K线数据获取成功: ${data.data.klines.length}条记录`, 'success');
                        const lastKline = data.data.klines[data.data.klines.length - 1];
                        addResult('api-results', `最新K线: ${lastKline.date} 收盘${lastKline.close}`, 'info');
                    } else {
                        throw new Error(data.error || 'K线数据格式错误');
                    }
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                addResult('api-results', `K线数据获取失败: ${error.message}`, 'error');
            }
        }

        async function testIndicatorAPI() {
            addResult('api-results', '开始测试技术指标API...', 'info');
            try {
                const response = await fetch('http://localhost:5001/api/indicators/000001/1d');
                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.data && data.data.indicators) {
                        testData.indicatorData = data.data;
                        const indicators = Object.keys(data.data.indicators);
                        addResult('api-results', `技术指标数据获取成功: ${indicators.join(', ')}`, 'success');
                        
                        // 检查各指标数据长度
                        indicators.forEach(indicator => {
                            const indicatorData = data.data.indicators[indicator];
                            if (typeof indicatorData === 'object') {
                                const subKeys = Object.keys(indicatorData);
                                addResult('api-results', `${indicator}: ${subKeys.join(', ')}`, 'info');
                            }
                        });
                    } else {
                        throw new Error(data.error || '技术指标数据格式错误');
                    }
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                addResult('api-results', `技术指标数据获取失败: ${error.message}`, 'error');
            }
        }

        // 指标数据测试函数
        function testMACDData() {
            clearResults('macd-results');
            addResult('macd-results', '开始测试MACD数据...', 'info');
            
            if (!testData.indicatorData || !testData.indicatorData.indicators) {
                addResult('macd-results', '请先运行技术指标API测试', 'warning');
                return;
            }

            const macdData = testData.indicatorData.indicators.macd;
            if (!macdData) {
                addResult('macd-results', 'MACD数据不存在', 'error');
                return;
            }

            addResult('macd-results', `MACD数据结构: ${Object.keys(macdData).join(', ')}`, 'info');
            
            ['macd', 'dif', 'dea'].forEach(key => {
                if (macdData[key]) {
                    const values = macdData[key];
                    addResult('macd-results', `${key.toUpperCase()}: 长度=${values.length}, 最后值=${values[values.length-1]}`, 'success');
                    
                    // 检查数据有效性
                    const validCount = values.filter(v => v !== null && v !== undefined && !isNaN(v)).length;
                    addResult('macd-results', `${key.toUpperCase()}: 有效数据=${validCount}/${values.length}`, 'info');
                } else {
                    addResult('macd-results', `${key.toUpperCase()}: 数据缺失`, 'error');
                }
            });
        }

        function testKDJData() {
            clearResults('kdj-results');
            addResult('kdj-results', '开始测试KDJ数据...', 'info');
            
            if (!testData.indicatorData || !testData.indicatorData.indicators) {
                addResult('kdj-results', '请先运行技术指标API测试', 'warning');
                return;
            }

            const kdjData = testData.indicatorData.indicators.kdj;
            if (!kdjData) {
                addResult('kdj-results', 'KDJ数据不存在', 'error');
                return;
            }

            addResult('kdj-results', `KDJ数据结构: ${Object.keys(kdjData).join(', ')}`, 'info');
            
            ['k', 'd', 'j'].forEach(key => {
                if (kdjData[key]) {
                    const values = kdjData[key];
                    addResult('kdj-results', `${key.toUpperCase()}: 长度=${values.length}, 最后值=${values[values.length-1]}`, 'success');
                    
                    // 检查数据有效性
                    const validCount = values.filter(v => v !== null && v !== undefined && !isNaN(v)).length;
                    addResult('kdj-results', `${key.toUpperCase()}: 有效数据=${validCount}/${values.length}`, 'info');
                } else {
                    addResult('kdj-results', `${key.toUpperCase()}: 数据缺失`, 'error');
                }
            });
        }

        function testRSIData() {
            clearResults('rsi-results');
            addResult('rsi-results', '开始测试RSI数据...', 'info');
            
            if (!testData.indicatorData || !testData.indicatorData.indicators) {
                addResult('rsi-results', '请先运行技术指标API测试', 'warning');
                return;
            }

            const rsiData = testData.indicatorData.indicators.rsi;
            if (!rsiData) {
                addResult('rsi-results', 'RSI数据不存在', 'error');
                return;
            }

            addResult('rsi-results', `RSI数据结构: ${Object.keys(rsiData).join(', ')}`, 'info');
            
            Object.entries(rsiData).forEach(([period, values]) => {
                if (values && Array.isArray(values)) {
                    addResult('rsi-results', `${period}: 长度=${values.length}, 最后值=${values[values.length-1]}`, 'success');
                    
                    // 检查数据有效性
                    const validCount = values.filter(v => v !== null && v !== undefined && !isNaN(v)).length;
                    addResult('rsi-results', `${period}: 有效数据=${validCount}/${values.length}`, 'info');
                } else {
                    addResult('rsi-results', `${period}: 数据格式错误`, 'error');
                }
            });
        }

        function testMAData() {
            clearResults('ma-results');
            addResult('ma-results', '开始测试均线数据...', 'info');
            
            if (!testData.indicatorData || !testData.indicatorData.indicators) {
                addResult('ma-results', '请先运行技术指标API测试', 'warning');
                return;
            }

            const maData = testData.indicatorData.indicators.ma;
            if (!maData) {
                addResult('ma-results', '均线数据不存在', 'error');
                return;
            }

            addResult('ma-results', `均线数据结构: ${Object.keys(maData).join(', ')}`, 'info');
            
            Object.entries(maData).forEach(([period, values]) => {
                if (values && Array.isArray(values)) {
                    addResult('ma-results', `${period}: 长度=${values.length}, 最后值=${values[values.length-1]}`, 'success');
                    
                    // 检查数据有效性
                    const validCount = values.filter(v => v !== null && v !== undefined && !isNaN(v)).length;
                    addResult('ma-results', `${period}: 有效数据=${validCount}/${values.length}`, 'info');
                } else {
                    addResult('ma-results', `${period}: 数据格式错误`, 'error');
                }
            });
        }

        function testDataAlignment() {
            clearResults('alignment-results');
            addResult('alignment-results', '开始测试数据对齐...', 'info');
            
            if (!testData.klineData || !testData.indicatorData) {
                addResult('alignment-results', '请先运行K线和技术指标API测试', 'warning');
                return;
            }

            const klineLength = testData.klineData.klines.length;
            addResult('alignment-results', `K线数据长度: ${klineLength}`, 'info');

            const indicators = testData.indicatorData.indicators;
            
            // 检查各指标数据长度是否与K线对齐
            Object.entries(indicators).forEach(([indicatorName, indicatorData]) => {
                if (typeof indicatorData === 'object') {
                    Object.entries(indicatorData).forEach(([subName, values]) => {
                        if (Array.isArray(values)) {
                            const length = values.length;
                            const aligned = length === klineLength;
                            addResult('alignment-results', 
                                `${indicatorName}.${subName}: 长度=${length} ${aligned ? '✅对齐' : '❌不对齐'}`, 
                                aligned ? 'success' : 'warning'
                            );
                        }
                    });
                }
            });
        }

        async function runAllIndicatorTests() {
            clearAllResults();
            addResult('api-results', '🚀 开始运行全部技术指标测试...', 'info');
            
            // 依次运行所有测试
            await testStockAPI();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testKlineAPI();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testIndicatorAPI();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testMACDData();
            testKDJData();
            testRSIData();
            testMAData();
            testDataAlignment();
            
            addResult('api-results', '🎉 全部技术指标测试完成！', 'success');
        }

        function openMainApp() {
            window.open('http://localhost:5001/professional.html', '_blank');
        }

        // 页面加载完成后自动运行基础测试
        document.addEventListener('DOMContentLoaded', () => {
            addResult('api-results', '页面加载完成，可以开始测试...', 'info');
        });
    </script>
</body>
</html>
