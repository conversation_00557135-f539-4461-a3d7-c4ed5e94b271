<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>指标范围验证测试</title>
    <style>
        body {
            background: #0f1419;
            color: #d1d4dc;
            font-family: monospace;
            margin: 20px;
            line-height: 1.6;
        }
        
        .test-section {
            background: #21262d;
            border: 1px solid #30363d;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .test-section h2 {
            color: #58a6ff;
            margin-top: 0;
        }
        
        .data-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 12px;
        }
        
        .highlight {
            color: #ff7b72;
            font-weight: bold;
        }
        
        .success {
            color: #7ee787;
        }
        
        .warning {
            color: #f0883e;
        }
        
        button {
            background: #238636;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 5px;
        }
        
        button:hover {
            background: #2ea043;
        }
        
        pre {
            background: #161b22;
            border: 1px solid #30363d;
            border-radius: 6px;
            padding: 15px;
            overflow-x: auto;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <h1>📊 技术指标数值范围验证</h1>
    
    <button onclick="testRanges()">测试指标范围</button>
    <button onclick="testSpecificStock('000001')">测试平安银行</button>
    <button onclick="testSpecificStock('000002')">测试万科A</button>
    <button onclick="testSpecificStock('600036')">测试招商银行</button>
    
    <div class="test-section">
        <h2>🎯 测试结果</h2>
        <div id="test-results">点击按钮开始测试...</div>
    </div>
    
    <div class="test-section">
        <h2>📈 KDJ数值分析</h2>
        <div id="kdj-analysis">等待数据...</div>
    </div>
    
    <div class="test-section">
        <h2>📊 RSI数值分析</h2>
        <div id="rsi-analysis">等待数据...</div>
    </div>
    
    <div class="test-section">
        <h2>📉 MACD数值分析</h2>
        <div id="macd-analysis">等待数据...</div>
    </div>

    <script>
        async function testRanges() {
            document.getElementById('test-results').innerHTML = '正在测试...';
            
            try {
                const stocks = ['000001', '000002', '600036', '600519', '000858'];
                const results = [];
                
                for (const stock of stocks) {
                    const result = await testSpecificStock(stock, false);
                    results.push(result);
                }
                
                displaySummaryResults(results);
            } catch (error) {
                document.getElementById('test-results').innerHTML = `<span class="highlight">测试失败: ${error.message}</span>`;
            }
        }
        
        async function testSpecificStock(stockCode, displayDetails = true) {
            try {
                // 获取股票数据
                const response = await fetch(`/api/stock/${stockCode}`);
                const data = await response.json();
                
                if (!data.klines || !data.indicators) {
                    throw new Error('数据格式错误');
                }
                
                const klines = data.klines.slice(-30);
                const indicators = data.indicators;
                
                const analysis = {
                    stock: stockCode,
                    kdj: analyzeKDJ(indicators.kdj, klines),
                    rsi: analyzeRSI(indicators.rsi, klines),
                    macd: analyzeMacd(indicators.macd, klines)
                };
                
                if (displayDetails) {
                    displayDetailedAnalysis(analysis);
                }
                
                return analysis;
            } catch (error) {
                const errorMsg = `${stockCode}: ${error.message}`;
                if (displayDetails) {
                    document.getElementById('test-results').innerHTML = `<span class="highlight">${errorMsg}</span>`;
                }
                return { stock: stockCode, error: error.message };
            }
        }
        
        function analyzeKDJ(kdjData, klines) {
            if (!kdjData) return { error: 'KDJ数据缺失' };
            
            let allValues = [];
            const lines = ['k', 'd', 'j'];
            const lineData = {};
            
            lines.forEach(line => {
                if (kdjData[line]) {
                    const values = kdjData[line].slice(-klines.length).filter(v => v !== null && v !== undefined && !isNaN(v));
                    allValues = allValues.concat(values);
                    lineData[line] = {
                        min: Math.min(...values),
                        max: Math.max(...values),
                        count: values.length
                    };
                }
            });
            
            return {
                overall: {
                    min: Math.min(...allValues),
                    max: Math.max(...allValues),
                    count: allValues.length
                },
                lines: lineData,
                exceedsNormal: allValues.some(v => v < 0 || v > 100)
            };
        }
        
        function analyzeRSI(rsiData, klines) {
            if (!rsiData) return { error: 'RSI数据缺失' };
            
            let allValues = [];
            const lineData = {};
            
            Object.entries(rsiData).forEach(([line, values]) => {
                if (values) {
                    const validValues = values.slice(-klines.length).filter(v => v !== null && v !== undefined && !isNaN(v));
                    allValues = allValues.concat(validValues);
                    lineData[line] = {
                        min: Math.min(...validValues),
                        max: Math.max(...validValues),
                        count: validValues.length
                    };
                }
            });
            
            return {
                overall: {
                    min: Math.min(...allValues),
                    max: Math.max(...allValues),
                    count: allValues.length
                },
                lines: lineData,
                exceedsNormal: allValues.some(v => v < 0 || v > 100)
            };
        }
        
        function analyzeMacd(macdData, klines) {
            if (!macdData) return { error: 'MACD数据缺失' };
            
            let allValues = [];
            const lineData = {};
            
            ['macd', 'dif', 'dea'].forEach(line => {
                if (macdData[line]) {
                    const values = macdData[line].slice(-klines.length).filter(v => v !== null && v !== undefined);
                    allValues = allValues.concat(values);
                    lineData[line] = {
                        min: Math.min(...values),
                        max: Math.max(...values),
                        count: values.length
                    };
                }
            });
            
            return {
                overall: {
                    min: Math.min(...allValues),
                    max: Math.max(...allValues),
                    count: allValues.length
                },
                lines: lineData
            };
        }
        
        function displayDetailedAnalysis(analysis) {
            // 显示KDJ分析
            if (analysis.kdj.error) {
                document.getElementById('kdj-analysis').innerHTML = `<span class="highlight">${analysis.kdj.error}</span>`;
            } else {
                const kdj = analysis.kdj;
                let html = `
                    <div class="data-row"><span>整体范围:</span><span class="${kdj.exceedsNormal ? 'warning' : 'success'}">${kdj.overall.min.toFixed(2)} ~ ${kdj.overall.max.toFixed(2)}</span></div>
                    <div class="data-row"><span>是否超出0-100:</span><span class="${kdj.exceedsNormal ? 'warning' : 'success'}">${kdj.exceedsNormal ? '是' : '否'}</span></div>
                `;
                
                Object.entries(kdj.lines).forEach(([line, data]) => {
                    html += `<div class="data-row"><span>${line.toUpperCase()}线:</span><span>${data.min.toFixed(2)} ~ ${data.max.toFixed(2)} (${data.count}个值)</span></div>`;
                });
                
                document.getElementById('kdj-analysis').innerHTML = html;
            }
            
            // 显示RSI分析
            if (analysis.rsi.error) {
                document.getElementById('rsi-analysis').innerHTML = `<span class="highlight">${analysis.rsi.error}</span>`;
            } else {
                const rsi = analysis.rsi;
                let html = `
                    <div class="data-row"><span>整体范围:</span><span class="${rsi.exceedsNormal ? 'warning' : 'success'}">${rsi.overall.min.toFixed(2)} ~ ${rsi.overall.max.toFixed(2)}</span></div>
                    <div class="data-row"><span>是否超出0-100:</span><span class="${rsi.exceedsNormal ? 'warning' : 'success'}">${rsi.exceedsNormal ? '是' : '否'}</span></div>
                `;
                
                Object.entries(rsi.lines).forEach(([line, data]) => {
                    html += `<div class="data-row"><span>${line}:</span><span>${data.min.toFixed(2)} ~ ${data.max.toFixed(2)} (${data.count}个值)</span></div>`;
                });
                
                document.getElementById('rsi-analysis').innerHTML = html;
            }
            
            // 显示MACD分析
            if (analysis.macd.error) {
                document.getElementById('macd-analysis').innerHTML = `<span class="highlight">${analysis.macd.error}</span>`;
            } else {
                const macd = analysis.macd;
                let html = `
                    <div class="data-row"><span>整体范围:</span><span class="success">${macd.overall.min.toFixed(4)} ~ ${macd.overall.max.toFixed(4)}</span></div>
                `;
                
                Object.entries(macd.lines).forEach(([line, data]) => {
                    html += `<div class="data-row"><span>${line.toUpperCase()}:</span><span>${data.min.toFixed(4)} ~ ${data.max.toFixed(4)} (${data.count}个值)</span></div>`;
                });
                
                document.getElementById('macd-analysis').innerHTML = html;
            }
            
            document.getElementById('test-results').innerHTML = `<span class="success">✅ ${analysis.stock} 分析完成</span>`;
        }
        
        function displaySummaryResults(results) {
            let html = '<h3>📊 多股票范围统计</h3>';
            
            results.forEach(result => {
                if (result.error) {
                    html += `<div class="data-row"><span>${result.stock}:</span><span class="highlight">错误 - ${result.error}</span></div>`;
                } else {
                    const kdjExceeds = result.kdj.exceedsNormal ? '⚠️' : '✅';
                    const rsiExceeds = result.rsi.exceedsNormal ? '⚠️' : '✅';
                    
                    html += `
                        <div class="data-row">
                            <span>${result.stock}:</span>
                            <span>KDJ ${kdjExceeds} ${result.kdj.overall.min.toFixed(1)}~${result.kdj.overall.max.toFixed(1)} | 
                                  RSI ${rsiExceeds} ${result.rsi.overall.min.toFixed(1)}~${result.rsi.overall.max.toFixed(1)}</span>
                        </div>
                    `;
                }
            });
            
            document.getElementById('test-results').innerHTML = html;
        }
    </script>
</body>
</html>
