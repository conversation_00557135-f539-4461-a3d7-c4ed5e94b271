<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专业股票分析系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="style.css">
    <style>
        /* 专业股票界面布局 */
        .professional-layout {
            display: flex;
            flex-direction: column;
            min-height: 100vh; /* 最小高度100vh，但可以超出 */
            background: var(--chart-bg);
        }



        .top-toolbar {
            display: flex;
            align-items: center;
            gap: 20px;
            padding: 8px 16px;
            background: var(--bg-secondary);
            border-bottom: 1px solid var(--border-color);
            flex-wrap: wrap;
        }

        /* 操作员专用周期控制 */
        .period-controls {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 12px;
            background: var(--bg-primary);
            border-radius: 6px;
            border: 1px solid var(--border-color);
        }

        .period-group {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .period-group-label {
            font-size: 11px;
            color: var(--text-muted);
            font-weight: 600;
            margin-right: 6px;
            min-width: 30px;
        }

        .period-divider {
            width: 1px;
            height: 24px;
            background: var(--border-color);
            margin: 0 4px;
        }

        /* 基础周期按钮 */
        .period-btn {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            color: var(--text-secondary);
            padding: 6px 10px;
            font-size: 11px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 40px;
            text-align: center;
            font-weight: 500;
            position: relative;
        }

        /* K线周期按钮 */
        .period-btn.kline {
            background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
            color: white;
            border-color: #1976d2;
        }

        .period-btn.kline:hover {
            background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
        }

        .period-btn.kline.active {
            background: linear-gradient(135deg, #1565c0 0%, #0d47a1 100%);
            box-shadow: 0 2px 12px rgba(33, 150, 243, 0.4);
        }

        /* 分时图周期按钮 */
        .period-btn.intraday {
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
            color: white;
            border-color: #f57c00;
        }

        .period-btn.intraday:hover {
            background: linear-gradient(135deg, #f57c00 0%, #ef6c00 100%);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(255, 152, 0, 0.3);
        }

        .period-btn.intraday.active {
            background: linear-gradient(135deg, #ef6c00 0%, #e65100 100%);
            box-shadow: 0 2px 12px rgba(255, 152, 0, 0.4);
        }

        /* 快捷键提示 */
        .period-btn .hotkey {
            position: absolute;
            top: -8px;
            right: -8px;
            background: var(--accent-color);
            color: white;
            font-size: 8px;
            padding: 1px 3px;
            border-radius: 2px;
            font-weight: 600;
        }

        /* 实时指示器 */
        .period-btn.realtime::after {
            content: '';
            position: absolute;
            top: 2px;
            right: 2px;
            width: 6px;
            height: 6px;
            background: #4caf50;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        /* 当前选中周期的特殊样式 */
        .period-btn.active {
            transform: scale(1.05);
            z-index: 2;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .period-controls {
                flex-wrap: wrap;
                gap: 8px;
            }

            .period-btn {
                min-width: 35px;
                padding: 4px 8px;
                font-size: 10px;
            }
        }

        .ma-controls-top {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .ma-label {
            color: var(--text-muted);
            font-size: 12px;
            margin-right: 4px;
        }

        /* 操作员紧凑均线控制 */
        .compact-ma-controls {
            display: flex;
            align-items: center;
            gap: 12px;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 6px 12px;
            margin-bottom: 8px;
        }

        .ma-label {
            color: var(--text-primary);
            font-size: 12px;
            font-weight: 600;
            margin-right: 4px;
            min-width: 30px;
        }

        .ma-buttons-row {
            display: flex;
            gap: 4px;
            align-items: center;
        }

        .ma-btn.compact {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            color: var(--text-secondary);
            padding: 4px 8px;
            border-radius: 3px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 11px;
            font-weight: 600;
            min-width: 28px;
            text-align: center;
        }

        .ma-btn.compact:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
            transform: translateY(-1px);
        }

        .ma-btn.compact.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
            box-shadow: 0 1px 4px rgba(33, 150, 243, 0.3);
        }

        .ma-presets {
            display: flex;
            gap: 3px;
            margin-left: 8px;
            padding-left: 8px;
            border-left: 1px solid var(--border-color);
        }

        .preset-btn {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            color: var(--text-secondary);
            padding: 4px 6px;
            border-radius: 3px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 10px;
            font-weight: 600;
            min-width: 20px;
            text-align: center;
        }

        .preset-btn:hover {
            background: var(--accent-color);
            color: white;
            border-color: var(--accent-color);
        }

        .preset-btn.clear {
            background: var(--bg-tertiary);
            color: var(--text-muted);
        }

        .preset-btn.clear:hover {
            background: #f44336;
            color: white;
            border-color: #f44336;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .compact-ma-controls {
                flex-wrap: wrap;
                gap: 8px;
            }

            .ma-buttons-row {
                flex-wrap: wrap;
            }

            .ma-presets {
                margin-left: 0;
                padding-left: 0;
                border-left: none;
                border-top: 1px solid var(--border-color);
                padding-top: 4px;
            }
        }

        .main-chart-area {
            display: flex;
            flex: 1;
            /* 移除overflow hidden，让内容可以超出 */
        }

        .chart-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: var(--chart-bg);
        }

        .main-chart {
            flex: 1;
            position: relative;
            min-height: 400px;
        }

        .sub-charts {
            /* 动态高度，根据内容自动调整 */
            border-top: 1px solid var(--border-color);
            display: flex !important;
            flex-direction: column !important;
            gap: 8px !important;
            padding: 8px;
            background: #1a1f29;
            margin-bottom: 0; /* 移除底部边距 */
        }

        .sub-chart {
            /* 使用JavaScript动态设置的固定高度 */
            border-bottom: 1px solid var(--border-color);
            flex-shrink: 0; /* 不压缩 */
        }

        .sub-chart-header {
            padding: 4px 8px;
            background: var(--bg-secondary);
            border-bottom: 1px solid var(--border-color);
        }

        .sub-chart-title {
            color: var(--text-secondary);
            font-size: 12px;
            font-weight: 500;
        }

        .sub-chart-content {
            height: calc(100% - 28px);
            position: relative;
        }

        .info-panel {
            width: 320px;
            background: var(--bg-secondary);
            border-left: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            overflow-y: auto;
        }

        /* 操作员面板样式 */
        .operator-panel {
            padding: 0;
            background: var(--bg-secondary);
            display: flex;
            flex-direction: column;
            gap: 0;
        }

        /* 1. 股票标识区 */
        .stock-identity-section {
            padding: 16px;
            background: linear-gradient(135deg, var(--bg-primary) 0%, rgba(45, 55, 72, 0.9) 100%);
            border-bottom: 2px solid var(--primary-color);
        }

        .stock-title-row {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 8px;
        }

        .stock-name {
            font-size: 18px;
            font-weight: 700;
            color: var(--text-primary);
        }

        .stock-code {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-muted);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-family: 'Monaco', 'Menlo', monospace;
        }

        .stock-status-row {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 11px;
        }

        .market-badge, .status-badge {
            background: var(--primary-color);
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
        }

        .status-badge {
            background: var(--color-up);
        }

        .update-time {
            color: var(--text-muted);
            margin-left: auto;
        }

        /* 2. 实时价格区 */
        .price-display-section {
            padding: 16px;
            background: var(--bg-primary);
            border-bottom: 1px solid var(--border-color);
        }

        .current-price-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 12px;
        }

        .price-large {
            font-size: 32px;
            font-weight: 800;
            color: var(--text-primary);
            font-family: 'Monaco', 'Menlo', monospace;
            line-height: 1;
        }

        .price-change-group {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 4px;
        }

        .change-amount,
        .change-percent {
            font-size: 14px;
            font-weight: 600;
            font-family: 'Monaco', 'Menlo', monospace;
            padding: 3px 6px;
            border-radius: 3px;
            min-width: 60px;
            text-align: center;
        }

        .change-amount.up,
        .change-percent.up {
            color: var(--color-up);
            background: rgba(244, 67, 54, 0.15);
        }

        .change-amount.down,
        .change-percent.down {
            color: var(--color-down);
            background: rgba(76, 175, 80, 0.15);
        }

        .change-amount.flat,
        .change-percent.flat {
            color: var(--text-muted);
            background: rgba(158, 158, 158, 0.15);
        }

        .price-range-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
        }

        .price-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 6px 8px;
            background: var(--bg-tertiary);
            border-radius: 4px;
            font-size: 12px;
        }

        .price-label {
            color: var(--text-muted);
            font-weight: 500;
        }

        .price-value {
            color: var(--text-primary);
            font-family: 'Monaco', 'Menlo', monospace;
            font-weight: 600;
        }

        .price-value.high {
            color: var(--color-up);
        }

        .price-value.low {
            color: var(--color-down);
        }

        /* 通用区域样式 */
        .section-title {
            font-size: 13px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 12px;
            padding-bottom: 6px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        /* 3. 成交数据区 */
        .trading-data-section {
            padding: 16px;
            background: var(--bg-secondary);
            border-bottom: 1px solid var(--border-color);
        }

        .trading-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
        }

        .trading-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px;
            background: var(--bg-tertiary);
            border-radius: 4px;
            font-size: 12px;
        }

        .trading-label {
            color: var(--text-muted);
            font-weight: 500;
        }

        .trading-value {
            color: var(--text-primary);
            font-family: 'Monaco', 'Menlo', monospace;
            font-weight: 600;
        }

        /* 4. 估值指标区 */
        .valuation-section {
            padding: 16px;
            background: var(--bg-primary);
            border-bottom: 1px solid var(--border-color);
        }

        .valuation-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
        }

        .valuation-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px;
            background: var(--bg-secondary);
            border-radius: 4px;
            font-size: 12px;
        }

        .valuation-label {
            color: var(--text-muted);
            font-weight: 500;
        }

        .valuation-value {
            color: var(--text-primary);
            font-family: 'Monaco', 'Menlo', monospace;
            font-weight: 600;
        }

        /* 5. 资金流向区 */
        .fund-flow-section {
            padding: 16px;
            background: var(--bg-secondary);
            border-bottom: 1px solid var(--border-color);
        }

        .flow-indicator {
            font-size: 11px;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: 600;
        }

        .flow-indicator.inflow {
            background: rgba(244, 67, 54, 0.2);
            color: var(--color-up);
        }

        .flow-indicator.outflow {
            background: rgba(76, 175, 80, 0.2);
            color: var(--color-down);
        }

        .fund-flow-grid {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .flow-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px;
            background: var(--bg-tertiary);
            border-radius: 4px;
            font-size: 12px;
        }

        .flow-item.main-flow {
            background: linear-gradient(135deg, var(--bg-primary) 0%, rgba(45, 55, 72, 0.8) 100%);
            border: 1px solid var(--primary-color);
        }

        .flow-label {
            color: var(--text-muted);
            font-weight: 500;
        }

        .flow-value {
            color: var(--text-primary);
            font-family: 'Monaco', 'Menlo', monospace;
            font-weight: 600;
        }

        .flow-value.positive {
            color: var(--color-up);
        }

        .flow-value.negative {
            color: var(--color-down);
        }

        /* 6. 技术信号区 */
        .technical-signals-section {
            padding: 16px;
            background: var(--bg-primary);
            border-bottom: 1px solid var(--border-color);
        }

        .signals-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
        }

        .signal-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px;
            background: var(--bg-secondary);
            border-radius: 4px;
            font-size: 12px;
        }

        .signal-label {
            color: var(--text-muted);
            font-weight: 500;
        }

        .signal-value {
            color: var(--text-primary);
            font-weight: 600;
        }

        .signal-value.bullish {
            color: var(--color-up);
        }

        .signal-value.bearish {
            color: var(--color-down);
        }

        .signal-value.neutral {
            color: var(--text-muted);
        }

        /* 7. 操作建议区 */
        .operation-advice-section {
            padding: 16px;
            background: var(--bg-secondary);
            border-bottom: 1px solid var(--border-color);
        }

        .advice-content {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .risk-level {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .risk-label {
            color: var(--text-muted);
            font-size: 12px;
            font-weight: 500;
        }

        .risk-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 600;
        }

        .risk-badge.low {
            background: rgba(76, 175, 80, 0.2);
            color: var(--color-down);
        }

        .risk-badge.medium {
            background: rgba(255, 193, 7, 0.2);
            color: #ff9800;
        }

        .risk-badge.high {
            background: rgba(244, 67, 54, 0.2);
            color: var(--color-up);
        }

        .operation-suggestion {
            padding: 12px;
            background: var(--bg-primary);
            border-radius: 6px;
            border-left: 3px solid var(--primary-color);
        }

        .advice-text {
            color: var(--text-primary);
            font-size: 12px;
            line-height: 1.4;
        }

        /* 8. 快速操作区 */
        .quick-actions-section {
            padding: 16px;
            background: var(--bg-primary);
        }

        .action-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
        }

        .action-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            padding: 10px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: var(--bg-secondary);
            color: var(--text-primary);
            font-size: 11px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
            transform: translateY(-1px);
        }

        .action-btn i {
            font-size: 12px;
        }

        .watch-btn:hover {
            background: #ff9800;
            border-color: #ff9800;
        }

        .alert-btn:hover {
            background: #2196f3;
            border-color: #2196f3;
        }

        .refresh-btn:hover {
            background: #4caf50;
            border-color: #4caf50;
        }

        .export-btn:hover {
            background: #9c27b0;
            border-color: #9c27b0;
        }

        /* 操作员专用技术指标控制栏 */
        .operator-indicator-toolbar {
            background: var(--bg-primary);
            border-top: 2px solid var(--primary-color);
            padding: 16px;
        }

        .indicator-control-panel {
            display: flex;
            gap: 24px;
            align-items: flex-start;
        }

        .core-indicators-group,
        .extended-indicators-group {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .indicator-group-label {
            color: var(--text-primary);
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .core-indicators,
        .extended-indicators {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }

        /* 核心指标按钮 */
        .indicator-btn.core-indicator {
            display: flex;
            align-items: center;
            gap: 12px;
            background: var(--bg-secondary);
            border: 2px solid var(--border-color);
            color: var(--text-secondary);
            padding: 12px 16px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 120px;
        }

        .indicator-info {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 2px;
        }

        .indicator-name {
            font-size: 13px;
            font-weight: 700;
        }

        .indicator-desc {
            font-size: 10px;
            color: var(--text-muted);
        }

        .indicator-status {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: var(--bg-tertiary);
        }

        .indicator-status i {
            font-size: 12px;
        }

        /* 核心指标激活状态 */
        .indicator-btn.core-indicator.active {
            background: linear-gradient(135deg, var(--primary-color) 0%, rgba(33, 150, 243, 0.8) 100%);
            border-color: var(--primary-color);
            color: white;
            box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
        }

        .indicator-btn.core-indicator.active .indicator-desc {
            color: rgba(255, 255, 255, 0.8);
        }

        .indicator-btn.core-indicator.active .indicator-status {
            background: rgba(255, 255, 255, 0.2);
        }

        /* 扩展指标按钮 */
        .indicator-btn.extended-indicator {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            color: var(--text-secondary);
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 11px;
            font-weight: 600;
        }

        .indicator-btn.extended-indicator.active {
            background: var(--accent-color);
            border-color: var(--accent-color);
            color: white;
            box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
        }

        .indicator-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        /* 指标快速操作 */
        .indicator-quick-actions {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-left: auto;
        }

        .indicator-action-btn {
            display: flex;
            align-items: center;
            gap: 6px;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            color: var(--text-secondary);
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 11px;
            font-weight: 500;
            min-width: 80px;
        }

        .indicator-action-btn:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .indicator-action-btn i {
            font-size: 10px;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .indicator-control-panel {
                flex-direction: column;
                gap: 16px;
            }

            .indicator-quick-actions {
                flex-direction: row;
                margin-left: 0;
            }
        }

        .chart-placeholder {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: var(--text-muted);
            flex-direction: column;
            gap: 16px;
        }

        .placeholder-content i {
            font-size: 48px;
            color: var(--text-muted);
        }

        .search-input {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            width: 200px;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .search-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
        }

        .search-btn:hover {
            background: #1557b0;
        }

        /* 指数显示栏样式 */
        .indices-bar {
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding: 12px 20px;
            background: linear-gradient(135deg, var(--bg-primary) 0%, rgba(45, 55, 72, 0.8) 100%);
            border-radius: 8px;
            margin-bottom: 16px;
            border: 1px solid var(--border-color);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .index-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 6px;
            transition: all 0.2s ease;
            min-width: 120px;
        }

        .index-item:hover {
            background: rgba(255, 255, 255, 0.05);
            transform: translateY(-1px);
        }

        .index-name {
            font-size: 12px;
            color: var(--text-muted);
            font-weight: 500;
        }

        .index-value {
            font-size: 16px;
            color: var(--text-primary);
            font-weight: 700;
            font-family: 'Monaco', 'Menlo', monospace;
        }

        .index-change {
            font-size: 12px;
            font-weight: 600;
            font-family: 'Monaco', 'Menlo', monospace;
            padding: 2px 6px;
            border-radius: 3px;
        }

        .index-change.up {
            color: var(--color-up);
            background: rgba(244, 67, 54, 0.1);
        }

        .index-change.down {
            color: var(--color-down);
            background: rgba(76, 175, 80, 0.1);
        }

        .index-change.flat {
            color: var(--text-muted);
            background: rgba(158, 158, 158, 0.1);
        }

        /* 加载指示器样式 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(15, 20, 25, 0.95);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .loading-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .loading-spinner {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 16px;
            color: var(--text-primary);
        }

        .loading-spinner i {
            font-size: 32px;
            color: var(--primary-color);
        }

        .loading-spinner span {
            font-size: 16px;
            color: var(--text-secondary);
        }
    </style>
</head>
<body>
    <div class="app-container">
        <main class="main-content">
            <!-- 专业股票分析界面 -->
            <div class="professional-layout">


                <!-- 指数显示栏 -->
                <div class="indices-bar">
                    <div class="index-item" data-code="399300">
                        <span class="index-name">沪深300</span>
                        <span class="index-value" id="sh-value">--</span>
                        <span class="index-change" id="sh-change">--</span>
                    </div>
                    <div class="index-item" data-code="399001">
                        <span class="index-name">深证成指</span>
                        <span class="index-value" id="sz-value">--</span>
                        <span class="index-change" id="sz-change">--</span>
                    </div>
                    <div class="index-item" data-code="399006">
                        <span class="index-name">创业板指</span>
                        <span class="index-value" id="cy-value">--</span>
                        <span class="index-change" id="cy-change">--</span>
                    </div>
                    <div class="index-item" data-code="399905">
                        <span class="index-name">中证500</span>
                        <span class="index-value" id="kc-value">--</span>
                        <span class="index-change" id="kc-change">--</span>
                    </div>
                </div>

                <!-- 顶部工具栏 -->
                <div class="top-toolbar">
                    <div class="search-container" style="display: flex; gap: 8px; align-items: center;">
                        <input type="text" id="stock-search" placeholder="输入股票代码或名称" class="search-input">
                        <button id="search-btn" class="search-btn">搜索</button>
                    </div>
                    
                    <!-- 操作员专用周期控制 -->
                    <div class="period-controls">
                        <!-- K线周期组 -->
                        <div class="period-group">
                            <span class="period-group-label">K线</span>
                            <button class="period-btn kline active" data-period="1d" data-hotkey="8">
                                日K
                                <span class="hotkey">8</span>
                            </button>
                            <button class="period-btn kline" data-period="1w" data-hotkey="9">
                                周K
                                <span class="hotkey">9</span>
                            </button>
                            <button class="period-btn kline" data-period="1M" data-hotkey="0">
                                月K
                                <span class="hotkey">0</span>
                            </button>
                        </div>

                        <div class="period-divider"></div>

                        <!-- 分时图周期组 -->
                        <div class="period-group">
                            <span class="period-group-label">分时</span>
                            <button class="period-btn intraday realtime" data-period="tick" data-hotkey="1">
                                分时
                                <span class="hotkey">1</span>
                            </button>
                            <button class="period-btn intraday" data-period="1m" data-hotkey="2">
                                1分
                                <span class="hotkey">2</span>
                            </button>
                            <button class="period-btn intraday" data-period="5m" data-hotkey="3">
                                5分
                                <span class="hotkey">3</span>
                            </button>
                            <button class="period-btn intraday" data-period="15m" data-hotkey="4">
                                15分
                                <span class="hotkey">4</span>
                            </button>
                            <button class="period-btn intraday" data-period="30m" data-hotkey="5">
                                30分
                                <span class="hotkey">5</span>
                            </button>
                            <button class="period-btn intraday" data-period="60m" data-hotkey="6">
                                60分
                                <span class="hotkey">6</span>
                            </button>
                            <button class="period-btn intraday" data-period="120m" data-hotkey="7">
                                120分
                                <span class="hotkey">7</span>
                            </button>
                        </div>

                        <div class="period-divider"></div>

                        <!-- 快速切换组 -->
                        <div class="period-group">
                            <span class="period-group-label">快速</span>
                            <button class="period-btn" data-period="prev" title="上一个周期 [Shift+Q]">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <button class="period-btn" data-period="next" title="下一个周期 [Shift+E]">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                            <button class="period-btn" data-period="refresh" title="刷新当前周期 [`]">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 操作员紧凑均线控制 -->
                    <div class="compact-ma-controls">
                        <span class="ma-label">均线</span>
                        <div class="ma-buttons-row">
                            <button class="ma-btn compact active" data-ma="5" data-color="#ff9800" title="MA5 [Q]">5</button>
                            <button class="ma-btn compact active" data-ma="10" data-color="#9c27b0" title="MA10 [W]">10</button>
                            <button class="ma-btn compact active" data-ma="20" data-color="#2196f3" title="MA20 [E]">20</button>
                            <button class="ma-btn compact" data-ma="30" data-color="#4caf50" title="MA30 [A]">30</button>
                            <button class="ma-btn compact" data-ma="60" data-color="#f44336" title="MA60 [S]">60</button>
                            <button class="ma-btn compact" data-ma="120" data-color="#795548" title="MA120 [Z]">120</button>
                            <button class="ma-btn compact" data-ma="250" data-color="#607d8b" title="MA250 [X]">250</button>
                        </div>
                        <div class="ma-presets">
                            <button class="preset-btn" id="ma-preset-short" title="短线 (5,10,20)">短</button>
                            <button class="preset-btn" id="ma-preset-swing" title="波段 (10,20,60)">波</button>
                            <button class="preset-btn" id="ma-preset-trend" title="趋势 (20,60,120)">趋</button>
                            <button class="preset-btn clear" id="ma-clear-all" title="清除所有">×</button>
                        </div>
                    </div>
                </div>

                <!-- 主要内容区域 -->
                <div class="main-chart-area">
                    <!-- 左侧图表区域 -->
                    <div class="chart-container">
                        <!-- 主图表 -->
                        <div id="main-chart" class="main-chart">
                            <div class="chart-placeholder">
                                <div class="placeholder-content">
                                    <i class="fas fa-chart-line"></i>
                                    <p>请搜索股票代码查看图表</p>
                                </div>
                            </div>
                        </div>

                        <!-- 子图表区域 -->
                        <div id="sub-charts" class="sub-charts">
                            <!-- 成交量图表 -->
                            <div id="volume-chart" class="sub-chart" style="display: block;">
                                <div class="sub-chart-header">
                                    <span class="sub-chart-title">成交量</span>
                                </div>
                                <div class="sub-chart-content"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧操作员分析面板 -->
                    <div class="info-panel">
                        <!-- 股票基本信息 -->
                        <div class="operator-panel" id="stock-panel" style="display: none;">

                            <!-- 1. 股票标识区 -->
                            <div class="stock-identity-section">
                                <div class="stock-title-row">
                                    <span id="stock-name" class="stock-name">--</span>
                                    <span id="stock-code" class="stock-code">--</span>
                                </div>
                                <div class="stock-status-row">
                                    <span id="stock-market" class="market-badge">--</span>
                                    <span id="trading-status" class="status-badge">交易中</span>
                                    <span id="last-update" class="update-time">--</span>
                                </div>
                            </div>

                            <!-- 2. 实时价格区 -->
                            <div class="price-display-section">
                                <div class="current-price-row">
                                    <span id="current-price" class="price-large">--</span>
                                    <div class="price-change-group">
                                        <span id="change-amount" class="change-amount">--</span>
                                        <span id="change-percent" class="change-percent">--</span>
                                    </div>
                                </div>
                                <div class="price-range-row">
                                    <div class="price-item">
                                        <span class="price-label">今开</span>
                                        <span id="open-price" class="price-value">--</span>
                                    </div>
                                    <div class="price-item">
                                        <span class="price-label">昨收</span>
                                        <span id="prev-close" class="price-value">--</span>
                                    </div>
                                    <div class="price-item">
                                        <span class="price-label">最高</span>
                                        <span id="high-price" class="price-value high">--</span>
                                    </div>
                                    <div class="price-item">
                                        <span class="price-label">最低</span>
                                        <span id="low-price" class="price-value low">--</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 3. 成交数据区 -->
                            <div class="trading-data-section">
                                <div class="section-title">成交数据</div>
                                <div class="trading-grid">
                                    <div class="trading-item">
                                        <span class="trading-label">成交量</span>
                                        <span id="volume" class="trading-value">--</span>
                                    </div>
                                    <div class="trading-item">
                                        <span class="trading-label">成交额</span>
                                        <span id="turnover" class="trading-value">--</span>
                                    </div>
                                    <div class="trading-item">
                                        <span class="trading-label">换手率</span>
                                        <span id="turnover-rate" class="trading-value">--</span>
                                    </div>
                                    <div class="trading-item">
                                        <span class="trading-label">振幅</span>
                                        <span id="amplitude" class="trading-value">--</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 4. 估值指标区 -->
                            <div class="valuation-section">
                                <div class="section-title">估值指标</div>
                                <div class="valuation-grid">
                                    <div class="valuation-item">
                                        <span class="valuation-label">市盈率</span>
                                        <span id="pe-ratio" class="valuation-value">--</span>
                                    </div>
                                    <div class="valuation-item">
                                        <span class="valuation-label">市净率</span>
                                        <span id="pb-ratio" class="valuation-value">--</span>
                                    </div>
                                    <div class="valuation-item">
                                        <span class="valuation-label">总市值</span>
                                        <span id="market-cap" class="valuation-value">--</span>
                                    </div>
                                    <div class="valuation-item">
                                        <span class="valuation-label">流通值</span>
                                        <span id="float-cap" class="valuation-value">--</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 5. 资金流向区 -->
                            <div class="fund-flow-section">
                                <div class="section-title">
                                    资金流向
                                    <span class="flow-indicator" id="flow-trend">--</span>
                                </div>
                                <div class="fund-flow-grid">
                                    <div class="flow-item main-flow">
                                        <span class="flow-label">主力净流入</span>
                                        <span id="main-fund-flow" class="flow-value">--</span>
                                    </div>
                                    <div class="flow-item">
                                        <span class="flow-label">超大单</span>
                                        <span id="super-large-flow" class="flow-value">--</span>
                                    </div>
                                    <div class="flow-item">
                                        <span class="flow-label">大单</span>
                                        <span id="large-flow" class="flow-value">--</span>
                                    </div>
                                    <div class="flow-item">
                                        <span class="flow-label">中单</span>
                                        <span id="medium-flow" class="flow-value">--</span>
                                    </div>
                                    <div class="flow-item">
                                        <span class="flow-label">小单</span>
                                        <span id="small-flow" class="flow-value">--</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 6. 技术信号区 -->
                            <div class="technical-signals-section">
                                <div class="section-title">技术信号</div>
                                <div class="signals-grid">
                                    <div class="signal-item">
                                        <span class="signal-label">趋势</span>
                                        <span id="trend-signal" class="signal-value">--</span>
                                    </div>
                                    <div class="signal-item">
                                        <span class="signal-label">支撑位</span>
                                        <span id="support-level" class="signal-value">--</span>
                                    </div>
                                    <div class="signal-item">
                                        <span class="signal-label">阻力位</span>
                                        <span id="resistance-level" class="signal-value">--</span>
                                    </div>
                                    <div class="signal-item">
                                        <span class="signal-label">强度</span>
                                        <span id="strength-signal" class="signal-value">--</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 7. 操作建议区 -->
                            <div class="operation-advice-section">
                                <div class="section-title">操作建议</div>
                                <div class="advice-content">
                                    <div class="risk-level">
                                        <span class="risk-label">风险等级:</span>
                                        <span id="risk-level" class="risk-badge">--</span>
                                    </div>
                                    <div class="operation-suggestion">
                                        <span id="operation-advice" class="advice-text">请选择股票查看分析建议</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 8. 快速操作区 -->
                            <div class="quick-actions-section">
                                <div class="section-title">快速操作</div>
                                <div class="action-buttons">
                                    <button class="action-btn watch-btn" id="add-to-watch">
                                        <i class="fas fa-star"></i> 加自选
                                    </button>
                                    <button class="action-btn alert-btn" id="set-alert">
                                        <i class="fas fa-bell"></i> 设提醒
                                    </button>
                                    <button class="action-btn refresh-btn" id="refresh-data">
                                        <i class="fas fa-sync-alt"></i> 刷新
                                    </button>
                                    <button class="action-btn export-btn" id="export-data">
                                        <i class="fas fa-download"></i> 导出
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作员专用技术指标控制栏 -->
                <div class="operator-indicator-toolbar">
                    <div class="indicator-control-panel">
                        <!-- 默认核心指标组 -->
                        <div class="core-indicators-group">
                            <span class="indicator-group-label">核心指标</span>
                            <div class="core-indicators">
                                <button class="indicator-btn core-indicator active" data-indicator="volume" data-color="#4caf50" title="成交量 - 量价分析基础">
                                    <div class="indicator-info">
                                        <span class="indicator-name">成交量</span>
                                        <span class="indicator-desc">VOL</span>
                                    </div>
                                    <div class="indicator-status">
                                        <i class="fas fa-chart-bar"></i>
                                    </div>
                                </button>
                                <button class="indicator-btn core-indicator active" data-indicator="macd" data-color="#ff9800" title="MACD - 趋势动量指标">
                                    <div class="indicator-info">
                                        <span class="indicator-name">MACD</span>
                                        <span class="indicator-desc">趋势</span>
                                    </div>
                                    <div class="indicator-status">
                                        <i class="fas fa-wave-square"></i>
                                    </div>
                                </button>
                                <button class="indicator-btn core-indicator active" data-indicator="kdj" data-color="#2196f3" title="KDJ - 随机震荡指标">
                                    <div class="indicator-info">
                                        <span class="indicator-name">KDJ</span>
                                        <span class="indicator-desc">震荡</span>
                                    </div>
                                    <div class="indicator-status">
                                        <i class="fas fa-chart-line"></i>
                                    </div>
                                </button>
                            </div>
                        </div>

                        <!-- 扩展指标组 -->
                        <div class="extended-indicators-group">
                            <span class="indicator-group-label">扩展指标</span>
                            <div class="extended-indicators">
                                <button class="indicator-btn extended-indicator" data-indicator="rsi" data-color="#9c27b0" title="RSI - 相对强弱指标">
                                    <span class="indicator-name">RSI</span>
                                </button>
                                <button class="indicator-btn extended-indicator" data-indicator="boll" data-color="#f44336" title="BOLL - 布林带">
                                    <span class="indicator-name">BOLL</span>
                                </button>
                                <button class="indicator-btn extended-indicator" data-indicator="cci" data-color="#795548" title="CCI - 顺势指标">
                                    <span class="indicator-name">CCI</span>
                                </button>
                                <button class="indicator-btn extended-indicator" data-indicator="wr" data-color="#607d8b" title="WR - 威廉指标">
                                    <span class="indicator-name">WR</span>
                                </button>
                                <button class="indicator-btn extended-indicator" data-indicator="bias" data-color="#ff5722" title="BIAS - 乖离率">
                                    <span class="indicator-name">BIAS</span>
                                </button>
                                <button class="indicator-btn extended-indicator" data-indicator="dmi" data-color="#3f51b5" title="DMI - 动向指标">
                                    <span class="indicator-name">DMI</span>
                                </button>
                            </div>
                        </div>

                        <!-- 指标快速操作 -->
                        <div class="indicator-quick-actions">
                            <button class="indicator-action-btn" id="indicator-preset-basic" title="基础组合 (成交量+MACD+KDJ)">
                                <i class="fas fa-layer-group"></i>
                                基础
                            </button>
                            <button class="indicator-action-btn" id="indicator-preset-trend" title="趋势组合 (MACD+BOLL+DMI)">
                                <i class="fas fa-trending-up"></i>
                                趋势
                            </button>
                            <button class="indicator-action-btn" id="indicator-preset-oscillator" title="震荡组合 (KDJ+RSI+CCI)">
                                <i class="fas fa-wave-square"></i>
                                震荡
                            </button>
                            <button class="indicator-action-btn" id="indicator-clear-all" title="清除所有指标">
                                <i class="fas fa-times"></i>
                                清除
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 加载指示器 -->
    <div class="loading-overlay show" id="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <span>正在初始化专业股票分析系统...</span>
        </div>
    </div>

    <!-- 脚本文件 -->
    <script src="dom_fix.js"></script>
    <script src="script.js?v=3.1&t=20250621001"></script>
    <script>
        // 初始化专业股票分析器
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 初始化专业股票分析系统');

            try {
                // 创建股票分析器实例
                const stockAnalyzer = new StockAnalyzer();

                // 设置为全局变量
                window.stockAnalyzer = stockAnalyzer;

                // 延迟初始化，确保DOM完全加载
                setTimeout(async () => {
                    console.log('开始专业版延迟初始化');
                    await stockAnalyzer.init();
                    console.log('✅ 专业版初始化完成');
                }, 200);

            } catch (error) {
                console.error('❌ 专业版初始化失败:', error);
            }
        });
    </script>
</body>
</html>
