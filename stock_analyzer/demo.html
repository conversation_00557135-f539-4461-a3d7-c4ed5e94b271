<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>真实股票数据演示</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        .demo-title {
            text-align: center;
            color: var(--primary-color);
            margin-bottom: 30px;
        }
        .stock-demo {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        .stock-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .stock-info h3 {
            margin: 0;
            color: var(--text-primary);
        }
        .stock-code {
            color: var(--text-muted);
            font-size: 14px;
        }
        .stock-price {
            text-align: right;
        }
        .price {
            font-size: 24px;
            font-weight: bold;
            margin: 0;
        }
        .change {
            font-size: 14px;
            margin: 5px 0 0 0;
        }
        .stock-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid var(--border-color);
        }
        .detail-item {
            text-align: center;
        }
        .detail-label {
            color: var(--text-muted);
            font-size: 12px;
            margin-bottom: 5px;
        }
        .detail-value {
            color: var(--text-primary);
            font-weight: 500;
        }
        .loading {
            text-align: center;
            color: var(--text-muted);
            padding: 20px;
        }
        .source-info {
            background: var(--bg-tertiary);
            padding: 10px;
            border-radius: 6px;
            margin-top: 15px;
            font-size: 12px;
            color: var(--text-muted);
        }
        .refresh-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 20px auto;
            display: block;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">🚀 真实股票数据演示</h1>
        
        <p style="text-align: center; color: var(--text-secondary); margin-bottom: 30px;">
            以下数据来自真实的股票API接口，每次刷新都会获取最新数据
        </p>

        <div id="stock-container">
            <div class="loading">正在加载真实股票数据...</div>
        </div>

        <button class="refresh-btn" onclick="loadAllStocks()">🔄 刷新数据</button>

        <div style="text-align: center; margin-top: 30px;">
            <a href="index.html" style="color: var(--primary-color); text-decoration: none;">
                ← 返回主应用
            </a>
        </div>
    </div>

    <script>
        // 要演示的股票列表
        const demoStocks = [
            '000001', // 平安银行
            '600519', // 贵州茅台
            '000858', // 五粮液
            '600036', // 招商银行
            '002594'  // 比亚迪
        ];

        async function loadStockData(code) {
            try {
                const response = await fetch(`http://localhost:5001/api/stock/${code}`);
                const result = await response.json();
                
                if (result.success) {
                    return result.data;
                } else {
                    throw new Error(result.error || '获取数据失败');
                }
            } catch (error) {
                console.error(`获取股票 ${code} 数据失败:`, error);
                return null;
            }
        }

        function createStockCard(data) {
            if (!data) return '';

            const isPositive = parseFloat(data.change) > 0;
            const changeColor = isPositive ? 'var(--color-up)' : 'var(--color-down)';
            const changeSymbol = isPositive ? '+' : '';

            return `
                <div class="stock-demo">
                    <div class="stock-header">
                        <div class="stock-info">
                            <h3>${data.name}</h3>
                            <div class="stock-code">${data.code} · ${data.market}</div>
                        </div>
                        <div class="stock-price">
                            <div class="price" style="color: ${changeColor}">¥${data.price}</div>
                            <div class="change" style="color: ${changeColor}">
                                ${changeSymbol}${data.change} (${changeSymbol}${data.changePercent}%)
                            </div>
                        </div>
                    </div>
                    <div class="stock-details">
                        <div class="detail-item">
                            <div class="detail-label">今开</div>
                            <div class="detail-value">¥${data.open}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">昨收</div>
                            <div class="detail-value">¥${data.prevClose}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">最高</div>
                            <div class="detail-value">¥${data.high}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">最低</div>
                            <div class="detail-value">¥${data.low}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">成交量</div>
                            <div class="detail-value">${data.volume}亿</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">成交额</div>
                            <div class="detail-value">${data.turnover}亿</div>
                        </div>
                    </div>
                    <div class="source-info">
                        📡 数据源: ${data.source || '未知'} | 
                        🕒 更新时间: ${new Date(data.timestamp * 1000).toLocaleTimeString()}
                    </div>
                </div>
            `;
        }

        async function loadAllStocks() {
            const container = document.getElementById('stock-container');
            container.innerHTML = '<div class="loading">正在获取真实股票数据...</div>';

            const stockCards = [];
            
            for (const code of demoStocks) {
                const data = await loadStockData(code);
                if (data) {
                    stockCards.push(createStockCard(data));
                }
            }

            if (stockCards.length > 0) {
                container.innerHTML = stockCards.join('');
            } else {
                container.innerHTML = '<div class="loading">❌ 无法获取股票数据，请检查网络连接</div>';
            }
        }

        // 页面加载时自动获取数据
        document.addEventListener('DOMContentLoaded', loadAllStocks);

        // 每30秒自动刷新一次
        setInterval(loadAllStocks, 30000);
    </script>
</body>
</html>
