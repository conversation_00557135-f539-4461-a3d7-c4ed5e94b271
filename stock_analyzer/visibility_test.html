<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>子图表完整显示测试</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            background: #0f1419;
            color: #d1d4dc;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-header h1 {
            color: #00d4aa;
            margin-bottom: 10px;
        }
        
        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .control-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .control-group label {
            font-size: 12px;
            color: #8b949e;
        }
        
        select, input, button {
            padding: 8px 12px;
            background: #21262d;
            border: 1px solid #30363d;
            border-radius: 6px;
            color: #d1d4dc;
            font-size: 14px;
        }
        
        button {
            background: #238636;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        button:hover {
            background: #2ea043;
        }
        
        .indicator-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .indicator-btn {
            padding: 8px 16px;
            background: #21262d;
            border: 1px solid #30363d;
            border-radius: 4px;
            color: #d1d4dc;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
        }
        
        .indicator-btn.active {
            background: #238636;
            border-color: #2ea043;
        }
        
        .indicator-btn:hover {
            border-color: #58a6ff;
        }
        
        .chart-container {
            background: #0f1419;
            border: 1px solid #30363d;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .main-chart {
            height: 400px;
            margin-bottom: 20px;
            background: #1e2329;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #8b949e;
        }
        
        .sub-charts-container {
            display: flex;
            flex-direction: column;
            min-height: 200px;
            border: 2px dashed #30363d;
            border-radius: 6px;
            position: relative;
        }
        
        .visibility-info {
            background: #21262d;
            border: 1px solid #30363d;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .visibility-info h3 {
            margin: 0 0 15px 0;
            color: #58a6ff;
        }
        
        .visibility-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding: 8px;
            background: #161b22;
            border-radius: 4px;
        }
        
        .visibility-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: bold;
        }
        
        .status-visible {
            background: #238636;
            color: white;
        }
        
        .status-partial {
            background: #f0883e;
            color: white;
        }
        
        .status-hidden {
            background: #da3633;
            color: white;
        }
        
        .height-summary {
            background: #161b22;
            border: 1px solid #30363d;
            border-radius: 6px;
            padding: 15px;
            margin-top: 10px;
        }
        
        .height-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 12px;
        }
        
        .height-value {
            color: #58a6ff;
            font-weight: bold;
        }
        
        .scroll-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #da3633;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>👁️ 子图表完整显示测试</h1>
            <p>验证所有技术指标子图表是否能够完全显示，无遮挡无截断</p>
        </div>
        
        <div class="controls">
            <div class="control-group">
                <label>股票代码</label>
                <input type="text" id="stock-code" value="000001" placeholder="输入股票代码">
            </div>
            <div class="control-group">
                <label>时间周期</label>
                <select id="period">
                    <option value="1d">日线</option>
                    <option value="1w">周线</option>
                    <option value="1M">月线</option>
                </select>
            </div>
            <div class="control-group">
                <label>操作</label>
                <button onclick="loadTestData()">加载数据</button>
            </div>
        </div>
        
        <div class="indicator-controls">
            <div class="indicator-btn active" data-indicator="volume">成交量</div>
            <div class="indicator-btn active" data-indicator="macd">MACD</div>
            <div class="indicator-btn active" data-indicator="kdj">KDJ</div>
            <div class="indicator-btn" data-indicator="rsi">RSI</div>
        </div>
        
        <div class="chart-container">
            <div id="main-chart" class="main-chart">
                主图区域 (400px)
            </div>
            <div id="sub-charts" class="sub-charts-container">
                <div class="scroll-indicator" id="scroll-indicator">
                    ⚠️ 内容超出显示区域
                </div>
            </div>
        </div>
        
        <div class="visibility-info">
            <h3>📊 子图表可见性检测</h3>
            <div id="visibility-results">
                点击"加载数据"开始检测...
            </div>
        </div>
        
        <div class="height-summary">
            <h4>📏 高度分配详情</h4>
            <div id="height-details">
                等待数据加载...
            </div>
        </div>
    </div>

    <script src="script.js"></script>
    <script>
        // 初始化股票分析器
        const stockAnalyzer = new StockAnalyzer();
        
        // 指标控制
        document.querySelectorAll('.indicator-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                this.classList.toggle('active');
                updateActiveIndicators();
                if (stockAnalyzer.lastKlineData && stockAnalyzer.lastIndicatorData) {
                    loadTestData(); // 重新加载以应用新的指标选择
                }
            });
        });
        
        function updateActiveIndicators() {
            const activeIndicators = Array.from(document.querySelectorAll('.indicator-btn.active'))
                .map(btn => btn.dataset.indicator);
            stockAnalyzer.activeIndicators = activeIndicators;
            console.log('活跃指标:', activeIndicators);
        }
        
        function loadTestData() {
            const stockCode = document.getElementById('stock-code').value;
            const period = document.getElementById('period').value;
            
            // 更新活跃指标
            updateActiveIndicators();
            
            // 加载数据
            stockAnalyzer.updateChart(period, stockCode).then(() => {
                setTimeout(() => {
                    checkVisibility();
                    updateHeightDetails();
                }, 1000); // 等待渲染完成
            }).catch(error => {
                console.error('加载数据失败:', error);
                document.getElementById('visibility-results').innerHTML = 
                    `<div class="visibility-item"><span>❌ 数据加载失败: ${error.message}</span></div>`;
            });
        }
        
        function checkVisibility() {
            const subChartsContainer = document.getElementById('sub-charts');
            const subCharts = subChartsContainer.querySelectorAll('.sub-chart');
            const containerRect = subChartsContainer.getBoundingClientRect();
            const scrollIndicator = document.getElementById('scroll-indicator');
            
            let results = '';
            let hasHidden = false;
            let hasPartial = false;
            
            if (subCharts.length === 0) {
                results = '<div class="visibility-item"><span>⚠️ 没有找到子图表</span></div>';
            } else {
                subCharts.forEach((chart, index) => {
                    const chartRect = chart.getBoundingClientRect();
                    const indicator = chart.id.replace('-chart', '');
                    
                    let status, statusClass, statusText;
                    
                    // 检查是否完全可见
                    const isFullyVisible = chartRect.top >= containerRect.top && 
                                         chartRect.bottom <= containerRect.bottom;
                    
                    // 检查是否部分可见
                    const isPartiallyVisible = chartRect.bottom > containerRect.top && 
                                             chartRect.top < containerRect.bottom;
                    
                    if (isFullyVisible) {
                        status = '完全可见';
                        statusClass = 'status-visible';
                        statusText = '✅';
                    } else if (isPartiallyVisible) {
                        status = '部分可见';
                        statusClass = 'status-partial';
                        statusText = '⚠️';
                        hasPartial = true;
                    } else {
                        status = '被遮挡';
                        statusClass = 'status-hidden';
                        statusText = '❌';
                        hasHidden = true;
                    }
                    
                    const height = chart.clientHeight;
                    const contentElement = chart.querySelector('.sub-chart-content');
                    const contentHeight = contentElement ? contentElement.clientHeight : 0;
                    
                    results += `
                        <div class="visibility-item">
                            <span>${statusText} ${stockAnalyzer.getIndicatorDisplayName(indicator)}</span>
                            <div>
                                <span class="visibility-status ${statusClass}">${status}</span>
                                <span style="margin-left: 10px; font-size: 11px; color: #8b949e;">
                                    ${height}px (内容: ${contentHeight}px)
                                </span>
                            </div>
                        </div>
                    `;
                });
                
                // 检查是否需要滚动
                const needsScroll = subChartsContainer.scrollHeight > subChartsContainer.clientHeight;
                if (needsScroll || hasHidden || hasPartial) {
                    scrollIndicator.style.display = 'block';
                } else {
                    scrollIndicator.style.display = 'none';
                }
            }
            
            document.getElementById('visibility-results').innerHTML = results;
        }
        
        function updateHeightDetails() {
            const subChartsContainer = document.getElementById('sub-charts');
            const subCharts = subChartsContainer.querySelectorAll('.sub-chart');
            const activeCount = stockAnalyzer.activeIndicators.length;
            
            if (activeCount === 0) {
                document.getElementById('height-details').innerHTML = '没有选择指标';
                return;
            }
            
            const heightConfig = stockAnalyzer.calculateSubChartHeights(activeCount);
            const actualContainerHeight = subChartsContainer.clientHeight;
            const actualScrollHeight = subChartsContainer.scrollHeight;
            
            let html = `
                <div class="height-row">
                    <span>指标数量:</span>
                    <span class="height-value">${activeCount}</span>
                </div>
                <div class="height-row">
                    <span>配置总高度:</span>
                    <span class="height-value">${heightConfig.totalHeight}px</span>
                </div>
                <div class="height-row">
                    <span>实际容器高度:</span>
                    <span class="height-value">${actualContainerHeight}px</span>
                </div>
                <div class="height-row">
                    <span>实际内容高度:</span>
                    <span class="height-value">${actualScrollHeight}px</span>
                </div>
                <div class="height-row">
                    <span>配置单个高度:</span>
                    <span class="height-value">${heightConfig.individualHeight}px</span>
                </div>
                <div class="height-row">
                    <span>配置内容高度:</span>
                    <span class="height-value">${heightConfig.contentHeight}px</span>
                </div>
                <div class="height-row">
                    <span>是否需要滚动:</span>
                    <span class="height-value ${actualScrollHeight > actualContainerHeight ? 'status-partial' : 'status-visible'}">
                        ${actualScrollHeight > actualContainerHeight ? '是' : '否'}
                    </span>
                </div>
            `;
            
            if (subCharts.length > 0) {
                const firstChart = subCharts[0];
                const actualIndividualHeight = firstChart.clientHeight;
                const contentElement = firstChart.querySelector('.sub-chart-content');
                const actualContentHeight = contentElement ? contentElement.clientHeight : 0;
                
                html += `
                    <div class="height-row">
                        <span>实际单个高度:</span>
                        <span class="height-value">${actualIndividualHeight}px</span>
                    </div>
                    <div class="height-row">
                        <span>实际内容高度:</span>
                        <span class="height-value">${actualContentHeight}px</span>
                    </div>
                `;
            }
            
            document.getElementById('height-details').innerHTML = html;
        }
        
        // 页面加载完成后自动加载测试数据
        window.addEventListener('load', function() {
            setTimeout(() => {
                loadTestData();
            }, 1000);
        });
        
        // 监听窗口大小变化
        window.addEventListener('resize', function() {
            setTimeout(() => {
                checkVisibility();
                updateHeightDetails();
            }, 100);
        });
    </script>
</body>
</html>
