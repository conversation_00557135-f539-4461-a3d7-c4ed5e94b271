<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速加载测试</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            background: var(--bg-card);
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        .status.success {
            background: rgba(76, 175, 80, 0.1);
            border: 1px solid var(--color-up);
            color: var(--color-up);
        }
        .status.error {
            background: rgba(244, 67, 54, 0.1);
            border: 1px solid var(--color-down);
            color: var(--color-down);
        }
        .status.info {
            background: rgba(33, 150, 243, 0.1);
            border: 1px solid var(--primary-color);
            color: var(--primary-color);
        }
        .test-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        .log-container {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 11px;
            color: var(--text-secondary);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center; color: var(--primary-color);">⚡ 快速加载测试</h1>
        
        <div class="test-section">
            <h3>🔍 加载问题诊断</h3>
            
            <div style="margin: 15px 0;">
                <button class="test-btn" onclick="testPageLoad()">测试页面加载</button>
                <button class="test-btn" onclick="testAPIConnection()">测试API连接</button>
                <button class="test-btn" onclick="testDOMElements()">测试DOM元素</button>
                <button class="test-btn" onclick="clearLog()">清空日志</button>
            </div>
            
            <div id="status-display"></div>
            <div id="log-container" class="log-container"></div>
        </div>

        <div class="test-section">
            <h3>🚀 简化版股票分析器</h3>
            
            <div style="margin: 15px 0;">
                <input type="text" id="stock-input" placeholder="股票代码 (如: 000858)" 
                       style="padding: 10px; background: var(--bg-tertiary); border: 1px solid var(--border-color); border-radius: 6px; color: var(--text-primary); margin-right: 10px;">
                <button class="test-btn" onclick="quickLoadStock()">快速加载股票</button>
            </div>
            
            <div id="quick-result" style="min-height: 200px; background: var(--bg-tertiary); border-radius: 8px; padding: 15px; margin: 15px 0;">
                <div style="text-align: center; color: var(--text-muted); padding: 50px;">
                    等待加载股票数据...
                </div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="index.html" style="color: var(--primary-color); text-decoration: none;">← 返回主应用</a>
        </div>
    </div>

    <script>
        // 重定向console到日志面板
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToLog(message, type = 'log') {
            const logEl = document.getElementById('log-container');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#ff4757' : type === 'warn' ? '#ffa502' : '#d1d4dc';
            logEl.innerHTML += `<div style="color: ${color};">[${timestamp}] ${message}</div>`;
            logEl.scrollTop = logEl.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToLog(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToLog('ERROR: ' + args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToLog('WARN: ' + args.join(' '), 'warn');
        };

        function showStatus(message, type = 'info') {
            const statusEl = document.getElementById('status-display');
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            statusEl.innerHTML = `<div class="status ${className}">${message}</div>`;
        }

        function testPageLoad() {
            console.log('=== 开始页面加载测试 ===');
            
            // 检查基本DOM元素
            const elements = [
                'loading-overlay',
                'price-chart', 
                'hot-stocks-list',
                'sh-index',
                'sz-index',
                'cy-index'
            ];
            
            let foundElements = 0;
            elements.forEach(id => {
                const el = document.getElementById(id);
                if (el) {
                    console.log(`✅ 找到元素: ${id}`);
                    foundElements++;
                } else {
                    console.log(`❌ 缺失元素: ${id}`);
                }
            });
            
            console.log(`DOM元素检查完成: ${foundElements}/${elements.length}`);
            
            // 检查CSS加载
            const styles = getComputedStyle(document.body);
            console.log(`CSS加载状态: ${styles.backgroundColor ? '✅ 正常' : '❌ 异常'}`);
            
            showStatus(`页面加载测试完成: ${foundElements}/${elements.length} 元素正常`, foundElements === elements.length ? 'success' : 'error');
        }

        async function testAPIConnection() {
            console.log('=== 开始API连接测试 ===');
            
            try {
                showStatus('正在测试API连接...', 'info');
                
                const response = await fetch('http://localhost:5001/api/market-status');
                const result = await response.json();
                
                if (result.success) {
                    console.log('✅ API连接正常');
                    console.log('市场状态:', result.data.message);
                    showStatus('✅ API连接测试成功', 'success');
                } else {
                    throw new Error('API返回失败');
                }
            } catch (error) {
                console.error('❌ API连接失败:', error.message);
                showStatus('❌ API连接测试失败', 'error');
            }
        }

        function testDOMElements() {
            console.log('=== 开始DOM元素测试 ===');
            
            // 检查按钮元素
            const buttons = {
                'chart-type-btn': document.querySelectorAll('.chart-type-btn').length,
                'chart-btn': document.querySelectorAll('.chart-btn').length,
                'indicator-btn': document.querySelectorAll('.indicator-btn').length
            };
            
            Object.entries(buttons).forEach(([type, count]) => {
                console.log(`${type}: ${count} 个`);
            });
            
            // 检查容器元素
            const containers = [
                'price-chart',
                'stock-info',
                'market-overview'
            ];
            
            containers.forEach(id => {
                const el = document.getElementById(id);
                if (el) {
                    console.log(`✅ 容器 ${id}: 存在 (${el.offsetWidth}x${el.offsetHeight})`);
                } else {
                    console.log(`❌ 容器 ${id}: 不存在`);
                }
            });
            
            showStatus('DOM元素测试完成', 'success');
        }

        async function quickLoadStock() {
            const stockCode = document.getElementById('stock-input').value || '000858';
            const resultEl = document.getElementById('quick-result');
            
            console.log(`=== 开始快速加载股票 ${stockCode} ===`);
            
            resultEl.innerHTML = '<div style="text-align: center; color: var(--primary-color); padding: 50px;">正在加载...</div>';
            
            try {
                // 获取股票基础数据
                const response = await fetch(`http://localhost:5001/api/stock/${stockCode}`);
                const result = await response.json();
                
                if (result.success) {
                    const data = result.data;
                    console.log('✅ 股票数据获取成功');
                    
                    const changeColor = data.change > 0 ? 'var(--color-up)' : 'var(--color-down)';
                    
                    resultEl.innerHTML = `
                        <div style="text-align: center;">
                            <h3 style="color: var(--text-primary); margin-bottom: 20px;">
                                ${data.name} (${data.code})
                            </h3>
                            <div style="font-size: 32px; color: ${changeColor}; margin-bottom: 10px;">
                                ¥${data.price}
                            </div>
                            <div style="color: ${changeColor}; margin-bottom: 20px;">
                                ${data.change > 0 ? '+' : ''}${data.change} (${data.change > 0 ? '+' : ''}${data.changePercent}%)
                            </div>
                            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px; text-align: left; max-width: 300px; margin: 0 auto;">
                                <div>开盘: <span style="color: var(--text-primary);">${data.open}</span></div>
                                <div>收盘: <span style="color: var(--text-primary);">${data.prevClose}</span></div>
                                <div>最高: <span style="color: var(--text-primary);">${data.high}</span></div>
                                <div>最低: <span style="color: var(--text-primary);">${data.low}</span></div>
                                <div>成交量: <span style="color: var(--text-primary);">${data.volume}</span></div>
                                <div>成交额: <span style="color: var(--text-primary);">${data.turnover}</span></div>
                            </div>
                            <div style="margin-top: 20px; padding: 10px; background: var(--bg-card); border-radius: 6px; font-size: 12px; color: var(--text-secondary);">
                                数据来源: ${data.source} | 状态: ${data.is_trading ? '交易中' : '已收盘'}
                            </div>
                        </div>
                    `;
                    
                    showStatus(`✅ 股票 ${stockCode} 加载成功`, 'success');
                } else {
                    throw new Error(result.error || '数据获取失败');
                }
            } catch (error) {
                console.error('❌ 股票加载失败:', error.message);
                resultEl.innerHTML = `
                    <div style="text-align: center; color: var(--color-down); padding: 50px;">
                        <div style="font-size: 24px; margin-bottom: 10px;">⚠️</div>
                        <div>股票加载失败</div>
                        <div style="font-size: 12px; margin-top: 10px;">${error.message}</div>
                    </div>
                `;
                showStatus('❌ 股票加载失败', 'error');
            }
        }

        function clearLog() {
            document.getElementById('log-container').innerHTML = '';
        }

        // 页面加载时自动运行测试
        document.addEventListener('DOMContentLoaded', () => {
            console.log('快速加载测试页面已准备就绪');
            setTimeout(() => {
                testPageLoad();
                testAPIConnection();
            }, 500);
        });
    </script>
</body>
</html>
