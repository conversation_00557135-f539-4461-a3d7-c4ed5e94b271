#!/bin/bash

echo "🚀 启动智能股票分析系统"
echo "=========================="

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装，请先安装Python3"
    exit 1
fi

echo "✅ Python3 已安装"

# 检查pip
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 未安装，请先安装pip3"
    exit 1
fi

echo "✅ pip3 已安装"

# 安装依赖
echo "📦 安装Python依赖包..."
pip3 install flask flask-cors requests

if [ $? -ne 0 ]; then
    echo "❌ 依赖安装失败"
    exit 1
fi

echo "✅ 依赖安装完成"

# 启动后端API服务
echo "🔧 启动后端API服务..."
python3 api.py &
API_PID=$!

# 等待API服务启动
sleep 3

# 检查API服务是否启动成功
if ps -p $API_PID > /dev/null; then
    echo "✅ 后端API服务启动成功 (PID: $API_PID)"
else
    echo "❌ 后端API服务启动失败"
    exit 1
fi

# 启动前端服务
echo "🌐 启动前端服务..."

# 检查是否有Python的http.server模块
if python3 -c "import http.server" 2>/dev/null; then
    echo "使用Python内置服务器..."
    python3 -m http.server 8080 &
    WEB_PID=$!
    echo "✅ 前端服务启动成功 (PID: $WEB_PID)"
    echo ""
    echo "🎉 系统启动完成！"
    echo "=========================="
    echo "📱 前端访问地址: http://localhost:8080"
    echo "🔌 后端API地址: http://localhost:5000"
    echo ""
    echo "📊 可用的API接口:"
    echo "  • GET /api/stock/<code>     - 获取股票数据"
    echo "  • GET /api/market           - 获取市场指数"
    echo "  • GET /api/hot-stocks?type= - 获取热门股票"
    echo "  • GET /api/search?q=        - 搜索股票"
    echo ""
    echo "💡 使用说明:"
    echo "  1. 在浏览器中打开 http://localhost:8080"
    echo "  2. 输入股票代码或名称进行搜索"
    echo "  3. 查看实时股价和智能分析"
    echo ""
    echo "⚠️  注意事项:"
    echo "  • 数据来源于东方财富网，仅供参考"
    echo "  • 实时数据可能有延迟"
    echo "  • 投资有风险，决策需谨慎"
    echo ""
    echo "🛑 停止服务: 按 Ctrl+C"
    
    # 等待用户中断
    trap "echo ''; echo '🛑 正在停止服务...'; kill $API_PID $WEB_PID 2>/dev/null; echo '✅ 服务已停止'; exit 0" INT
    
    # 保持脚本运行
    wait
else
    echo "❌ Python http.server模块不可用"
    echo "请手动启动前端服务:"
    echo "  方法1: 使用Python: python3 -m http.server 8080"
    echo "  方法2: 使用Node.js: npx serve -p 8080"
    echo "  方法3: 直接用浏览器打开 index.html"
    
    echo ""
    echo "后端API服务正在运行 (PID: $API_PID)"
    echo "按 Ctrl+C 停止服务"
    
    trap "echo ''; echo '🛑 正在停止API服务...'; kill $API_PID 2>/dev/null; echo '✅ API服务已停止'; exit 0" INT
    wait $API_PID
fi
