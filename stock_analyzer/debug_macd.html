<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MACD数据调试</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1e2329;
            color: #d1d4dc;
            padding: 20px;
        }
        .debug-section {
            background: rgba(30, 35, 41, 0.95);
            border: 1px solid #ffc107;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 20px;
        }
        .debug-title {
            color: #ffc107;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .data-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 10px;
            font-size: 12px;
        }
        .highlight {
            background: rgba(255, 193, 7, 0.2);
            padding: 4px;
            border-radius: 4px;
        }
        button {
            background: #ffc107;
            color: #1e2329;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }
        button:hover {
            background: #ffca28;
        }
    </style>
</head>
<body>
    <h1>🔧 MACD数据调试工具</h1>
    
    <div class="debug-section">
        <div class="debug-title">API数据测试</div>
        <button onclick="testAPI()">获取API数据</button>
        <div id="api-result"></div>
    </div>
    
    <div class="debug-section">
        <div class="debug-title">前端数据测试</div>
        <button onclick="testFrontend()">测试前端数据</button>
        <div id="frontend-result"></div>
    </div>
    
    <div class="debug-section">
        <div class="debug-title">索引测试</div>
        <button onclick="testIndex()">测试索引映射</button>
        <div id="index-result"></div>
    </div>

    <script>
        async function testAPI() {
            const result = document.getElementById('api-result');
            result.innerHTML = '正在获取API数据...';
            
            try {
                const response = await fetch('http://localhost:5001/api/indicators/000001?period=1d&count=100&indicators=macd');
                const data = await response.json();
                
                if (data.success && data.data.indicators.macd) {
                    const macd = data.data.indicators.macd;
                    const lastIndex = macd.dif.length - 1;
                    
                    result.innerHTML = `
                        <div style="margin-top: 10px;">
                            <div class="highlight">✅ API数据获取成功</div>
                            <div>数据长度: ${macd.dif.length}</div>
                            <div>最新索引: ${lastIndex}</div>
                            <div class="data-grid" style="margin-top: 10px;">
                                <div><strong>最新MACD值:</strong></div>
                                <div></div>
                                <div></div>
                                <div>DIF: <span style="color: #ffc107;">${macd.dif[lastIndex]}</span></div>
                                <div>DEA: <span style="color: #2196f3;">${macd.dea[lastIndex]}</span></div>
                                <div>MACD: <span style="color: #ff4757;">${macd.macd[lastIndex]}</span></div>
                            </div>
                            <div style="margin-top: 10px;">
                                <strong>前5个值:</strong><br>
                                DIF: ${macd.dif.slice(0, 5).join(', ')}<br>
                                DEA: ${macd.dea.slice(0, 5).join(', ')}<br>
                                MACD: ${macd.macd.slice(0, 5).join(', ')}
                            </div>
                            <div style="margin-top: 10px;">
                                <strong>后5个值:</strong><br>
                                DIF: ${macd.dif.slice(-5).join(', ')}<br>
                                DEA: ${macd.dea.slice(-5).join(', ')}<br>
                                MACD: ${macd.macd.slice(-5).join(', ')}
                            </div>
                        </div>
                    `;
                } else {
                    result.innerHTML = '<div style="color: #ff4757;">❌ API数据获取失败</div>';
                }
            } catch (error) {
                result.innerHTML = `<div style="color: #ff4757;">❌ 错误: ${error.message}</div>`;
            }
        }
        
        function testFrontend() {
            const result = document.getElementById('frontend-result');
            result.innerHTML = '检查前端数据...';
            
            // 检查是否有StockAnalyzer实例
            if (window.stockAnalyzer && window.stockAnalyzer.lastIndicatorData) {
                const data = window.stockAnalyzer.lastIndicatorData;
                if (data.indicators && data.indicators.macd) {
                    const macd = data.indicators.macd;
                    const lastIndex = macd.dif.length - 1;
                    
                    result.innerHTML = `
                        <div style="margin-top: 10px;">
                            <div class="highlight">✅ 前端数据存在</div>
                            <div>数据长度: ${macd.dif.length}</div>
                            <div>最新索引: ${lastIndex}</div>
                            <div class="data-grid" style="margin-top: 10px;">
                                <div><strong>最新MACD值:</strong></div>
                                <div></div>
                                <div></div>
                                <div>DIF: <span style="color: #ffc107;">${macd.dif[lastIndex]}</span></div>
                                <div>DEA: <span style="color: #2196f3;">${macd.dea[lastIndex]}</span></div>
                                <div>MACD: <span style="color: #ff4757;">${macd.macd[lastIndex]}</span></div>
                            </div>
                        </div>
                    `;
                } else {
                    result.innerHTML = '<div style="color: #ff4757;">❌ 前端MACD数据不存在</div>';
                }
            } else {
                result.innerHTML = '<div style="color: #ff4757;">❌ 前端StockAnalyzer实例不存在</div>';
            }
        }
        
        function testIndex() {
            const result = document.getElementById('index-result');
            result.innerHTML = '测试索引映射...';
            
            if (window.stockAnalyzer && window.stockAnalyzer.lastIndicatorData) {
                const data = window.stockAnalyzer.lastIndicatorData;
                if (data.indicators && data.indicators.macd) {
                    const macd = data.indicators.macd;
                    const length = macd.dif.length;
                    
                    let html = `
                        <div style="margin-top: 10px;">
                            <div class="highlight">索引映射测试</div>
                            <div>数据总长度: ${length}</div>
                            <div style="margin-top: 10px;">
                                <strong>不同索引的MACD值:</strong>
                            </div>
                            <div class="data-grid" style="margin-top: 5px;">
                                <div><strong>索引</strong></div>
                                <div><strong>DIF</strong></div>
                                <div><strong>DEA</strong></div>
                                <div><strong>MACD</strong></div>
                    `;
                    
                    // 测试几个关键索引
                    const testIndexes = [0, 29, length-1];
                    testIndexes.forEach(idx => {
                        if (idx < length) {
                            html += `
                                <div>${idx} ${idx === length-1 ? '(最新)' : ''}</div>
                                <div>${macd.dif[idx]}</div>
                                <div>${macd.dea[idx]}</div>
                                <div>${macd.macd[idx]}</div>
                            `;
                        }
                    });
                    
                    html += '</div></div>';
                    result.innerHTML = html;
                } else {
                    result.innerHTML = '<div style="color: #ff4757;">❌ 前端MACD数据不存在</div>';
                }
            } else {
                result.innerHTML = '<div style="color: #ff4757;">❌ 前端StockAnalyzer实例不存在</div>';
            }
        }
        
        // 自动测试
        window.addEventListener('load', () => {
            setTimeout(() => {
                testAPI();
                setTimeout(testFrontend, 1000);
            }, 500);
        });
    </script>
</body>
</html>
