<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KDJ指标显示测试</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            background: #0f1419;
            color: #d1d4dc;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-header h1 {
            color: #00d4aa;
            margin-bottom: 10px;
        }
        
        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .control-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .control-group label {
            font-size: 12px;
            color: #8b949e;
        }
        
        select, input, button {
            padding: 8px 12px;
            background: #21262d;
            border: 1px solid #30363d;
            border-radius: 6px;
            color: #d1d4dc;
            font-size: 14px;
        }
        
        button {
            background: #238636;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        button:hover {
            background: #2ea043;
        }
        
        .indicator-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .indicator-btn {
            padding: 8px 16px;
            background: #21262d;
            border: 1px solid #30363d;
            border-radius: 4px;
            color: #d1d4dc;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
        }
        
        .indicator-btn.active {
            background: #238636;
            border-color: #2ea043;
        }
        
        .indicator-btn:hover {
            border-color: #58a6ff;
        }
        
        .chart-container {
            background: #0f1419;
            border: 1px solid #30363d;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .main-chart {
            height: 400px;
            margin-bottom: 20px;
            background: #1e2329;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #8b949e;
        }
        
        .sub-charts {
            min-height: 300px;
            max-height: 1200px;
            display: flex;
            flex-direction: column;
            gap: 8px;
            overflow-y: auto;
            padding: 8px;
            background: #1a1f29;
            border-radius: 6px;
        }
        
        .debug-info {
            background: #21262d;
            border: 1px solid #30363d;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .debug-info h3 {
            margin: 0 0 15px 0;
            color: #58a6ff;
        }
        
        .debug-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding: 8px;
            background: #161b22;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
        }
        
        .debug-value {
            color: #79c0ff;
            font-weight: bold;
        }
        
        .status-indicator {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: bold;
        }
        
        .status-success {
            background: #238636;
            color: white;
        }
        
        .status-error {
            background: #da3633;
            color: white;
        }
        
        .status-warning {
            background: #f0883e;
            color: white;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>📊 KDJ指标显示测试</h1>
            <p>专门测试KDJ指标是否能够正确显示和渲染</p>
        </div>
        
        <div class="controls">
            <div class="control-group">
                <label>股票代码</label>
                <input type="text" id="stock-code" value="000001" placeholder="输入股票代码">
            </div>
            <div class="control-group">
                <label>时间周期</label>
                <select id="period">
                    <option value="1d">日线</option>
                    <option value="1w">周线</option>
                    <option value="1M">月线</option>
                </select>
            </div>
            <div class="control-group">
                <label>操作</label>
                <button onclick="loadKDJTest()">加载KDJ测试</button>
            </div>
        </div>
        
        <div class="indicator-controls">
            <div class="indicator-btn" data-indicator="volume">成交量</div>
            <div class="indicator-btn" data-indicator="macd">MACD</div>
            <div class="indicator-btn active" data-indicator="kdj">KDJ</div>
            <div class="indicator-btn" data-indicator="rsi">RSI</div>
        </div>
        
        <div class="chart-container">
            <div id="main-chart" class="main-chart">
                主图区域 (K线图)
            </div>
            <div id="sub-charts" class="sub-charts">
                <!-- 子图表将在这里动态生成 -->
            </div>
        </div>
        
        <div class="debug-info">
            <h3>🔍 调试信息</h3>
            <div id="debug-results">
                点击"加载KDJ测试"开始调试...
            </div>
        </div>
    </div>

    <script src="script.js"></script>
    <script>
        // 初始化股票分析器
        let stockAnalyzer;
        
        // 指标控制
        document.querySelectorAll('.indicator-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                this.classList.toggle('active');
                updateActiveIndicators();
            });
        });
        
        function updateActiveIndicators() {
            if (!stockAnalyzer) return;
            
            const activeIndicators = Array.from(document.querySelectorAll('.indicator-btn.active'))
                .map(btn => btn.dataset.indicator);
            stockAnalyzer.activeIndicators = activeIndicators;
            
            updateDebugInfo('activeIndicators', activeIndicators.join(', '));
            console.log('活跃指标更新:', activeIndicators);
        }
        
        function updateDebugInfo(key, value, status = 'info') {
            const debugResults = document.getElementById('debug-results');
            const existingItem = debugResults.querySelector(`[data-key="${key}"]`);
            
            const statusClass = status === 'success' ? 'status-success' : 
                               status === 'error' ? 'status-error' : 
                               status === 'warning' ? 'status-warning' : '';
            
            const itemHtml = `
                <div class="debug-item" data-key="${key}">
                    <span>${key}:</span>
                    <span class="debug-value ${statusClass}">${value}</span>
                </div>
            `;
            
            if (existingItem) {
                existingItem.outerHTML = itemHtml;
            } else {
                debugResults.innerHTML += itemHtml;
            }
        }
        
        async function loadKDJTest() {
            const stockCode = document.getElementById('stock-code').value;
            const period = document.getElementById('period').value;
            
            updateDebugInfo('测试状态', '开始加载...', 'info');
            
            try {
                // 初始化分析器
                if (!stockAnalyzer) {
                    stockAnalyzer = new StockAnalyzer();
                    updateDebugInfo('StockAnalyzer', '初始化成功', 'success');
                }
                
                // 更新活跃指标
                updateActiveIndicators();
                
                // 测试API调用
                const indicatorsUrl = `/api/indicators/${stockCode}?period=${period}&mas=${stockAnalyzer.activeMAs.join(',')}&indicators=${stockAnalyzer.activeIndicators.join(',')}`;
                updateDebugInfo('API URL', indicatorsUrl);
                
                // 调用API
                const response = await fetch(indicatorsUrl);
                const data = await response.json();
                
                if (data.success) {
                    updateDebugInfo('API调用', '成功', 'success');
                    updateDebugInfo('返回指标', Object.keys(data.data.indicators).join(', '));
                    
                    // 检查KDJ数据
                    if (data.data.indicators.kdj) {
                        updateDebugInfo('KDJ数据', '存在', 'success');
                        const kdjData = data.data.indicators.kdj;
                        updateDebugInfo('KDJ长度', kdjData.k.length);
                        updateDebugInfo('KDJ最新值', `K:${kdjData.k[kdjData.k.length-1]}, D:${kdjData.d[kdjData.d.length-1]}, J:${kdjData.j[kdjData.j.length-1]}`);
                    } else {
                        updateDebugInfo('KDJ数据', '不存在', 'error');
                    }
                    
                    // 加载图表数据
                    await stockAnalyzer.updateChart(period, stockCode);
                    updateDebugInfo('图表更新', '成功', 'success');
                    
                    // 检查子图表
                    setTimeout(() => {
                        const subCharts = document.querySelectorAll('.sub-chart');
                        updateDebugInfo('子图表数量', subCharts.length);
                        
                        const kdjChart = document.getElementById('kdj-chart');
                        if (kdjChart) {
                            updateDebugInfo('KDJ图表', '已创建', 'success');
                            updateDebugInfo('KDJ高度', `${kdjChart.clientHeight}px`);
                        } else {
                            updateDebugInfo('KDJ图表', '未创建', 'error');
                        }
                    }, 1000);
                    
                } else {
                    updateDebugInfo('API调用', `失败: ${data.error}`, 'error');
                }
                
            } catch (error) {
                updateDebugInfo('测试状态', `错误: ${error.message}`, 'error');
                console.error('KDJ测试失败:', error);
            }
        }
        
        // 页面加载完成后自动运行测试
        window.addEventListener('load', function() {
            setTimeout(() => {
                loadKDJTest();
            }, 1000);
        });
    </script>
</body>
</html>
