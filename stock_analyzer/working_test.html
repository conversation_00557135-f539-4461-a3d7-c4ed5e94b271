<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>完全修复版 - 股票分析器</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .test-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 10px;
            font-size: 11px;
            color: var(--text-secondary);
            z-index: 10000;
            max-width: 200px;
        }
        .test-info h4 {
            margin: 0 0 8px 0;
            color: var(--primary-color);
            font-size: 12px;
        }
        .test-info .status {
            display: flex;
            justify-content: space-between;
            margin: 3px 0;
        }
        .status-ok { color: var(--color-up); }
        .status-error { color: var(--color-down); }
        .close-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 3px 8px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 10px;
            margin-top: 8px;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 顶部导航 -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-chart-line"></i>
                    <span>股票分析器 (完全修复版)</span>
                </div>
                <nav class="nav-menu">
                    <a href="#" class="nav-item active">实时行情</a>
                    <a href="#" class="nav-item">技术分析</a>
                    <a href="#" class="nav-item">资讯中心</a>
                </nav>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 市场概览 -->
            <section class="market-overview" id="market-overview">
                <h3>市场概览</h3>
                <div class="market-indices">
                    <div class="index-item">
                        <div class="index-name">上证指数</div>
                        <div class="index-value" id="sh-index">3,245.67</div>
                        <div class="index-change positive" id="sh-change">+1.23%</div>
                    </div>
                    <div class="index-item">
                        <div class="index-name">深证成指</div>
                        <div class="index-value" id="sz-index">12,456.78</div>
                        <div class="index-change negative" id="sz-change">-0.45%</div>
                    </div>
                    <div class="index-item">
                        <div class="index-name">创业板指</div>
                        <div class="index-value" id="cy-index">2,789.34</div>
                        <div class="index-change positive" id="cy-change">+0.89%</div>
                    </div>
                </div>
            </section>

            <!-- 搜索区域 -->
            <section class="search-section">
                <div class="search-container">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="stock-search" placeholder="输入股票代码或名称，如：000001 或 平安银行">
                        <button class="search-btn" id="search-btn">搜索</button>
                    </div>
                    <div class="quick-search">
                        <span class="quick-label">热门股票：</span>
                        <button class="quick-btn" data-code="000001">平安银行</button>
                        <button class="quick-btn" data-code="000002">万科A</button>
                        <button class="quick-btn" data-code="600036">招商银行</button>
                        <button class="quick-btn" data-code="600519">贵州茅台</button>
                        <button class="quick-btn" data-code="000858">五粮液</button>
                    </div>
                </div>
            </section>

            <!-- 股票信息面板 -->
            <section class="stock-panel" id="stock-panel" style="display: none;">
                <div id="stock-info" style="display: none;"></div>
                
                <div class="stock-header">
                    <div class="stock-basic-info">
                        <h2 class="stock-name" id="stock-name">股票名称</h2>
                        <span class="stock-code" id="stock-code">000000</span>
                        <span class="stock-market" id="stock-market">深A</span>
                    </div>
                    <div class="stock-price-info">
                        <div class="current-price" id="current-price">0.00</div>
                        <div class="price-change">
                            <span class="change-amount" id="change-amount">+0.00</span>
                            <span class="change-percent" id="change-percent">+0.00%</span>
                        </div>
                    </div>
                </div>

                <div class="stock-stats">
                    <div class="stat-item">
                        <span class="stat-label">开盘</span>
                        <span class="stat-value" id="open-price">0.00</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">昨收</span>
                        <span class="stat-value" id="prev-close">0.00</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">最高</span>
                        <span class="stat-value" id="high-price">0.00</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">最低</span>
                        <span class="stat-value" id="low-price">0.00</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">成交量</span>
                        <span class="stat-value" id="volume">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">成交额</span>
                        <span class="stat-value" id="turnover">0</span>
                    </div>
                </div>

                <!-- 图表和分析区域 -->
                <div class="analysis-container">
                    <div class="chart-section">
                        <div class="chart-header">
                            <h3>价格走势</h3>
                            <div class="chart-controls">
                                <div class="chart-type-controls">
                                    <button class="chart-type-btn active" data-type="kline">K线</button>
                                    <button class="chart-type-btn" data-type="timeline">分时</button>
                                </div>
                                <div class="chart-period-controls">
                                    <button class="chart-btn active" data-period="1d">日K</button>
                                    <button class="chart-btn" data-period="1w">周K</button>
                                    <button class="chart-btn" data-period="1m">月K</button>
                                    <button class="chart-btn" data-period="5m">5分</button>
                                    <button class="chart-btn" data-period="15m">15分</button>
                                    <button class="chart-btn" data-period="60m">60分</button>
                                </div>
                            </div>
                        </div>
                        <div class="chart-container">
                            <div id="price-chart"></div>
                        </div>
                        <div class="indicator-controls">
                            <h4>技术指标</h4>
                            <div class="indicator-buttons">
                                <button class="indicator-btn active" data-indicator="ma">均线</button>
                                <button class="indicator-btn" data-indicator="boll">布林带</button>
                                <button class="indicator-btn" data-indicator="macd">MACD</button>
                                <button class="indicator-btn" data-indicator="kdj">KDJ</button>
                                <button class="indicator-btn" data-indicator="rsi">RSI</button>
                                <button class="indicator-btn" data-indicator="volume">成交量</button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 热门股票列表 -->
            <section class="hot-stocks-section">
                <div class="section-header">
                    <h3>热门股票 (真实数据)</h3>
                    <div class="section-tabs">
                        <button class="tab-btn active" data-tab="gainers">涨幅榜</button>
                        <button class="tab-btn" data-tab="losers">跌幅榜</button>
                        <button class="tab-btn" data-tab="volume">成交量榜</button>
                    </div>
                </div>
                <div class="stocks-list" id="hot-stocks-list">
                    <!-- 股票列表将在这里动态生成 -->
                </div>
            </section>
        </main>
    </div>

    <!-- 加载指示器 -->
    <div class="loading-overlay" id="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <!-- 测试信息面板 -->
    <div class="test-info" id="test-info">
        <h4>🔧 系统状态</h4>
        <div id="status-content">检查中...</div>
        <button class="close-btn" onclick="document.getElementById('test-info').style.display='none'">关闭</button>
    </div>

    <!-- 脚本文件 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://unpkg.com/lightweight-charts@4.1.3/dist/lightweight-charts.standalone.production.js"></script>
    <script src="script.js"></script>
    
    <script>
        // 检查系统状态
        function checkSystemStatus() {
            const elements = [
                'loading-overlay',
                'price-chart',
                'hot-stocks-list',
                'sh-index',
                'sz-index',
                'cy-index',
                'stock-info',
                'market-overview',
                'search-btn',
                'stock-search'
            ];
            
            let found = 0;
            let statusHtml = '';
            
            elements.forEach(id => {
                const exists = document.getElementById(id) !== null;
                if (exists) found++;
                statusHtml += `<div class="status"><span>${id}</span><span class="${exists ? 'status-ok' : 'status-error'}">${exists ? '✅' : '❌'}</span></div>`;
            });
            
            const buttons = {
                'chart-type-btn': document.querySelectorAll('.chart-type-btn').length,
                'chart-btn': document.querySelectorAll('.chart-btn').length,
                'indicator-btn': document.querySelectorAll('.indicator-btn').length,
                'tab-btn': document.querySelectorAll('.tab-btn').length,
                'quick-btn': document.querySelectorAll('.quick-btn').length
            };
            
            Object.entries(buttons).forEach(([type, count]) => {
                statusHtml += `<div class="status"><span>${type}</span><span class="status-ok">${count}</span></div>`;
            });
            
            document.getElementById('status-content').innerHTML = statusHtml;
            
            // 10秒后隐藏状态面板
            setTimeout(() => {
                const statusPanel = document.getElementById('test-info');
                if (statusPanel) {
                    statusPanel.style.display = 'none';
                }
            }, 10000);
            
            return found === elements.length;
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('完全修复版页面加载完成');
            
            // 检查系统状态
            setTimeout(() => {
                const allGood = checkSystemStatus();
                console.log('系统状态检查完成:', allGood ? '正常' : '有问题');
            }, 500);
            
            // 初始化分析器
            setTimeout(() => {
                console.log('开始初始化股票分析器');
                try {
                    window.stockAnalyzer = new StockAnalyzer();
                    console.log('股票分析器初始化成功');
                } catch (error) {
                    console.error('股票分析器初始化失败:', error);
                }
            }, 1000);
        });
        
        // 测试搜索功能
        function testSearch(code) {
            const searchInput = document.getElementById('stock-search');
            if (searchInput) {
                searchInput.value = code;
                if (window.stockAnalyzer) {
                    window.stockAnalyzer.searchStock();
                }
            }
        }
        
        // 添加全局测试函数
        window.testFunctions = {
            search: testSearch,
            checkStatus: checkSystemStatus
        };
    </script>
</body>
</html>
