<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 综合功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
            margin: 0;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
        }
        .test-section {
            background: #333;
            margin: 15px 0;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #ffc107;
        }
        .test-section h3 {
            margin: 0 0 10px 0;
            color: #ffc107;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .test-item {
            background: #444;
            padding: 12px;
            border-radius: 4px;
            border: 1px solid #555;
        }
        .test-item h4 {
            margin: 0 0 8px 0;
            color: #2196f3;
            font-size: 14px;
        }
        .test-result {
            padding: 8px;
            margin: 5px 0;
            border-radius: 3px;
            font-size: 12px;
            font-family: monospace;
        }
        .success { background: #1b5e20; color: #4caf50; }
        .error { background: #b71c1c; color: #f44336; }
        .warning { background: #e65100; color: #ff9800; }
        .info { background: #0d47a1; color: #2196f3; }
        .test-button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 12px;
        }
        .test-button:hover {
            background: #1976d2;
        }
        .test-button.danger {
            background: #f44336;
        }
        .test-button.success {
            background: #4caf50;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-online { background: #4caf50; }
        .status-offline { background: #f44336; }
        .status-warning { background: #ff9800; }
        .log-container {
            background: #000;
            color: #0f0;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 11px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 股票分析系统 - 综合功能测试</h1>
        
        <div class="test-section">
            <h3>📡 API连接测试</h3>
            <div class="test-grid">
                <div class="test-item">
                    <h4>后端API服务器</h4>
                    <div id="api-status">
                        <span class="status-indicator status-offline"></span>
                        检测中...
                    </div>
                    <button class="test-button" onclick="testAPI()">测试API</button>
                </div>
                <div class="test-item">
                    <h4>股票数据获取</h4>
                    <div id="stock-data-status">
                        <span class="status-indicator status-offline"></span>
                        未测试
                    </div>
                    <button class="test-button" onclick="testStockData()">测试股票数据</button>
                </div>
                <div class="test-item">
                    <h4>K线数据获取</h4>
                    <div id="kline-data-status">
                        <span class="status-indicator status-offline"></span>
                        未测试
                    </div>
                    <button class="test-button" onclick="testKlineData()">测试K线数据</button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🎮 键盘功能测试</h3>
            <div class="test-grid">
                <div class="test-item">
                    <h4>左右键导航</h4>
                    <div id="arrow-key-status">
                        <span class="status-indicator status-warning"></span>
                        等待测试
                    </div>
                    <div style="font-size: 11px; color: #888; margin-top: 5px;">
                        请按左右键测试
                    </div>
                </div>
                <div class="test-item">
                    <h4>数字键周期切换</h4>
                    <div id="number-key-status">
                        <span class="status-indicator status-warning"></span>
                        等待测试
                    </div>
                    <div style="font-size: 11px; color: #888; margin-top: 5px;">
                        请按1-0数字键测试
                    </div>
                </div>
                <div class="test-item">
                    <h4>Shift组合键</h4>
                    <div id="shift-key-status">
                        <span class="status-indicator status-warning"></span>
                        等待测试
                    </div>
                    <div style="font-size: 11px; color: #888; margin-top: 5px;">
                        请按Shift+Q/E测试
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 图表功能测试</h3>
            <div class="test-grid">
                <div class="test-item">
                    <h4>K线图渲染</h4>
                    <div id="chart-render-status">
                        <span class="status-indicator status-offline"></span>
                        未测试
                    </div>
                    <button class="test-button" onclick="testChartRender()">测试图表渲染</button>
                </div>
                <div class="test-item">
                    <h4>十字光标系统</h4>
                    <div id="crosshair-status">
                        <span class="status-indicator status-offline"></span>
                        未测试
                    </div>
                    <button class="test-button" onclick="testCrosshair()">测试十字光标</button>
                </div>
                <div class="test-item">
                    <h4>技术指标</h4>
                    <div id="indicator-status">
                        <span class="status-indicator status-offline"></span>
                        未测试
                    </div>
                    <button class="test-button" onclick="testIndicators()">测试技术指标</button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>📋 测试日志</h3>
            <div class="log-container" id="testLog">
                === 测试日志 ===<br>
                等待开始测试...<br>
            </div>
            <button class="test-button success" onclick="runAllTests()">🚀 运行全部测试</button>
            <button class="test-button" onclick="clearLog()">清除日志</button>
            <button class="test-button danger" onclick="openMainApp()">打开主应用</button>
        </div>
    </div>

    <script>
        let testCount = 0;

        function addLog(message, type = 'info') {
            testCount++;
            const timestamp = new Date().toLocaleTimeString();
            const log = document.getElementById('testLog');
            const typeIcon = {
                'success': '✅',
                'error': '❌', 
                'warning': '⚠️',
                'info': 'ℹ️'
            };
            log.innerHTML += `[${timestamp}] ${typeIcon[type] || 'ℹ️'} ${testCount}: ${message}<br>`;
            log.scrollTop = log.scrollHeight;
        }

        function clearLog() {
            document.getElementById('testLog').innerHTML = '=== 测试日志 ===<br>';
            testCount = 0;
        }

        function updateStatus(elementId, status, message) {
            const element = document.getElementById(elementId);
            if (element) {
                const indicator = element.querySelector('.status-indicator');
                const statusClasses = {
                    'success': 'status-online',
                    'error': 'status-offline', 
                    'warning': 'status-warning'
                };
                
                indicator.className = `status-indicator ${statusClasses[status] || 'status-offline'}`;
                element.innerHTML = `<span class="status-indicator ${statusClasses[status] || 'status-offline'}"></span>${message}`;
            }
        }

        // API测试
        async function testAPI() {
            addLog('开始测试API连接...');
            try {
                const response = await fetch('http://localhost:5001/api/health');
                if (response.ok) {
                    updateStatus('api-status', 'success', 'API服务器在线');
                    addLog('API服务器连接成功', 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                updateStatus('api-status', 'error', 'API服务器离线');
                addLog(`API连接失败: ${error.message}`, 'error');
            }
        }

        // 股票数据测试
        async function testStockData() {
            addLog('开始测试股票数据获取...');
            try {
                const response = await fetch('http://localhost:5001/api/stock/000001');
                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.data) {
                        updateStatus('stock-data-status', 'success', '股票数据正常');
                        addLog(`股票数据获取成功: ${data.data.name}`, 'success');
                    } else {
                        throw new Error(data.error || '数据格式错误');
                    }
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                updateStatus('stock-data-status', 'error', '股票数据异常');
                addLog(`股票数据获取失败: ${error.message}`, 'error');
            }
        }

        // K线数据测试
        async function testKlineData() {
            addLog('开始测试K线数据获取...');
            try {
                const response = await fetch('http://localhost:5001/api/kline/000001/1d');
                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.data && data.data.klines) {
                        updateStatus('kline-data-status', 'success', `K线数据正常 (${data.data.klines.length}条)`);
                        addLog(`K线数据获取成功: ${data.data.klines.length}条记录`, 'success');
                    } else {
                        throw new Error(data.error || 'K线数据格式错误');
                    }
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                updateStatus('kline-data-status', 'error', 'K线数据异常');
                addLog(`K线数据获取失败: ${error.message}`, 'error');
            }
        }

        // 图表渲染测试
        function testChartRender() {
            addLog('开始测试图表渲染...');
            // 这里可以添加图表渲染测试逻辑
            updateStatus('chart-render-status', 'warning', '需要在主应用中测试');
            addLog('图表渲染测试需要在主应用中进行', 'warning');
        }

        // 十字光标测试
        function testCrosshair() {
            addLog('开始测试十字光标系统...');
            updateStatus('crosshair-status', 'warning', '需要在主应用中测试');
            addLog('十字光标测试需要在主应用中进行', 'warning');
        }

        // 技术指标测试
        function testIndicators() {
            addLog('开始测试技术指标...');
            updateStatus('indicator-status', 'warning', '需要在主应用中测试');
            addLog('技术指标测试需要在主应用中进行', 'warning');
        }

        // 运行全部测试
        async function runAllTests() {
            addLog('🚀 开始运行全部测试...', 'info');
            
            await testAPI();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testStockData();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testKlineData();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testChartRender();
            testCrosshair();
            testIndicators();
            
            addLog('🎉 全部测试完成！', 'success');
        }

        // 打开主应用
        function openMainApp() {
            window.open('http://localhost:5001/professional.html', '_blank');
            addLog('已打开主应用页面', 'info');
        }

        // 键盘事件监听
        document.addEventListener('keydown', (e) => {
            const key = e.key;
            
            // 左右键测试
            if (key === 'ArrowLeft' || key === 'ArrowRight') {
                updateStatus('arrow-key-status', 'success', `${key === 'ArrowLeft' ? '左' : '右'}键响应正常`);
                addLog(`${key === 'ArrowLeft' ? '左' : '右'}键测试通过`, 'success');
                e.preventDefault();
            }
            
            // 数字键测试
            if (/^[0-9]$/.test(key)) {
                updateStatus('number-key-status', 'success', `数字键${key}响应正常`);
                addLog(`数字键${key}测试通过`, 'success');
            }
            
            // Shift组合键测试
            if (e.shiftKey && (key === 'Q' || key === 'E')) {
                updateStatus('shift-key-status', 'success', `Shift+${key}响应正常`);
                addLog(`Shift+${key}组合键测试通过`, 'success');
                e.preventDefault();
            }
        });

        // 页面加载完成后自动测试API
        document.addEventListener('DOMContentLoaded', () => {
            addLog('页面加载完成，开始自动测试...', 'info');
            setTimeout(testAPI, 1000);
        });
    </script>
</body>
</html>
