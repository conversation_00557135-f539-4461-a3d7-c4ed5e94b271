<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 MACD修复验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
            margin: 0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
        }
        .test-result {
            background: #333;
            padding: 15px;
            margin: 10px 0;
            border-radius: 6px;
            border-left: 4px solid #2196f3;
        }
        .success { border-left-color: #4caf50; }
        .error { border-left-color: #f44336; }
        .warning { border-left-color: #ff9800; }
        .macd-data {
            background: #000;
            color: #0f0;
            padding: 10px;
            font-family: monospace;
            font-size: 11px;
            border-radius: 4px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
        }
        .btn {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #1976d2; }
        .btn.success { background: #4caf50; }
        .btn.danger { background: #f44336; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 MACD修复验证工具</h1>
        
        <div class="test-result">
            <h3>📊 修复前后对比测试</h3>
            <button class="btn success" onclick="testMACDFix()">🚀 测试MACD修复</button>
            <button class="btn" onclick="testSpecificIndex()">🎯 测试特定索引</button>
            <button class="btn danger" onclick="clearResults()">清除结果</button>
        </div>

        <div class="test-result">
            <h3>📈 MACD数据详情</h3>
            <div id="macd-details" class="macd-data">
                等待测试...
            </div>
        </div>

        <div class="test-result">
            <h3>🔍 问题诊断</h3>
            <div id="diagnosis-results">
                <!-- 诊断结果将在这里显示 -->
            </div>
        </div>

        <div class="test-result">
            <h3>✅ 修复验证</h3>
            <div id="fix-validation">
                <!-- 修复验证结果将在这里显示 -->
            </div>
        </div>
    </div>

    <script>
        function addLog(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const timestamp = new Date().toLocaleTimeString();
            const typeIcon = {
                'success': '✅',
                'error': '❌', 
                'warning': '⚠️',
                'info': 'ℹ️'
            };
            container.innerHTML += `[${timestamp}] ${typeIcon[type]} ${message}<br>`;
            container.scrollTop = container.scrollHeight;
        }

        function clearResults() {
            ['macd-details', 'diagnosis-results', 'fix-validation'].forEach(id => {
                document.getElementById(id).innerHTML = '已清除...<br>';
            });
        }

        async function testMACDFix() {
            addLog('macd-details', '🔧 开始测试MACD修复...', 'info');
            
            try {
                // 获取MACD数据
                const response = await fetch('http://localhost:5001/api/indicators/000001?period=1d&indicators=macd');
                if (!response.ok) throw new Error(`HTTP ${response.status}`);
                
                const result = await response.json();
                if (!result.success) throw new Error(result.error);
                
                const macdData = result.data.indicators.macd;
                addLog('macd-details', `MACD数据获取成功`, 'success');
                
                // 分析数据质量
                analyzeMACDQuality(macdData);
                
                // 测试特定问题索引
                testProblematicIndices(macdData);
                
                // 验证修复效果
                validateFix(macdData);
                
            } catch (error) {
                addLog('macd-details', `测试失败: ${error.message}`, 'error');
            }
        }

        function analyzeMACDQuality(macdData) {
            addLog('diagnosis-results', '📊 分析MACD数据质量...', 'info');
            
            const totalLength = macdData.dif.length;
            const validDif = macdData.dif.filter(v => v !== null && !isNaN(v)).length;
            const validDea = macdData.dea.filter(v => v !== null && !isNaN(v)).length;
            const validMacd = macdData.macd.filter(v => v !== null && !isNaN(v)).length;
            
            addLog('diagnosis-results', `总数据长度: ${totalLength}`, 'info');
            addLog('diagnosis-results', `DIF有效数据: ${validDif}/${totalLength} (${(validDif/totalLength*100).toFixed(1)}%)`, 'info');
            addLog('diagnosis-results', `DEA有效数据: ${validDea}/${totalLength} (${(validDea/totalLength*100).toFixed(1)}%)`, 'info');
            addLog('diagnosis-results', `MACD有效数据: ${validMacd}/${totalLength} (${(validMacd/totalLength*100).toFixed(1)}%)`, 'info');
            
            // 检查数据完整性
            if (validDea >= totalLength - 35) {
                addLog('diagnosis-results', '✅ DEA数据完整性良好', 'success');
            } else if (validDea >= totalLength - 50) {
                addLog('diagnosis-results', '⚠️ DEA数据完整性一般', 'warning');
            } else {
                addLog('diagnosis-results', '❌ DEA数据完整性差', 'error');
            }
        }

        function testProblematicIndices(macdData) {
            addLog('diagnosis-results', '🎯 测试问题索引...', 'info');
            
            // 测试图片中显示的问题索引
            const testIndices = [19, 28, 35, 40];
            
            testIndices.forEach(index => {
                if (index < macdData.dif.length) {
                    const dif = macdData.dif[index];
                    const dea = macdData.dea[index];
                    const macd = macdData.macd[index];
                    
                    const difStr = dif !== null ? dif.toFixed(4) : 'null';
                    const deaStr = dea !== null ? dea.toFixed(4) : 'null';
                    const macdStr = macd !== null ? macd.toFixed(4) : 'null';
                    
                    addLog('diagnosis-results', `索引${index}: DIF=${difStr}, DEA=${deaStr}, MACD=${macdStr}`, 'info');
                    
                    // 检查是否修复了问题
                    if (index >= 35 && dea !== null && macd !== null) {
                        addLog('diagnosis-results', `✅ 索引${index}已修复`, 'success');
                    } else if (index < 35) {
                        addLog('diagnosis-results', `ℹ️ 索引${index}数据不足（正常）`, 'info');
                    } else {
                        addLog('diagnosis-results', `⚠️ 索引${index}仍有问题`, 'warning');
                    }
                }
            });
        }

        function validateFix(macdData) {
            addLog('fix-validation', '✅ 验证修复效果...', 'info');
            
            let fixedCount = 0;
            let totalTestable = 0;
            
            // 从索引35开始检查（理论上应该有完整数据）
            for (let i = 35; i < macdData.dif.length; i++) {
                totalTestable++;
                
                const dif = macdData.dif[i];
                const dea = macdData.dea[i];
                const macd = macdData.macd[i];
                
                if (dif !== null && dea !== null && macd !== null) {
                    // 验证MACD公式
                    const expectedMacd = (dif - dea) * 2;
                    const diff = Math.abs(macd - expectedMacd);
                    
                    if (diff < 0.0001) {
                        fixedCount++;
                    }
                }
            }
            
            const fixRate = totalTestable > 0 ? (fixedCount / totalTestable * 100) : 0;
            
            addLog('fix-validation', `修复率: ${fixedCount}/${totalTestable} (${fixRate.toFixed(1)}%)`, 'info');
            
            if (fixRate >= 95) {
                addLog('fix-validation', '🎉 MACD修复成功！数据质量优秀', 'success');
            } else if (fixRate >= 80) {
                addLog('fix-validation', '✅ MACD修复良好，少量数据仍需优化', 'success');
            } else if (fixRate >= 60) {
                addLog('fix-validation', '⚠️ MACD部分修复，仍有问题需要解决', 'warning');
            } else {
                addLog('fix-validation', '❌ MACD修复失败，需要进一步调试', 'error');
            }
            
            // 显示最新几个数据点
            addLog('fix-validation', '最新5个数据点:', 'info');
            const startIdx = Math.max(0, macdData.dif.length - 5);
            for (let i = startIdx; i < macdData.dif.length; i++) {
                const dif = macdData.dif[i];
                const dea = macdData.dea[i];
                const macd = macdData.macd[i];
                
                const difStr = dif !== null ? dif.toFixed(4) : '--';
                const deaStr = dea !== null ? dea.toFixed(4) : '--';
                const macdStr = macd !== null ? macd.toFixed(4) : '--';
                
                addLog('fix-validation', `[${i}] DIF:${difStr} DEA:${deaStr} MACD:${macdStr}`, 'info');
            }
        }

        async function testSpecificIndex() {
            const index = prompt('请输入要测试的索引位置:', '28');
            if (index === null) return;
            
            const indexNum = parseInt(index);
            if (isNaN(indexNum)) {
                alert('请输入有效的数字');
                return;
            }
            
            addLog('macd-details', `🎯 测试索引 ${indexNum}...`, 'info');
            
            try {
                const response = await fetch('http://localhost:5001/api/indicators/000001?period=1d&indicators=macd');
                const result = await response.json();
                const macdData = result.data.indicators.macd;
                
                if (indexNum >= macdData.dif.length) {
                    addLog('macd-details', `❌ 索引${indexNum}超出数据范围 (最大${macdData.dif.length-1})`, 'error');
                    return;
                }
                
                const dif = macdData.dif[indexNum];
                const dea = macdData.dea[indexNum];
                const macd = macdData.macd[indexNum];
                
                addLog('macd-details', `索引${indexNum}数据:`, 'info');
                addLog('macd-details', `  DIF: ${dif !== null ? dif.toFixed(4) : 'null'}`, 'info');
                addLog('macd-details', `  DEA: ${dea !== null ? dea.toFixed(4) : 'null'}`, 'info');
                addLog('macd-details', `  MACD: ${macd !== null ? macd.toFixed(4) : 'null'}`, 'info');
                
                if (dif !== null && dea !== null && macd !== null) {
                    const expected = (dif - dea) * 2;
                    const diff = Math.abs(macd - expected);
                    addLog('macd-details', `  公式验证: 期望${expected.toFixed(4)}, 差异${diff.toFixed(6)}`, diff < 0.0001 ? 'success' : 'warning');
                }
                
            } catch (error) {
                addLog('macd-details', `测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后自动运行测试
        document.addEventListener('DOMContentLoaded', () => {
            addLog('macd-details', '🔧 MACD修复验证工具已加载', 'info');
            addLog('macd-details', '点击"测试MACD修复"开始验证...', 'info');
        });
    </script>
</body>
</html>
