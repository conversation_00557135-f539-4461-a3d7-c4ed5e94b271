<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>按钮测试</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .test-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 30px;
            background: var(--bg-card);
            border-radius: 12px;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
        }
        .test-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 15px 0;
        }
        .log-area {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            color: var(--text-secondary);
        }
        .clear-btn {
            background: var(--danger-color);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center; color: var(--primary-color);">🔘 按钮事件测试</h1>
        
        <div class="test-section">
            <h3>图表类型按钮</h3>
            <div class="test-buttons">
                <button class="chart-type-btn active" data-type="kline">K线</button>
                <button class="chart-type-btn" data-type="timeline">分时</button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>时间周期按钮</h3>
            <div class="test-buttons">
                <button class="chart-btn active" data-period="1d">日K</button>
                <button class="chart-btn" data-period="1w">周K</button>
                <button class="chart-btn" data-period="1m">月K</button>
                <button class="chart-btn" data-period="5m">5分</button>
                <button class="chart-btn" data-period="15m">15分</button>
                <button class="chart-btn" data-period="60m">60分</button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>技术指标按钮</h3>
            <div class="test-buttons">
                <button class="indicator-btn active" data-indicator="ma">均线</button>
                <button class="indicator-btn" data-indicator="boll">布林带</button>
                <button class="indicator-btn" data-indicator="macd">MACD</button>
                <button class="indicator-btn" data-indicator="kdj">KDJ</button>
                <button class="indicator-btn" data-indicator="rsi">RSI</button>
                <button class="indicator-btn" data-indicator="volume">成交量</button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>热门股票标签</h3>
            <div class="test-buttons">
                <button class="tab-btn active" data-tab="gainers">涨幅榜</button>
                <button class="tab-btn" data-tab="losers">跌幅榜</button>
                <button class="tab-btn" data-tab="volume">成交量榜</button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>搜索测试</h3>
            <div style="display: flex; gap: 10px; margin: 15px 0;">
                <input type="text" id="stock-search" placeholder="输入股票代码" style="flex: 1; padding: 10px; background: var(--bg-tertiary); border: 1px solid var(--border-color); border-radius: 6px; color: var(--text-primary);">
                <button id="search-btn" class="search-btn" style="padding: 10px 20px; background: var(--primary-color); color: white; border: none; border-radius: 6px; cursor: pointer;">搜索</button>
            </div>
            <div class="test-buttons">
                <button class="quick-btn" data-code="000001">平安银行</button>
                <button class="quick-btn" data-code="600519">贵州茅台</button>
                <button class="quick-btn" data-code="000858">五粮液</button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>事件日志</h3>
            <button class="clear-btn" onclick="clearLog()">清空日志</button>
            <div id="log-area" class="log-area"></div>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="clean_version.html" style="color: var(--primary-color); text-decoration: none;">← 返回主应用</a>
        </div>
    </div>

    <script>
        // 日志功能
        function addLog(message) {
            const logArea = document.getElementById('log-area');
            const timestamp = new Date().toLocaleTimeString();
            logArea.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('log-area').innerHTML = '';
        }
        
        // 简化的事件处理器
        class ButtonTester {
            constructor() {
                this.currentChartType = 'kline';
                this.currentPeriod = '1d';
                this.activeIndicators = ['ma'];
                this.currentStock = null;
                
                this.bindEvents();
                addLog('按钮测试器初始化完成');
            }
            
            bindEvents() {
                // 使用事件委托
                document.addEventListener('click', (e) => {
                    // 图表类型按钮
                    if (e.target.classList.contains('chart-type-btn')) {
                        addLog(`图表类型切换: ${e.target.dataset.type}`);
                        document.querySelectorAll('.chart-type-btn').forEach(b => b.classList.remove('active'));
                        e.target.classList.add('active');
                        this.currentChartType = e.target.dataset.type;
                        return;
                    }
                    
                    // 周期按钮
                    if (e.target.classList.contains('chart-btn')) {
                        addLog(`周期切换: ${e.target.dataset.period}`);
                        document.querySelectorAll('.chart-btn').forEach(b => b.classList.remove('active'));
                        e.target.classList.add('active');
                        this.currentPeriod = e.target.dataset.period;
                        return;
                    }
                    
                    // 技术指标按钮
                    if (e.target.classList.contains('indicator-btn')) {
                        const indicator = e.target.dataset.indicator;
                        const wasActive = e.target.classList.contains('active');
                        
                        e.target.classList.toggle('active');
                        
                        if (wasActive) {
                            this.activeIndicators = this.activeIndicators.filter(i => i !== indicator);
                            addLog(`指标关闭: ${indicator}`);
                        } else {
                            if (!this.activeIndicators.includes(indicator)) {
                                this.activeIndicators.push(indicator);
                            }
                            addLog(`指标开启: ${indicator}`);
                        }
                        
                        addLog(`当前活跃指标: ${this.activeIndicators.join(', ')}`);
                        return;
                    }
                    
                    // 热门股票标签按钮
                    if (e.target.classList.contains('tab-btn')) {
                        addLog(`标签切换: ${e.target.dataset.tab}`);
                        document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
                        e.target.classList.add('active');
                        return;
                    }
                    
                    // 快速搜索按钮
                    if (e.target.classList.contains('quick-btn')) {
                        const code = e.target.dataset.code;
                        addLog(`快速搜索: ${code}`);
                        document.getElementById('stock-search').value = code;
                        this.searchStock();
                        return;
                    }
                    
                    // 搜索按钮
                    if (e.target.id === 'search-btn') {
                        this.searchStock();
                        return;
                    }
                });
                
                // 回车搜索
                document.getElementById('stock-search').addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.searchStock();
                    }
                });
                
                addLog('事件绑定完成');
            }
            
            searchStock() {
                const query = document.getElementById('stock-search').value.trim();
                if (!query) {
                    addLog('搜索失败: 请输入股票代码');
                    return;
                }
                
                addLog(`搜索股票: ${query}`);
                this.currentStock = query;
                
                // 模拟搜索过程
                setTimeout(() => {
                    addLog(`搜索完成: ${query} - 模拟数据加载成功`);
                }, 500);
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            addLog('页面加载完成');
            
            setTimeout(() => {
                new ButtonTester();
            }, 100);
        });
    </script>
</body>
</html>
