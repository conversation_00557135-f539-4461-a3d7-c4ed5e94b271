<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KDJ显示对比测试</title>
    <style>
        body {
            background: #0f1419;
            color: #d1d4dc;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
        }
        
        .comparison-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .chart-section {
            background: #21262d;
            border: 1px solid #30363d;
            border-radius: 8px;
            padding: 20px;
        }
        
        .chart-section h3 {
            margin-top: 0;
            text-align: center;
        }
        
        .old-method {
            border-left: 4px solid #ff7b72;
        }
        
        .new-method {
            border-left: 4px solid #7ee787;
        }
        
        .chart-area {
            height: 300px;
            background: #0f1419;
            border: 1px solid #30363d;
            border-radius: 6px;
            margin: 15px 0;
            position: relative;
        }
        
        .info-panel {
            background: #161b22;
            border: 1px solid #30363d;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 12px;
        }
        
        .highlight {
            color: #58a6ff;
            font-weight: bold;
        }
        
        .warning {
            color: #f0883e;
        }
        
        .success {
            color: #7ee787;
        }
        
        button {
            background: #238636;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px;
            font-size: 14px;
        }
        
        button:hover {
            background: #2ea043;
        }
        
        .controls {
            text-align: center;
            margin-bottom: 30px;
        }
        
        select {
            background: #21262d;
            border: 1px solid #30363d;
            border-radius: 6px;
            color: #d1d4dc;
            padding: 8px 12px;
            margin: 0 10px;
        }
    </style>
</head>
<body>
    <h1>📊 KDJ指标显示方式对比</h1>
    
    <div class="controls">
        <select id="stock-select">
            <option value="000001">平安银行 (000001)</option>
            <option value="000002">万科A (000002)</option>
            <option value="600036">招商银行 (600036)</option>
            <option value="600519">贵州茅台 (600519)</option>
            <option value="000858">五粮液 (000858)</option>
        </select>
        <button onclick="loadComparison()">加载对比数据</button>
        <button onclick="generateRandomData()">生成极值测试数据</button>
    </div>
    
    <div class="comparison-container">
        <div class="chart-section old-method">
            <h3>🔴 传统固定范围 (0-100)</h3>
            <div class="chart-area" id="old-chart">
                <canvas width="400" height="280" style="width: 100%; height: 100%;"></canvas>
            </div>
            <div class="info-panel">
                <div class="info-row">
                    <span>显示范围:</span>
                    <span class="highlight">0 - 100 (固定)</span>
                </div>
                <div class="info-row">
                    <span>实际数据范围:</span>
                    <span id="old-actual-range">-</span>
                </div>
                <div class="info-row">
                    <span>数据截断:</span>
                    <span id="old-truncated" class="warning">-</span>
                </div>
                <div class="info-row">
                    <span>可见参考线:</span>
                    <span>20, 50, 80</span>
                </div>
            </div>
        </div>
        
        <div class="chart-section new-method">
            <h3>🟢 动态范围 (优化后)</h3>
            <div class="chart-area" id="new-chart">
                <canvas width="400" height="280" style="width: 100%; height: 100%;"></canvas>
            </div>
            <div class="info-panel">
                <div class="info-row">
                    <span>显示范围:</span>
                    <span id="new-display-range" class="success">-</span>
                </div>
                <div class="info-row">
                    <span>实际数据范围:</span>
                    <span id="new-actual-range">-</span>
                </div>
                <div class="info-row">
                    <span>数据截断:</span>
                    <span class="success">无</span>
                </div>
                <div class="info-row">
                    <span>可见参考线:</span>
                    <span id="new-reference-lines">-</span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="info-panel">
        <h3>📈 对比分析</h3>
        <div id="comparison-analysis">点击"加载对比数据"开始分析...</div>
    </div>

    <script>
        let currentData = null;
        
        async function loadComparison() {
            const stockCode = document.getElementById('stock-select').value;
            
            try {
                const response = await fetch(`/api/stock/${stockCode}`);
                const data = await response.json();
                
                if (!data.klines || !data.indicators || !data.indicators.kdj) {
                    throw new Error('KDJ数据缺失');
                }
                
                currentData = {
                    klines: data.klines.slice(-30),
                    kdj: data.indicators.kdj
                };
                
                drawComparison();
            } catch (error) {
                alert('加载数据失败: ' + error.message);
            }
        }
        
        function generateRandomData() {
            // 生成包含极值的测试数据
            const klines = [];
            const kdj = { k: [], d: [], j: [] };
            
            for (let i = 0; i < 30; i++) {
                // 生成一些极值情况
                let k, d;
                if (i < 10) {
                    // 前10个数据：正常范围
                    k = 20 + Math.random() * 60;
                    d = 20 + Math.random() * 60;
                } else if (i < 20) {
                    // 中间10个数据：接近极值
                    k = Math.random() < 0.5 ? 5 + Math.random() * 10 : 85 + Math.random() * 10;
                    d = Math.random() < 0.5 ? 5 + Math.random() * 10 : 85 + Math.random() * 10;
                } else {
                    // 后10个数据：极值情况
                    k = Math.random() < 0.5 ? Math.random() * 5 : 95 + Math.random() * 5;
                    d = Math.random() < 0.5 ? Math.random() * 5 : 95 + Math.random() * 5;
                }
                
                const j = 3 * k - 2 * d; // J值经常超出0-100范围
                
                kdj.k.push(k);
                kdj.d.push(d);
                kdj.j.push(j);
                
                klines.push({
                    date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
                });
            }
            
            currentData = { klines, kdj };
            drawComparison();
        }
        
        function drawComparison() {
            if (!currentData) return;
            
            const { klines, kdj } = currentData;
            
            // 计算实际数据范围
            let allValues = [];
            ['k', 'd', 'j'].forEach(line => {
                if (kdj[line]) {
                    allValues = allValues.concat(kdj[line].filter(v => v !== null && v !== undefined));
                }
            });
            
            const actualMin = Math.min(...allValues);
            const actualMax = Math.max(...allValues);
            
            // 绘制传统固定范围图表
            drawKDJChart('old-chart', kdj, klines, 0, 100, true);
            
            // 绘制动态范围图表
            const buffer = (actualMax - actualMin) * 0.1;
            const dynamicMin = actualMin - buffer;
            const dynamicMax = actualMax + buffer;
            drawKDJChart('new-chart', kdj, klines, dynamicMin, dynamicMax, false);
            
            // 更新信息面板
            updateInfoPanels(actualMin, actualMax, dynamicMin, dynamicMax);
            
            // 更新对比分析
            updateComparisonAnalysis(actualMin, actualMax, allValues);
        }
        
        function drawKDJChart(containerId, kdj, klines, minValue, maxValue, isFixed) {
            const container = document.getElementById(containerId);
            const canvas = container.querySelector('canvas');
            const ctx = canvas.getContext('2d');
            
            // 清空画布
            ctx.fillStyle = '#0f1419';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            const padding = { top: 20, right: 40, bottom: 20, left: 40 };
            const chartWidth = canvas.width - padding.left - padding.right;
            const chartHeight = canvas.height - padding.top - padding.bottom;
            const range = maxValue - minValue;
            
            // 绘制网格
            ctx.strokeStyle = '#2b3139';
            ctx.lineWidth = 0.5;
            ctx.setLineDash([1, 1]);
            for (let i = 1; i < 5; i++) {
                const y = padding.top + (chartHeight / 5) * i;
                ctx.beginPath();
                ctx.moveTo(padding.left, y);
                ctx.lineTo(padding.left + chartWidth, y);
                ctx.stroke();
            }
            ctx.setLineDash([]);
            
            // 绘制参考线
            const referenceLines = [20, 50, 80];
            ctx.strokeStyle = '#666666';
            ctx.lineWidth = 1;
            ctx.setLineDash([3, 3]);
            
            referenceLines.forEach(refValue => {
                if (refValue >= minValue && refValue <= maxValue) {
                    const normalizedValue = (refValue - minValue) / range;
                    const y = padding.top + chartHeight - (normalizedValue * chartHeight);
                    
                    ctx.beginPath();
                    ctx.moveTo(padding.left, y);
                    ctx.lineTo(padding.left + chartWidth, y);
                    ctx.stroke();
                }
            });
            ctx.setLineDash([]);
            
            // 绘制KDJ线条
            const colors = { k: '#ff9800', d: '#2196f3', j: '#4caf50' };
            const pointSpacing = chartWidth / klines.length;
            
            Object.entries(kdj).forEach(([line, values]) => {
                if (values && colors[line]) {
                    ctx.strokeStyle = colors[line];
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    
                    let hasValidPoint = false;
                    values.forEach((value, index) => {
                        if (value !== null && value !== undefined) {
                            const x = padding.left + (index + 0.5) * pointSpacing;
                            
                            // 对于固定范围，截断超出范围的值
                            let displayValue = value;
                            if (isFixed) {
                                displayValue = Math.max(0, Math.min(100, value));
                            }
                            
                            const normalizedValue = (displayValue - minValue) / range;
                            const y = padding.top + chartHeight - (normalizedValue * chartHeight);
                            
                            if (!hasValidPoint) {
                                ctx.moveTo(x, y);
                                hasValidPoint = true;
                            } else {
                                ctx.lineTo(x, y);
                            }
                        }
                    });
                    
                    if (hasValidPoint) {
                        ctx.stroke();
                    }
                }
            });
            
            // 绘制数值标签
            ctx.fillStyle = '#d1d4dc';
            ctx.font = '10px Arial';
            ctx.textAlign = 'right';
            ctx.fillText(maxValue.toFixed(1), padding.left - 5, padding.top + 12);
            ctx.fillText(minValue.toFixed(1), padding.left - 5, padding.top + chartHeight - 2);
        }
        
        function updateInfoPanels(actualMin, actualMax, dynamicMin, dynamicMax) {
            // 更新传统方法信息
            document.getElementById('old-actual-range').textContent = `${actualMin.toFixed(1)} - ${actualMax.toFixed(1)}`;
            
            const truncated = actualMin < 0 || actualMax > 100;
            document.getElementById('old-truncated').textContent = truncated ? '是' : '否';
            document.getElementById('old-truncated').className = truncated ? 'warning' : 'success';
            
            // 更新新方法信息
            document.getElementById('new-display-range').textContent = `${dynamicMin.toFixed(1)} - ${dynamicMax.toFixed(1)}`;
            document.getElementById('new-actual-range').textContent = `${actualMin.toFixed(1)} - ${actualMax.toFixed(1)}`;
            
            // 计算可见参考线
            const visibleLines = [20, 50, 80].filter(line => line >= dynamicMin && line <= dynamicMax);
            document.getElementById('new-reference-lines').textContent = visibleLines.length > 0 ? visibleLines.join(', ') : '无';
        }
        
        function updateComparisonAnalysis(actualMin, actualMax, allValues) {
            const exceedsNormal = actualMin < 0 || actualMax > 100;
            const jValues = currentData.kdj.j.filter(v => v !== null && v !== undefined);
            const jExceeds = jValues.some(v => v < 0 || v > 100);
            
            let html = `
                <div class="info-row">
                    <span>数据是否超出0-100范围:</span>
                    <span class="${exceedsNormal ? 'warning' : 'success'}">${exceedsNormal ? '是' : '否'}</span>
                </div>
                <div class="info-row">
                    <span>J值是否超出范围:</span>
                    <span class="${jExceeds ? 'warning' : 'success'}">${jExceeds ? '是' : '否'}</span>
                </div>
                <div class="info-row">
                    <span>传统方法问题:</span>
                    <span class="${exceedsNormal ? 'warning' : 'success'}">${exceedsNormal ? '会截断超出范围的数据，丢失重要信息' : '数据在正常范围内'}</span>
                </div>
                <div class="info-row">
                    <span>优化方法优势:</span>
                    <span class="success">完整显示所有数据，保留极值信号</span>
                </div>
            `;
            
            if (jExceeds) {
                const jMin = Math.min(...jValues);
                const jMax = Math.max(...jValues);
                html += `
                    <div class="info-row">
                        <span>J值实际范围:</span>
                        <span class="highlight">${jMin.toFixed(1)} - ${jMax.toFixed(1)}</span>
                    </div>
                `;
            }
            
            document.getElementById('comparison-analysis').innerHTML = html;
        }
        
        // 页面加载后自动生成测试数据
        window.addEventListener('load', function() {
            setTimeout(generateRandomData, 500);
        });
    </script>
</body>
</html>
