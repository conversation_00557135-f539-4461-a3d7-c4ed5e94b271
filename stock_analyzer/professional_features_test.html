<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专业功能测试</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .feature-demo {
            background: var(--bg-card);
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        .chart-container {
            height: 500px;
            background: var(--bg-tertiary);
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid var(--border-color);
            position: relative;
        }
        .controls-demo {
            display: flex;
            gap: 15px;
            margin: 15px 0;
            flex-wrap: wrap;
        }
        .control-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        .control-group label {
            font-size: 12px;
            color: var(--text-secondary);
            font-weight: bold;
        }
        .btn-group {
            display: flex;
            gap: 5px;
        }
        .demo-btn {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            color: var(--text-secondary);
            border-radius: 6px;
            padding: 6px 12px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .demo-btn.active,
        .demo-btn:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .feature-item {
            background: var(--bg-tertiary);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }
        .feature-item h4 {
            color: var(--primary-color);
            margin-bottom: 10px;
        }
        .feature-item ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .feature-item li {
            padding: 3px 0;
            color: var(--text-secondary);
            font-size: 13px;
        }
        .feature-item li:before {
            content: "✅ ";
            color: var(--color-up);
        }
        .interaction-hint {
            background: rgba(33, 150, 243, 0.1);
            border: 1px solid var(--primary-color);
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            color: var(--primary-color);
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center; color: var(--primary-color);">🚀 专业股票分析功能演示</h1>
        
        <div class="feature-demo">
            <h3>📊 专业图表功能</h3>
            
            <div class="controls-demo">
                <div class="control-group">
                    <label>图表类型</label>
                    <div class="btn-group">
                        <button class="demo-btn chart-type-btn active" data-type="kline">K线</button>
                        <button class="demo-btn chart-type-btn" data-type="timeline">分时</button>
                    </div>
                </div>
                
                <div class="control-group">
                    <label>时间周期</label>
                    <div class="btn-group">
                        <button class="demo-btn chart-btn active" data-period="1d">日K</button>
                        <button class="demo-btn chart-btn" data-period="1w">周K</button>
                        <button class="demo-btn chart-btn" data-period="1m">月K</button>
                        <button class="demo-btn chart-btn" data-period="5m">5分</button>
                    </div>
                </div>
                
                <div class="control-group">
                    <label>技术指标</label>
                    <div class="btn-group">
                        <button class="demo-btn indicator-btn active" data-indicator="ma">均线</button>
                        <button class="demo-btn indicator-btn" data-indicator="boll">布林带</button>
                        <button class="demo-btn indicator-btn" data-indicator="macd">MACD</button>
                        <button class="demo-btn indicator-btn" data-indicator="kdj">KDJ</button>
                        <button class="demo-btn indicator-btn" data-indicator="rsi">RSI</button>
                        <button class="demo-btn indicator-btn" data-indicator="volume">成交量</button>
                    </div>
                </div>
                
                <div class="control-group">
                    <label>测试股票</label>
                    <div class="btn-group">
                        <button class="demo-btn" onclick="loadStock('000001')">平安银行</button>
                        <button class="demo-btn" onclick="loadStock('600519')">贵州茅台</button>
                        <button class="demo-btn" onclick="loadStock('002594')">比亚迪</button>
                    </div>
                </div>
            </div>
            
            <div class="interaction-hint">
                💡 <strong>交互提示</strong>：在图表上移动鼠标查看详细的K线信息（开高低收、成交量、换手率等）
            </div>
            
            <div id="chart-container" class="chart-container"></div>
        </div>

        <div class="feature-demo">
            <h3>🎯 已实现的专业功能</h3>
            <div class="feature-list">
                <div class="feature-item">
                    <h4>📈 图表控制</h4>
                    <ul>
                        <li>周期切换按钮响应</li>
                        <li>技术指标开关</li>
                        <li>K线/分时图切换</li>
                        <li>实时数据更新</li>
                    </ul>
                </div>
                
                <div class="feature-item">
                    <h4>🎨 均线图例</h4>
                    <ul>
                        <li>MA5 橙色 #ff9800</li>
                        <li>MA10 紫色 #9c27b0</li>
                        <li>MA20 蓝色 #2196f3</li>
                        <li>MA60 绿色 #4caf50</li>
                        <li>实时数值显示</li>
                    </ul>
                </div>
                
                <div class="feature-item">
                    <h4>📊 多面板布局</h4>
                    <ul>
                        <li>主图：K线+均线+布林带</li>
                        <li>MACD子图</li>
                        <li>KDJ子图</li>
                        <li>RSI子图</li>
                        <li>成交量子图</li>
                    </ul>
                </div>
                
                <div class="feature-item">
                    <h4>🖱️ 鼠标交互</h4>
                    <ul>
                        <li>十字光标跟随</li>
                        <li>K线详细信息</li>
                        <li>开高低收显示</li>
                        <li>成交量/成交额</li>
                        <li>涨跌幅计算</li>
                        <li>换手率显示</li>
                    </ul>
                </div>
                
                <div class="feature-item">
                    <h4>🎯 专业标准</h4>
                    <ul>
                        <li>中国股市配色</li>
                        <li>专业K线绘制</li>
                        <li>准确的技术指标</li>
                        <li>实时数据源</li>
                        <li>响应式设计</li>
                    </ul>
                </div>
                
                <div class="feature-item">
                    <h4>📡 数据源</h4>
                    <ul>
                        <li>东方财富K线数据</li>
                        <li>新浪财经实时数据</li>
                        <li>腾讯财经备用源</li>
                        <li>智能数据切换</li>
                        <li>交易时间检测</li>
                    </ul>
                </div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="index.html" style="color: var(--primary-color); text-decoration: none; margin-right: 20px;">← 返回主应用</a>
            <a href="chart_fixed_test.html" style="color: var(--primary-color); text-decoration: none;">查看图表测试 →</a>
        </div>
    </div>

    <script>
        // 简化的股票分析器
        class ProfessionalStockAnalyzer {
            constructor() {
                this.currentStock = null;
                this.currentPeriod = '1d';
                this.currentChartType = 'kline';
                this.activeIndicators = ['ma'];
                this.lastKlineData = null;
                this.lastIndicatorData = null;
                
                this.bindEvents();
                this.loadStock('000001'); // 默认加载平安银行
            }
            
            bindEvents() {
                // 图表类型切换
                document.querySelectorAll('.chart-type-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        document.querySelectorAll('.chart-type-btn').forEach(b => b.classList.remove('active'));
                        e.target.classList.add('active');
                        this.currentChartType = e.target.dataset.type;
                        if (this.currentStock) {
                            this.updateChart();
                        }
                    });
                });

                // 周期切换
                document.querySelectorAll('.chart-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        document.querySelectorAll('.chart-btn').forEach(b => b.classList.remove('active'));
                        e.target.classList.add('active');
                        this.currentPeriod = e.target.dataset.period;
                        if (this.currentStock) {
                            this.updateChart();
                        }
                    });
                });

                // 技术指标切换
                document.querySelectorAll('.indicator-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const indicator = e.target.dataset.indicator;
                        
                        if (e.target.classList.contains('active')) {
                            e.target.classList.remove('active');
                            this.activeIndicators = this.activeIndicators.filter(i => i !== indicator);
                        } else {
                            e.target.classList.add('active');
                            if (!this.activeIndicators.includes(indicator)) {
                                this.activeIndicators.push(indicator);
                            }
                        }
                        
                        if (this.currentStock) {
                            this.updateChart();
                        }
                    });
                });
            }
            
            async loadStock(stockCode) {
                this.currentStock = stockCode;
                await this.updateChart();
            }
            
            async updateChart() {
                if (!this.currentStock) return;
                
                try {
                    const [klineResponse, indicatorResponse] = await Promise.all([
                        fetch(`http://localhost:5001/api/kline/${this.currentStock}?period=${this.currentPeriod}&count=50`),
                        fetch(`http://localhost:5001/api/indicators/${this.currentStock}?period=${this.currentPeriod}`)
                    ]);

                    const klineResult = await klineResponse.json();
                    const indicatorResult = await indicatorResponse.json();

                    if (klineResult.success && indicatorResult.success) {
                        this.lastKlineData = klineResult.data;
                        this.lastIndicatorData = indicatorResult.data;
                        this.renderChart();
                    }
                } catch (error) {
                    console.error('更新图表失败:', error);
                }
            }
            
            renderChart() {
                // 这里会调用主应用中的图表渲染逻辑
                // 为了演示，我们创建一个简化版本
                const container = document.getElementById('chart-container');
                container.innerHTML = `
                    <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; color: var(--text-muted);">
                        <div style="font-size: 48px; margin-bottom: 20px;">📊</div>
                        <div style="font-size: 18px; margin-bottom: 10px;">专业图表功能演示</div>
                        <div style="font-size: 14px; text-align: center;">
                            当前股票: ${this.currentStock}<br>
                            周期: ${this.currentPeriod}<br>
                            图表类型: ${this.currentChartType}<br>
                            活跃指标: ${this.activeIndicators.join(', ')}<br><br>
                            <span style="color: var(--primary-color);">请在主应用中查看完整的专业图表功能</span>
                        </div>
                    </div>
                `;
            }
        }
        
        // 全局函数
        function loadStock(stockCode) {
            if (window.analyzer) {
                window.analyzer.loadStock(stockCode);
            }
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            window.analyzer = new ProfessionalStockAnalyzer();
        });
    </script>
</body>
</html>
