<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>技术指标显示测试</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            background: #0f1419;
            color: #d1d4dc;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-header h1 {
            color: #00d4aa;
            margin-bottom: 10px;
        }
        
        .test-controls {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .control-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .control-group label {
            font-size: 12px;
            color: #8b949e;
        }
        
        select, input, button {
            padding: 8px 12px;
            background: #21262d;
            border: 1px solid #30363d;
            border-radius: 6px;
            color: #d1d4dc;
            font-size: 14px;
        }
        
        button {
            background: #238636;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        button:hover {
            background: #2ea043;
        }
        
        .indicator-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .indicator-btn {
            padding: 6px 12px;
            background: #21262d;
            border: 1px solid #30363d;
            border-radius: 4px;
            color: #d1d4dc;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
        }
        
        .indicator-btn.active {
            background: #238636;
            border-color: #2ea043;
        }
        
        .indicator-btn:hover {
            border-color: #58a6ff;
        }
        
        .chart-container {
            background: #0f1419;
            border: 1px solid #30363d;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .main-chart {
            height: 400px;
            margin-bottom: 20px;
            background: #1e2329;
            border-radius: 6px;
        }
        
        .sub-charts-container {
            display: flex;
            flex-direction: column;
            gap: 8px;
            min-height: 200px;
        }
        
        .status-info {
            background: #21262d;
            border: 1px solid #30363d;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .status-info h3 {
            margin: 0 0 10px 0;
            color: #58a6ff;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 12px;
        }
        
        .status-value {
            color: #00d4aa;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>📊 技术指标显示测试</h1>
            <p>测试各个技术指标子图的canvas显示效果和最高最低区间设置</p>
        </div>
        
        <div class="test-controls">
            <div class="control-group">
                <label>股票代码</label>
                <input type="text" id="stock-code" value="000001" placeholder="输入股票代码">
            </div>
            <div class="control-group">
                <label>时间周期</label>
                <select id="period">
                    <option value="1d">日线</option>
                    <option value="1w">周线</option>
                    <option value="1M">月线</option>
                </select>
            </div>
            <div class="control-group">
                <label>操作</label>
                <button onclick="loadTestData()">加载数据</button>
            </div>
        </div>
        
        <div class="indicator-controls">
            <div class="indicator-btn active" data-indicator="volume">成交量</div>
            <div class="indicator-btn active" data-indicator="macd">MACD</div>
            <div class="indicator-btn active" data-indicator="kdj">KDJ</div>
            <div class="indicator-btn active" data-indicator="rsi">RSI</div>
        </div>
        
        <div class="chart-container">
            <div id="main-chart" class="main-chart"></div>
            <div id="sub-charts" class="sub-charts-container"></div>
        </div>
        
        <div class="status-info">
            <h3>📈 显示状态</h3>
            <div class="status-item">
                <span>主图高度:</span>
                <span class="status-value" id="main-height">-</span>
            </div>
            <div class="status-item">
                <span>子图数量:</span>
                <span class="status-value" id="sub-count">-</span>
            </div>
            <div class="status-item">
                <span>子图容器总高度:</span>
                <span class="status-value" id="sub-container-height">-</span>
            </div>
            <div class="status-item">
                <span>单个子图高度:</span>
                <span class="status-value" id="individual-height">-</span>
            </div>
            <div class="status-item">
                <span>子图内容高度:</span>
                <span class="status-value" id="content-height">-</span>
            </div>
            <div class="status-item">
                <span>高度分配策略:</span>
                <span class="status-value" id="height-strategy">-</span>
            </div>
            <div class="status-item">
                <span>数据加载状态:</span>
                <span class="status-value" id="data-status">未加载</span>
            </div>
        </div>

        <div class="status-info">
            <h3>📊 指标数值范围</h3>
            <div class="status-item">
                <span>MACD范围:</span>
                <span class="status-value" id="macd-range">-</span>
            </div>
            <div class="status-item">
                <span>KDJ范围:</span>
                <span class="status-value" id="kdj-range">-</span>
            </div>
            <div class="status-item">
                <span>RSI范围:</span>
                <span class="status-value" id="rsi-range">-</span>
            </div>
            <div class="status-item">
                <span>成交量范围:</span>
                <span class="status-value" id="volume-range">-</span>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
    <script>
        // 初始化股票分析器
        const stockAnalyzer = new StockAnalyzer();
        
        // 指标控制
        document.querySelectorAll('.indicator-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                this.classList.toggle('active');
                updateActiveIndicators();
            });
        });
        
        function updateActiveIndicators() {
            const activeIndicators = Array.from(document.querySelectorAll('.indicator-btn.active'))
                .map(btn => btn.dataset.indicator);
            stockAnalyzer.activeIndicators = activeIndicators;
            console.log('活跃指标:', activeIndicators);
        }
        
        function loadTestData() {
            const stockCode = document.getElementById('stock-code').value;
            const period = document.getElementById('period').value;
            
            document.getElementById('data-status').textContent = '加载中...';
            
            // 更新活跃指标
            updateActiveIndicators();
            
            // 加载数据
            stockAnalyzer.updateChart(period, stockCode).then(() => {
                document.getElementById('data-status').textContent = '加载完成';
                updateStatusInfo();
            }).catch(error => {
                document.getElementById('data-status').textContent = '加载失败: ' + error.message;
                console.error('加载数据失败:', error);
            });
        }
        
        function updateStatusInfo() {
            const mainChart = document.getElementById('main-chart');
            const subCharts = document.getElementById('sub-charts');
            const subChartElements = subCharts.querySelectorAll('.sub-chart');

            document.getElementById('main-height').textContent = mainChart.clientHeight + 'px';
            document.getElementById('sub-count').textContent = subChartElements.length;

            // 更新子图表高度信息
            document.getElementById('sub-container-height').textContent = subCharts.clientHeight + 'px';

            if (subChartElements.length > 0) {
                const firstSubChart = subChartElements[0];
                const individualHeight = firstSubChart.clientHeight;
                const contentElement = firstSubChart.querySelector('.sub-chart-content');
                const contentHeight = contentElement ? contentElement.clientHeight : 0;

                document.getElementById('individual-height').textContent = individualHeight + 'px';
                document.getElementById('content-height').textContent = contentHeight + 'px';

                // 显示高度分配策略
                const strategy = getHeightStrategy(subChartElements.length);
                document.getElementById('height-strategy').textContent = strategy;
            } else {
                document.getElementById('individual-height').textContent = '-';
                document.getElementById('content-height').textContent = '-';
                document.getElementById('height-strategy').textContent = '-';
            }

            // 更新指标范围信息
            if (stockAnalyzer.lastKlineData && stockAnalyzer.lastIndicatorData) {
                const ranges = stockAnalyzer.getIndicatorRanges(stockAnalyzer.lastKlineData, stockAnalyzer.lastIndicatorData);

                document.getElementById('macd-range').textContent = ranges.macd ?
                    `${ranges.macd.min.toFixed(3)} ~ ${ranges.macd.max.toFixed(3)}` : '-';

                document.getElementById('kdj-range').textContent = ranges.kdj ?
                    `${ranges.kdj.min.toFixed(1)} ~ ${ranges.kdj.max.toFixed(1)}` : '-';

                document.getElementById('rsi-range').textContent = ranges.rsi ?
                    `${ranges.rsi.min.toFixed(1)} ~ ${ranges.rsi.max.toFixed(1)}` : '-';

                document.getElementById('volume-range').textContent = ranges.volume ?
                    `${formatVolume(ranges.volume.min)} ~ ${formatVolume(ranges.volume.max)}` : '-';
            }
        }

        function getHeightStrategy(count) {
            switch (count) {
                case 1: return '单指标充足空间';
                case 2: return '双指标平衡分配';
                case 3: return '三指标标准分配';
                case 4: return '四指标紧凑分配';
                default: return `${count}指标最小空间`;
            }
        }

        function formatVolume(volume) {
            if (volume >= 100000000) {
                return (volume / 100000000).toFixed(1) + '亿';
            } else if (volume >= 10000) {
                return (volume / 10000).toFixed(1) + '万';
            } else {
                return volume.toFixed(0);
            }
        }
        
        // 页面加载完成后自动加载测试数据
        window.addEventListener('load', function() {
            setTimeout(() => {
                loadTestData();
            }, 1000);
        });
    </script>
</body>
</html>
