<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>子图表高度分配测试</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            background: #0f1419;
            color: #d1d4dc;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
        }
        
        .test-container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-header h1 {
            color: #00d4aa;
            margin-bottom: 10px;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .test-case {
            background: #21262d;
            border: 1px solid #30363d;
            border-radius: 8px;
            padding: 20px;
        }
        
        .test-case h3 {
            margin: 0 0 15px 0;
            color: #58a6ff;
            text-align: center;
        }
        
        .indicator-selection {
            display: flex;
            gap: 8px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }
        
        .indicator-btn {
            padding: 4px 8px;
            background: #21262d;
            border: 1px solid #30363d;
            border-radius: 4px;
            color: #d1d4dc;
            cursor: pointer;
            font-size: 11px;
            transition: all 0.2s;
        }
        
        .indicator-btn.active {
            background: #238636;
            border-color: #2ea043;
        }
        
        .chart-container {
            background: #0f1419;
            border: 1px solid #30363d;
            border-radius: 6px;
            margin-bottom: 15px;
            overflow: hidden;
        }
        
        .main-chart {
            height: 200px;
            background: #1e2329;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #8b949e;
            font-size: 12px;
        }
        
        .sub-charts-container {
            display: flex;
            flex-direction: column;
            min-height: 100px;
        }
        
        .height-info {
            background: #161b22;
            border: 1px solid #30363d;
            border-radius: 6px;
            padding: 12px;
            font-size: 11px;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 4px;
        }
        
        .info-value {
            color: #58a6ff;
            font-weight: bold;
        }
        
        .controls {
            text-align: center;
            margin-bottom: 30px;
        }
        
        button {
            background: #238636;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        button:hover {
            background: #2ea043;
        }
        
        .summary {
            background: #21262d;
            border: 1px solid #30363d;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .summary h3 {
            color: #58a6ff;
            margin-top: 0;
        }
        
        .summary-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
        }
        
        .summary-table th,
        .summary-table td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #30363d;
        }
        
        .summary-table th {
            background: #161b22;
            color: #58a6ff;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>📏 子图表高度分配测试</h1>
            <p>测试不同指标数量下的动态高度分配策略</p>
        </div>
        
        <div class="controls">
            <button onclick="loadAllTests()">加载所有测试</button>
            <button onclick="generateHeightSummary()">生成高度分配总结</button>
        </div>
        
        <div class="test-grid">
            <!-- 1个指标 -->
            <div class="test-case">
                <h3>1个指标 - 充足空间</h3>
                <div class="indicator-selection">
                    <div class="indicator-btn active" data-indicator="volume">成交量</div>
                </div>
                <div class="chart-container">
                    <div class="main-chart">主图 (200px)</div>
                    <div id="sub-charts-1" class="sub-charts-container"></div>
                </div>
                <div class="height-info" id="info-1"></div>
            </div>
            
            <!-- 2个指标 -->
            <div class="test-case">
                <h3>2个指标 - 平衡分配</h3>
                <div class="indicator-selection">
                    <div class="indicator-btn active" data-indicator="volume">成交量</div>
                    <div class="indicator-btn active" data-indicator="macd">MACD</div>
                </div>
                <div class="chart-container">
                    <div class="main-chart">主图 (200px)</div>
                    <div id="sub-charts-2" class="sub-charts-container"></div>
                </div>
                <div class="height-info" id="info-2"></div>
            </div>
            
            <!-- 3个指标 -->
            <div class="test-case">
                <h3>3个指标 - 标准分配</h3>
                <div class="indicator-selection">
                    <div class="indicator-btn active" data-indicator="volume">成交量</div>
                    <div class="indicator-btn active" data-indicator="macd">MACD</div>
                    <div class="indicator-btn active" data-indicator="kdj">KDJ</div>
                </div>
                <div class="chart-container">
                    <div class="main-chart">主图 (200px)</div>
                    <div id="sub-charts-3" class="sub-charts-container"></div>
                </div>
                <div class="height-info" id="info-3"></div>
            </div>
            
            <!-- 4个指标 -->
            <div class="test-case">
                <h3>4个指标 - 紧凑分配</h3>
                <div class="indicator-selection">
                    <div class="indicator-btn active" data-indicator="volume">成交量</div>
                    <div class="indicator-btn active" data-indicator="macd">MACD</div>
                    <div class="indicator-btn active" data-indicator="kdj">KDJ</div>
                    <div class="indicator-btn active" data-indicator="rsi">RSI</div>
                </div>
                <div class="chart-container">
                    <div class="main-chart">主图 (200px)</div>
                    <div id="sub-charts-4" class="sub-charts-container"></div>
                </div>
                <div class="height-info" id="info-4"></div>
            </div>
        </div>
        
        <div class="summary">
            <h3>📊 高度分配策略总结</h3>
            <table class="summary-table">
                <thead>
                    <tr>
                        <th>指标数量</th>
                        <th>分配策略</th>
                        <th>单个高度</th>
                        <th>总高度</th>
                        <th>内容高度</th>
                        <th>空间利用率</th>
                    </tr>
                </thead>
                <tbody id="summary-tbody">
                    <tr><td colspan="6">点击"生成高度分配总结"查看详细信息</td></tr>
                </tbody>
            </table>
        </div>
    </div>

    <script src="script.js"></script>
    <script>
        // 模拟股票分析器实例
        const stockAnalyzer = new StockAnalyzer();
        
        // 模拟数据
        const mockKlineData = {
            klines: Array.from({length: 30}, (_, i) => ({
                date: new Date(Date.now() - (29-i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                open: 10 + Math.random() * 2,
                close: 10 + Math.random() * 2,
                high: 11 + Math.random() * 2,
                low: 9 + Math.random() * 2,
                volume: 1000000 + Math.random() * 5000000
            }))
        };
        
        const mockIndicatorData = {
            indicators: {
                volume: mockKlineData.klines.map(k => k.volume),
                macd: {
                    macd: Array.from({length: 30}, () => (Math.random() - 0.5) * 0.1),
                    dif: Array.from({length: 30}, () => (Math.random() - 0.5) * 0.2),
                    dea: Array.from({length: 30}, () => (Math.random() - 0.5) * 0.15)
                },
                kdj: {
                    k: Array.from({length: 30}, () => 20 + Math.random() * 60),
                    d: Array.from({length: 30}, () => 20 + Math.random() * 60),
                    j: Array.from({length: 30}, () => -20 + Math.random() * 140)
                },
                rsi: {
                    rsi6: Array.from({length: 30}, () => 30 + Math.random() * 40),
                    rsi12: Array.from({length: 30}, () => 30 + Math.random() * 40),
                    rsi24: Array.from({length: 30}, () => 30 + Math.random() * 40)
                }
            }
        };
        
        async function loadAllTests() {
            for (let i = 1; i <= 4; i++) {
                await loadTestCase(i);
                await new Promise(resolve => setTimeout(resolve, 200)); // 延迟以便观察
            }
        }
        
        async function loadTestCase(caseNumber) {
            const indicators = ['volume', 'macd', 'kdj', 'rsi'].slice(0, caseNumber);
            const container = document.getElementById(`sub-charts-${caseNumber}`);
            
            // 设置活跃指标
            stockAnalyzer.activeIndicators = indicators;
            
            // 清空容器
            container.innerHTML = '';
            
            // 创建子图表
            stockAnalyzer.updateSubCharts(container, mockKlineData, mockIndicatorData);
            
            // 更新信息显示
            setTimeout(() => {
                updateTestCaseInfo(caseNumber, indicators.length);
            }, 500);
        }
        
        function updateTestCaseInfo(caseNumber, indicatorCount) {
            const container = document.getElementById(`sub-charts-${caseNumber}`);
            const infoDiv = document.getElementById(`info-${caseNumber}`);
            
            const heightConfig = stockAnalyzer.calculateSubChartHeights(indicatorCount);
            const actualHeight = container.clientHeight;
            const subCharts = container.querySelectorAll('.sub-chart');
            
            let actualIndividualHeight = 0;
            let actualContentHeight = 0;
            
            if (subCharts.length > 0) {
                actualIndividualHeight = subCharts[0].clientHeight;
                const contentElement = subCharts[0].querySelector('.sub-chart-content');
                actualContentHeight = contentElement ? contentElement.clientHeight : 0;
            }
            
            infoDiv.innerHTML = `
                <div class="info-row">
                    <span>配置总高度:</span>
                    <span class="info-value">${heightConfig.totalHeight}px</span>
                </div>
                <div class="info-row">
                    <span>实际总高度:</span>
                    <span class="info-value">${actualHeight}px</span>
                </div>
                <div class="info-row">
                    <span>配置单个高度:</span>
                    <span class="info-value">${heightConfig.individualHeight}px</span>
                </div>
                <div class="info-row">
                    <span>实际单个高度:</span>
                    <span class="info-value">${actualIndividualHeight}px</span>
                </div>
                <div class="info-row">
                    <span>配置内容高度:</span>
                    <span class="info-value">${heightConfig.contentHeight}px</span>
                </div>
                <div class="info-row">
                    <span>实际内容高度:</span>
                    <span class="info-value">${actualContentHeight}px</span>
                </div>
                <div class="info-row">
                    <span>标题高度:</span>
                    <span class="info-value">${heightConfig.headerHeight}px</span>
                </div>
                <div class="info-row">
                    <span>间距:</span>
                    <span class="info-value">${heightConfig.indicatorGap}px</span>
                </div>
            `;
        }
        
        function generateHeightSummary() {
            const tbody = document.getElementById('summary-tbody');
            let html = '';
            
            for (let count = 1; count <= 5; count++) {
                const heightConfig = stockAnalyzer.calculateSubChartHeights(count);
                const strategy = getStrategyName(count);
                const utilization = ((heightConfig.contentHeight / heightConfig.individualHeight) * 100).toFixed(1);
                
                html += `
                    <tr>
                        <td>${count}</td>
                        <td>${strategy}</td>
                        <td>${heightConfig.individualHeight}px</td>
                        <td>${heightConfig.totalHeight}px</td>
                        <td>${heightConfig.contentHeight}px</td>
                        <td>${utilization}%</td>
                    </tr>
                `;
            }
            
            tbody.innerHTML = html;
        }
        
        function getStrategyName(count) {
            switch (count) {
                case 1: return '充足空间';
                case 2: return '平衡分配';
                case 3: return '标准分配';
                case 4: return '紧凑分配';
                default: return '最小空间';
            }
        }
        
        // 页面加载后自动运行测试
        window.addEventListener('load', function() {
            setTimeout(() => {
                loadAllTests();
                setTimeout(generateHeightSummary, 2000);
            }, 1000);
        });
    </script>
</body>
</html>
