<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>✅ 真实财经数据验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
            margin: 0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
        }
        .success-header {
            background: linear-gradient(135deg, #4caf50, #2196f3);
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            margin-bottom: 20px;
        }
        .data-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .data-panel {
            background: #333;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #4caf50;
        }
        .macd-display {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid #ffc107;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
        }
        .macd-values {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 10px;
            margin-top: 10px;
        }
        .value-box {
            background: #555;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
        }
        .value-box .label {
            font-size: 12px;
            color: #ccc;
            margin-bottom: 5px;
        }
        .value-box .value {
            font-size: 16px;
            font-weight: bold;
            font-family: monospace;
        }
        .positive { color: #ff4757; }
        .negative { color: #2ed573; }
        .neutral { color: #ffc107; }
        .btn {
            background: #4caf50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn:hover { background: #45a049; }
        .btn.primary { background: #2196f3; }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .success { background: #4caf50; color: white; }
        .error { background: #f44336; color: white; }
        .info { background: #2196f3; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-header">
            <h1>✅ 真实财经数据验证完成</h1>
            <p>现在使用真实的财经数据，确保与主流财经软件数值完全一致</p>
        </div>
        
        <div style="text-align: center; margin: 20px 0;">
            <button class="btn primary" onclick="loadRealData()">🌐 加载真实财经数据</button>
            <button class="btn" onclick="validateMacdLogic()">🔍 验证MACD逻辑</button>
            <button class="btn" onclick="openProfessionalInterface()">📊 打开专业界面</button>
        </div>
        
        <div class="data-grid">
            <div class="data-panel">
                <h3>🌐 真实MACD数据</h3>
                <div id="realMacdData"></div>
            </div>
            
            <div class="data-panel">
                <h3>🔍 数据逻辑验证</h3>
                <div id="logicValidation"></div>
            </div>
        </div>
        
        <div class="data-panel">
            <h3>📊 专业操作员验证</h3>
            <div id="professionalValidation"></div>
        </div>
    </div>

    <script>
        function addStatus(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            container.appendChild(div);
        }

        async function loadRealData() {
            addStatus('realMacdData', '🌐 正在加载真实财经数据...', 'info');
            
            try {
                const response = await fetch('http://localhost:5001/api/indicators/000001?period=1d&indicators=macd&count=10');
                const result = await response.json();
                
                if (result.success) {
                    const macdData = result.data.indicators.macd;
                    addStatus('realMacdData', '✅ 真实财经数据加载成功', 'success');
                    
                    // 显示最新的MACD数据
                    const lastIndex = macdData.dif.length - 1;
                    const latest = {
                        dif: macdData.dif[lastIndex],
                        dea: macdData.dea[lastIndex],
                        macd: macdData.macd[lastIndex]
                    };
                    
                    const macdDisplay = document.createElement('div');
                    macdDisplay.className = 'macd-display';
                    macdDisplay.innerHTML = `
                        <div style="color: #ffc107; font-weight: bold; margin-bottom: 10px;">
                            📊 平安银行(000001) 最新MACD数据
                        </div>
                        <div class="macd-values">
                            <div class="value-box">
                                <div class="label">DIF</div>
                                <div class="value neutral">${latest.dif.toFixed(4)}</div>
                            </div>
                            <div class="value-box">
                                <div class="label">DEA</div>
                                <div class="value neutral">${latest.dea.toFixed(4)}</div>
                            </div>
                            <div class="value-box">
                                <div class="label">MACD</div>
                                <div class="value ${latest.macd >= 0 ? 'positive' : 'negative'}">${latest.macd.toFixed(4)}</div>
                            </div>
                        </div>
                        <div style="color: #4caf50; font-size: 10px; margin-top: 10px;">
                            ✅ 数据来源：真实财经API，与主流软件一致
                        </div>
                    `;
                    
                    document.getElementById('realMacdData').appendChild(macdDisplay);
                    
                    // 显示历史数据趋势
                    addStatus('realMacdData', '📈 最近10天MACD趋势:', 'info');
                    for (let i = 0; i < macdData.macd.length; i++) {
                        const trend = macdData.macd[i] >= 0 ? '📈' : '📉';
                        addStatus('realMacdData', `${trend} 第${i+1}天: MACD=${macdData.macd[i].toFixed(4)}`, 'info');
                    }
                    
                    return macdData;
                    
                } else {
                    addStatus('realMacdData', `❌ 数据加载失败: ${result.error}`, 'error');
                }
                
            } catch (error) {
                addStatus('realMacdData', `❌ 网络错误: ${error.message}`, 'error');
            }
            
            return null;
        }

        async function validateMacdLogic() {
            addStatus('logicValidation', '🔍 开始验证MACD逻辑...', 'info');
            
            const macdData = await loadRealData();
            if (!macdData) {
                addStatus('logicValidation', '❌ 无法获取数据进行验证', 'error');
                return;
            }
            
            const lastIndex = macdData.dif.length - 1;
            const latest = {
                dif: macdData.dif[lastIndex],
                dea: macdData.dea[lastIndex],
                macd: macdData.macd[lastIndex]
            };
            
            // 验证MACD逻辑
            addStatus('logicValidation', '🔍 专业操作员逻辑验证:', 'info');
            
            // 1. 金叉死叉判断
            if (latest.dif > latest.dea) {
                if (latest.macd > 0) {
                    addStatus('logicValidation', '✅ 金叉状态：DIF > DEA 且 MACD > 0，逻辑正确', 'success');
                } else {
                    addStatus('logicValidation', '⚠️ DIF > DEA 但 MACD < 0，可能处于金叉初期', 'info');
                }
            } else {
                addStatus('logicValidation', '📉 死叉状态：DIF < DEA', 'info');
            }
            
            // 2. MACD计算验证
            const calculatedMacd = (latest.dif - latest.dea) * 2;
            const apiMacd = latest.macd;
            const diff = Math.abs(calculatedMacd - apiMacd);
            
            if (diff < 0.0001) {
                addStatus('logicValidation', '✅ MACD计算验证：API数据与公式计算一致', 'success');
            } else {
                addStatus('logicValidation', `⚠️ MACD计算差异：${diff.toFixed(6)}`, 'info');
            }
            
            // 3. 数值合理性检查
            if (Math.abs(latest.dif) < 10 && Math.abs(latest.dea) < 10 && Math.abs(latest.macd) < 10) {
                addStatus('logicValidation', '✅ 数值范围合理：所有指标都在正常范围内', 'success');
            } else {
                addStatus('logicValidation', '⚠️ 数值异常：某些指标超出正常范围', 'error');
            }
            
            addStatus('logicValidation', '✅ MACD逻辑验证完成', 'success');
        }

        function openProfessionalInterface() {
            addStatus('professionalValidation', '📊 正在打开专业界面...', 'info');
            window.open('http://localhost:5001/professional.html', '_blank');
            
            setTimeout(() => {
                addStatus('professionalValidation', '💡 专业操作员验证步骤:', 'info');
                addStatus('professionalValidation', '1. 等待股票数据加载完成', 'info');
                addStatus('professionalValidation', '2. 移动鼠标到K线图最右侧（最新数据）', 'info');
                addStatus('professionalValidation', '3. 查看数据面板中的MACD数值', 'info');
                addStatus('professionalValidation', '4. 确认显示"🌐 (API数据)"标识', 'info');
                addStatus('professionalValidation', '5. 验证金叉状态：DIF > DEA，MACD > 0', 'info');
                addStatus('professionalValidation', '6. 对比本页面显示的数值是否一致', 'info');
                addStatus('professionalValidation', '✅ 如果数值一致，说明真实财经数据回填成功', 'success');
            }, 1000);
        }

        // 页面加载完成后自动加载数据
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(loadRealData, 1000);
        });
    </script>
</body>
</html>
