<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 最终MACD验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
            margin: 0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
        }
        .test-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .test-panel {
            background: #333;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #2196f3;
        }
        .btn {
            background: #4caf50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #45a049; }
        .btn.primary { background: #2196f3; }
        .result {
            background: #444;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .success { border-left: 4px solid #4caf50; }
        .error { border-left: 4px solid #f44336; }
        .warning { border-left: 4px solid #ff9800; }
        .macd-display {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 10px;
            margin: 10px 0;
        }
        .macd-value {
            background: #555;
            padding: 8px;
            border-radius: 4px;
            text-align: center;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
            font-size: 11px;
        }
        .comparison-table th, .comparison-table td {
            border: 1px solid #555;
            padding: 5px;
            text-align: center;
        }
        .comparison-table th {
            background: #444;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 最终MACD数据验证</h1>
        <p>验证修改后的API是否正确提供MACD数据，并确保专业界面能正确显示</p>
        
        <div style="text-align: center; margin: 20px 0;">
            <button class="btn primary" onclick="runFullTest()">🚀 运行完整测试</button>
            <button class="btn" onclick="testApiData()">📡 测试API数据</button>
            <button class="btn" onclick="testFrontendIntegration()">🔗 测试前端集成</button>
            <button class="btn" onclick="openProfessionalInterface()">📊 打开专业界面</button>
        </div>
        
        <div class="test-grid">
            <div class="test-panel">
                <h3>📡 API数据测试</h3>
                <div id="apiResults"></div>
            </div>
            
            <div class="test-panel">
                <h3>🔗 前端集成测试</h3>
                <div id="frontendResults"></div>
            </div>
        </div>
        
        <div class="test-panel">
            <h3>📊 MACD数据对比</h3>
            <div id="comparisonResults"></div>
        </div>
        
        <div class="test-panel">
            <h3>✅ 最终验证结果</h3>
            <div id="finalResults"></div>
        </div>
    </div>

    <script>
        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            container.appendChild(div);
        }

        async function testApiData() {
            addResult('apiResults', '📡 开始测试API数据...', 'info');
            
            try {
                const response = await fetch('http://localhost:5001/api/indicators/000001?period=1d&indicators=macd,kdj&count=100');
                const result = await response.json();
                
                if (result.success) {
                    addResult('apiResults', '✅ API调用成功', 'success');
                    
                    const indicators = result.data.indicators;
                    if (indicators.macd) {
                        const macdData = indicators.macd;
                        addResult('apiResults', `📊 MACD数据结构: ${Object.keys(macdData).join(', ')}`, 'info');
                        
                        // 检查数据完整性
                        const lengths = {
                            dif: macdData.dif?.length || 0,
                            dea: macdData.dea?.length || 0,
                            macd: macdData.macd?.length || 0
                        };
                        
                        addResult('apiResults', `📊 数据长度: DIF=${lengths.dif}, DEA=${lengths.dea}, MACD=${lengths.macd}`, 'info');
                        
                        // 检查数据质量
                        const validMacd = macdData.macd.filter(v => v !== null && !isNaN(v)).length;
                        const validDif = macdData.dif.filter(v => v !== null && !isNaN(v)).length;
                        const validDea = macdData.dea.filter(v => v !== null && !isNaN(v)).length;
                        
                        addResult('apiResults', `📊 有效数据: DIF=${validDif}, DEA=${validDea}, MACD=${validMacd}`, 'info');
                        
                        // 显示最新值
                        const lastIndex = macdData.dif.length - 1;
                        if (lastIndex >= 0) {
                            const latest = {
                                dif: macdData.dif[lastIndex],
                                dea: macdData.dea[lastIndex],
                                macd: macdData.macd[lastIndex]
                            };
                            
                            addResult('apiResults', `📊 最新MACD值: DIF=${latest.dif?.toFixed(4) || 'null'}, DEA=${latest.dea?.toFixed(4) || 'null'}, MACD=${latest.macd?.toFixed(4) || 'null'}`, latest.macd !== null ? 'success' : 'warning');
                        }
                        
                        return macdData;
                        
                    } else {
                        addResult('apiResults', '❌ 未找到MACD数据', 'error');
                    }
                    
                } else {
                    addResult('apiResults', `❌ API调用失败: ${result.error}`, 'error');
                }
                
            } catch (error) {
                addResult('apiResults', `❌ 网络错误: ${error.message}`, 'error');
            }
            
            return null;
        }

        async function testFrontendIntegration() {
            addResult('frontendResults', '🔗 开始测试前端集成...', 'info');
            
            try {
                // 模拟前端的完整数据获取流程
                const queryCode = '000001';
                const period = '1d';
                const activeIndicators = ['volume', 'macd', 'kdj'];
                const activeMAs = [5, 10, 20];
                
                const [klineResponse, indicatorResponse] = await Promise.all([
                    fetch(`http://localhost:5001/api/kline/${queryCode}?period=${period}&count=100`),
                    fetch(`http://localhost:5001/api/indicators/${queryCode}?period=${period}&mas=${activeMAs.join(',')}&indicators=${activeIndicators.join(',')}`)
                ]);
                
                if (klineResponse.ok && indicatorResponse.ok) {
                    const klineResult = await klineResponse.json();
                    const indicatorResult = await indicatorResponse.json();
                    
                    if (klineResult.success && indicatorResult.success) {
                        addResult('frontendResults', '✅ 前端数据流测试成功', 'success');
                        
                        // 验证数据同步
                        const klineCount = klineResult.data.klines.length;
                        const macdCount = indicatorResult.data.indicators.macd?.dif?.length || 0;
                        
                        addResult('frontendResults', `📊 数据同步检查: K线${klineCount}条, MACD${macdCount}条`, 'info');
                        
                        if (klineCount === macdCount) {
                            addResult('frontendResults', '✅ 数据长度同步正确', 'success');
                        } else {
                            addResult('frontendResults', '⚠️ 数据长度不同步', 'warning');
                        }
                        
                        // 测试数据面板函数
                        const macdData = indicatorResult.data.indicators.macd;
                        if (macdData) {
                            // 模拟getIndicatorValueAtIndex函数
                            function getIndicatorValueAtIndex(values, index) {
                                if (!values || !Array.isArray(values)) return '--';
                                if (index < 0 || index >= values.length) return '--';
                                const value = values[index];
                                if (value === null || value === undefined) return '--';
                                if (isNaN(value)) return '--';
                                return typeof value === 'number' ? value.toFixed(4) : value;
                            }
                            
                            // 测试关键索引
                            const testIndices = [29, 50, 99];
                            testIndices.forEach(index => {
                                if (index < macdData.dif.length) {
                                    const dif = getIndicatorValueAtIndex(macdData.dif, index);
                                    const dea = getIndicatorValueAtIndex(macdData.dea, index);
                                    const macd = getIndicatorValueAtIndex(macdData.macd, index);
                                    
                                    const hasData = dif !== '--' && dea !== '--' && macd !== '--';
                                    addResult('frontendResults', `索引${index}: DIF=${dif} DEA=${dea} MACD=${macd} ${hasData ? '✅' : '⚠️'}`, hasData ? 'success' : 'warning');
                                }
                            });
                        }
                        
                        return indicatorResult.data;
                        
                    } else {
                        addResult('frontendResults', '❌ 数据获取失败', 'error');
                    }
                } else {
                    addResult('frontendResults', '❌ API请求失败', 'error');
                }
                
            } catch (error) {
                addResult('frontendResults', `❌ 前端集成测试失败: ${error.message}`, 'error');
            }
            
            return null;
        }

        async function runFullTest() {
            addResult('finalResults', '🚀 开始运行完整测试...', 'info');
            
            // 清空之前的结果
            ['apiResults', 'frontendResults', 'comparisonResults'].forEach(id => {
                document.getElementById(id).innerHTML = '';
            });
            
            // 运行所有测试
            const apiData = await testApiData();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            const frontendData = await testFrontendIntegration();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 最终验证
            if (apiData && frontendData) {
                addResult('finalResults', '✅ 所有测试完成', 'success');
                addResult('finalResults', '✅ MACD数据API正常工作', 'success');
                addResult('finalResults', '✅ 前端数据流正常', 'success');
                addResult('finalResults', '🎯 建议：现在可以打开专业界面验证数据面板显示', 'info');
            } else {
                addResult('finalResults', '❌ 部分测试失败，请检查错误信息', 'error');
            }
        }

        function openProfessionalInterface() {
            addResult('finalResults', '📊 正在打开专业界面...', 'info');
            window.open('http://localhost:5001/professional.html', '_blank');
            addResult('finalResults', '💡 请在专业界面中移动鼠标到K线图上，查看数据面板中的MACD数值', 'info');
        }

        // 页面加载完成后自动运行测试
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(runFullTest, 1000);
        });
    </script>
</body>
</html>
