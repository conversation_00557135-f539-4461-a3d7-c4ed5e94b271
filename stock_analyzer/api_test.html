<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API调用测试</title>
    <style>
        body {
            background: #0f1419;
            color: #d1d4dc;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .test-section {
            background: #21262d;
            border: 1px solid #30363d;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .test-section h3 {
            color: #58a6ff;
            margin-top: 0;
        }
        
        button {
            background: #238636;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #2ea043;
        }
        
        .result {
            background: #161b22;
            border: 1px solid #30363d;
            border-radius: 6px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .url {
            color: #79c0ff;
            word-break: break-all;
        }
        
        .success {
            color: #56d364;
        }
        
        .error {
            color: #f85149;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 API调用测试</h1>
        
        <div class="test-section">
            <h3>1. 测试旧版API调用（不带indicators参数）</h3>
            <button onclick="testOldAPI()">测试旧版API</button>
            <div id="old-api-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>2. 测试新版API调用（带indicators参数）</h3>
            <button onclick="testNewAPI()">测试新版API - 所有指标</button>
            <button onclick="testNewAPIKDJ()">测试新版API - 仅KDJ</button>
            <button onclick="testNewAPISelected()">测试新版API - 选中指标</button>
            <div id="new-api-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>3. 前端指标状态测试</h3>
            <button onclick="testFrontendState()">检查前端指标状态</button>
            <div id="frontend-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>4. 模拟前端API调用</h3>
            <button onclick="simulateFrontendCall()">模拟前端调用</button>
            <div id="simulate-result" class="result"></div>
        </div>
    </div>

    <script src="script.js"></script>
    <script>
        async function testOldAPI() {
            const resultDiv = document.getElementById('old-api-result');
            const url = '/api/indicators/300340?period=1d&mas=5,10,20';
            
            resultDiv.innerHTML = `<div class="url">请求URL: ${url}</div>\n正在请求...`;
            
            try {
                const response = await fetch(url);
                const data = await response.json();
                
                resultDiv.innerHTML = `<div class="url">请求URL: ${url}</div>\n<div class="success">✅ 请求成功</div>\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.innerHTML = `<div class="url">请求URL: ${url}</div>\n<div class="error">❌ 请求失败: ${error.message}</div>`;
            }
        }
        
        async function testNewAPI() {
            const resultDiv = document.getElementById('new-api-result');
            const url = '/api/indicators/300340?period=1d&mas=5,10,20&indicators=volume,macd,kdj,rsi,boll';
            
            resultDiv.innerHTML = `<div class="url">请求URL: ${url}</div>\n正在请求...`;
            
            try {
                const response = await fetch(url);
                const data = await response.json();
                
                resultDiv.innerHTML = `<div class="url">请求URL: ${url}</div>\n<div class="success">✅ 请求成功</div>\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.innerHTML = `<div class="url">请求URL: ${url}</div>\n<div class="error">❌ 请求失败: ${error.message}</div>`;
            }
        }
        
        async function testNewAPIKDJ() {
            const resultDiv = document.getElementById('new-api-result');
            const url = '/api/indicators/300340?period=1d&mas=5,10,20&indicators=kdj';
            
            resultDiv.innerHTML = `<div class="url">请求URL: ${url}</div>\n正在请求...`;
            
            try {
                const response = await fetch(url);
                const data = await response.json();
                
                resultDiv.innerHTML = `<div class="url">请求URL: ${url}</div>\n<div class="success">✅ 请求成功</div>\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.innerHTML = `<div class="url">请求URL: ${url}</div>\n<div class="error">❌ 请求失败: ${error.message}</div>`;
            }
        }
        
        async function testNewAPISelected() {
            const resultDiv = document.getElementById('new-api-result');
            const url = '/api/indicators/300340?period=1d&mas=5,10,20&indicators=volume,macd,kdj';
            
            resultDiv.innerHTML = `<div class="url">请求URL: ${url}</div>\n正在请求...`;
            
            try {
                const response = await fetch(url);
                const data = await response.json();
                
                resultDiv.innerHTML = `<div class="url">请求URL: ${url}</div>\n<div class="success">✅ 请求成功</div>\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.innerHTML = `<div class="url">请求URL: ${url}</div>\n<div class="error">❌ 请求失败: ${error.message}</div>`;
            }
        }
        
        function testFrontendState() {
            const resultDiv = document.getElementById('frontend-result');
            
            try {
                // 检查是否有StockAnalyzer实例
                if (typeof StockAnalyzer !== 'undefined') {
                    const analyzer = new StockAnalyzer();
                    
                    const state = {
                        activeIndicators: analyzer.activeIndicators,
                        activeMAs: analyzer.activeMAs,
                        currentStock: analyzer.currentStock,
                        hasKDJ: analyzer.activeIndicators.includes('kdj')
                    };
                    
                    resultDiv.innerHTML = `<div class="success">✅ StockAnalyzer可用</div>\n${JSON.stringify(state, null, 2)}`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ StockAnalyzer未定义</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 检查失败: ${error.message}</div>`;
            }
        }
        
        async function simulateFrontendCall() {
            const resultDiv = document.getElementById('simulate-result');
            
            try {
                if (typeof StockAnalyzer !== 'undefined') {
                    const analyzer = new StockAnalyzer();
                    
                    // 模拟前端的API调用
                    const stockCode = '300340';
                    const period = '1d';
                    const indicators = analyzer.activeIndicators.join(',');
                    const mas = analyzer.activeMAs.join(',');
                    
                    const url = `/api/indicators/${stockCode}?period=${period}&mas=${mas}&indicators=${indicators}`;
                    
                    resultDiv.innerHTML = `<div class="url">模拟前端调用URL: ${url}</div>\n正在请求...`;
                    
                    const response = await fetch(url);
                    const data = await response.json();
                    
                    resultDiv.innerHTML = `<div class="url">模拟前端调用URL: ${url}</div>\n<div class="success">✅ 请求成功</div>\n返回的指标: ${Object.keys(data.data.indicators).join(', ')}\n\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ StockAnalyzer未定义</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 模拟调用失败: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
