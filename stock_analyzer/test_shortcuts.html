<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快捷键测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: #ffffff;
            padding: 20px;
            margin: 0;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
        }
        .shortcut-group {
            margin-bottom: 20px;
            padding: 15px;
            background: #333;
            border-radius: 6px;
        }
        .shortcut-group h3 {
            color: #ffc107;
            margin-top: 0;
        }
        .shortcut-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #444;
        }
        .shortcut-item:last-child {
            border-bottom: none;
        }
        .key {
            background: #555;
            color: #ffc107;
            padding: 4px 8px;
            border-radius: 4px;
            font-family: monospace;
            font-weight: bold;
        }
        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .status.success {
            background: #4caf50;
            color: white;
        }
        .status.error {
            background: #f44336;
            color: white;
        }
        .status.pending {
            background: #ff9800;
            color: white;
        }
        #log {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            height: 200px;
            overflow-y: auto;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎮 快捷键测试页面</h1>
        <p>按下相应的快捷键来测试功能是否正常工作</p>

        <div class="shortcut-group">
            <h3>📊 十字光标导航</h3>
            <div class="shortcut-item">
                <span>WASD 导航</span>
                <div>
                    <span class="key">A</span>
                    <span class="key">D</span>
                    <span class="key">W</span>
                    <span class="key">S</span>
                    <span class="status pending" id="wasd-status">待测试</span>
                </div>
            </div>
            <div class="shortcut-item">
                <span>方向键导航</span>
                <div>
                    <span class="key">← → ↑ ↓</span>
                    <span class="status pending" id="arrow-status">待测试</span>
                </div>
            </div>
            <div class="shortcut-item">
                <span>快速跳转</span>
                <div>
                    <span class="key">Q</span>
                    <span class="key">E</span>
                    <span class="key">Z</span>
                    <span class="key">X</span>
                    <span class="status pending" id="jump-status">待测试</span>
                </div>
            </div>
        </div>

        <div class="shortcut-group">
            <h3>⚡ 周期切换</h3>
            <div class="shortcut-item">
                <span>数字键切换</span>
                <div>
                    <span class="key">1-0</span>
                    <span class="status pending" id="period-status">待测试</span>
                </div>
            </div>
        </div>

        <div class="shortcut-group">
            <h3>📈 均线控制</h3>
            <div class="shortcut-item">
                <span>Shift + 字母</span>
                <div>
                    <span class="key">Shift+Q/W/E/R</span>
                    <span class="status pending" id="ma-status">待测试</span>
                </div>
            </div>
        </div>

        <div class="shortcut-group">
            <h3>🔧 功能控制</h3>
            <div class="shortcut-item">
                <span>核心功能</span>
                <div>
                    <span class="key">Space</span>
                    <span class="key">C</span>
                    <span class="key">V</span>
                    <span class="key">B</span>
                    <span class="status pending" id="function-status">待测试</span>
                </div>
            </div>
            <div class="shortcut-item">
                <span>系统功能</span>
                <div>
                    <span class="key">`</span>
                    <span class="key">F1</span>
                    <span class="key">F2</span>
                    <span class="status pending" id="system-status">待测试</span>
                </div>
            </div>
        </div>

        <div id="log"></div>
    </div>

    <script>
        const log = document.getElementById('log');
        
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'success' ? '#4caf50' : type === 'error' ? '#f44336' : '#ffc107';
            log.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            log.scrollTop = log.scrollHeight;
        }

        function updateStatus(id, status, text) {
            const element = document.getElementById(id);
            if (element) {
                element.className = `status ${status}`;
                element.textContent = text;
            }
        }

        // 测试快捷键
        document.addEventListener('keydown', (e) => {
            const key = e.key.toLowerCase();
            const isCtrl = e.ctrlKey || e.metaKey;
            const isShift = e.shiftKey;
            const isAlt = e.altKey;

            addLog(`按键: ${e.key} (Ctrl:${isCtrl}, Shift:${isShift}, Alt:${isAlt})`);

            // 测试WASD导航
            if (['a', 'd', 'w', 's'].includes(key) && !isCtrl && !isShift && !isAlt) {
                e.preventDefault();
                updateStatus('wasd-status', 'success', '✅ 正常');
                addLog(`WASD导航测试成功: ${key.toUpperCase()}`, 'success');
            }

            // 测试方向键
            if (['arrowleft', 'arrowright', 'arrowup', 'arrowdown'].includes(key) && !isCtrl && !isShift && !isAlt) {
                e.preventDefault();
                updateStatus('arrow-status', 'success', '✅ 正常');
                addLog(`方向键测试成功: ${e.key}`, 'success');
            }

            // 测试快速跳转
            if (['q', 'e', 'z', 'x'].includes(key) && !isCtrl && !isShift && !isAlt) {
                e.preventDefault();
                updateStatus('jump-status', 'success', '✅ 正常');
                addLog(`快速跳转测试成功: ${key.toUpperCase()}`, 'success');
            }

            // 测试数字键周期切换
            if (['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'].includes(key) && !isCtrl && !isShift && !isAlt) {
                e.preventDefault();
                updateStatus('period-status', 'success', '✅ 正常');
                addLog(`周期切换测试成功: ${key}`, 'success');
            }

            // 测试均线控制
            if (['q', 'w', 'e', 'r', 't', 'y', 'u'].includes(key) && !isCtrl && isShift && !isAlt) {
                e.preventDefault();
                updateStatus('ma-status', 'success', '✅ 正常');
                addLog(`均线控制测试成功: Shift+${key.toUpperCase()}`, 'success');
            }

            // 测试功能控制
            if ([' ', 'c', 'v', 'b'].includes(key) && !isCtrl && !isShift && !isAlt) {
                e.preventDefault();
                updateStatus('function-status', 'success', '✅ 正常');
                addLog(`功能控制测试成功: ${key === ' ' ? 'Space' : key.toUpperCase()}`, 'success');
            }

            // 测试系统功能
            if (['`', 'f1', 'f2'].includes(key) && !isCtrl && !isShift && !isAlt) {
                e.preventDefault();
                updateStatus('system-status', 'success', '✅ 正常');
                addLog(`系统功能测试成功: ${key.toUpperCase()}`, 'success');
            }

            // 检查浏览器冲突
            if (isCtrl) {
                if (['d', 'w', 't', 'f', 'h'].includes(key)) {
                    addLog(`⚠️ 检测到可能的浏览器冲突: Ctrl+${key.toUpperCase()}`, 'error');
                }
            }
        });

        addLog('快捷键测试页面已加载，开始测试快捷键功能');
        addLog('请按下相应的快捷键来测试功能');
    </script>
</body>
</html>
