<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 MACD数值验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
            margin: 0;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: #333;
            border-radius: 6px;
            overflow: hidden;
        }
        .data-table th, .data-table td {
            padding: 8px 12px;
            text-align: center;
            border-bottom: 1px solid #444;
            font-family: monospace;
            font-size: 12px;
        }
        .data-table th {
            background: #444;
            font-weight: bold;
        }
        .data-table tr:hover {
            background: #3a3a3a;
        }
        .positive { color: #4caf50; }
        .negative { color: #f44336; }
        .neutral { color: #ffc107; }
        .btn {
            background: #4caf50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #45a049; }
        .info-box {
            background: #333;
            padding: 15px;
            margin: 10px 0;
            border-radius: 6px;
            border-left: 4px solid #2196f3;
        }
        .latest-values {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin: 20px 0;
        }
        .value-card {
            background: #333;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
        }
        .value-card h3 {
            margin: 0 0 10px 0;
            color: #2196f3;
        }
        .value-card .value {
            font-size: 24px;
            font-weight: bold;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 MACD数值验证工具</h1>
        <p>获取平安银行(000001)的MACD数据，用于与财经网站对比验证</p>
        
        <button class="btn" onclick="loadMacdData()">🔄 获取MACD数据</button>
        <button class="btn" onclick="showLatest10()">📋 显示最新10天</button>
        <button class="btn" onclick="exportData()">📤 导出数据</button>
        
        <div class="info-box">
            <strong>使用说明：</strong>
            <br>1. 点击"获取MACD数据"获取最新的技术指标数据
            <br>2. 将显示的数值与东方财富、同花顺等财经网站的MACD数据进行对比
            <br>3. 重点关注最新几天的DIF、DEA、MACD数值是否一致
        </div>
        
        <div class="latest-values" id="latestValues" style="display: none;">
            <div class="value-card">
                <h3>DIF</h3>
                <div class="value" id="latestDif">--</div>
            </div>
            <div class="value-card">
                <h3>DEA</h3>
                <div class="value" id="latestDea">--</div>
            </div>
            <div class="value-card">
                <h3>MACD</h3>
                <div class="value" id="latestMacd">--</div>
            </div>
        </div>
        
        <div id="dataContainer"></div>
    </div>

    <script>
        let macdData = null;

        async function loadMacdData() {
            try {
                const response = await fetch('http://localhost:5001/api/indicators/000001?period=1d&indicators=macd&count=30');
                const result = await response.json();
                
                if (result.success) {
                    macdData = result.data;
                    displayMacdData();
                    updateLatestValues();
                    document.getElementById('latestValues').style.display = 'grid';
                } else {
                    alert('获取数据失败: ' + result.error);
                }
            } catch (error) {
                alert('网络错误: ' + error.message);
            }
        }

        function displayMacdData() {
            if (!macdData) return;
            
            const container = document.getElementById('dataContainer');
            const klines = macdData.klines;
            const macd = macdData.indicators.macd;
            
            let html = `
                <h3>📈 MACD技术指标数据 (最新30天)</h3>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>日期</th>
                            <th>收盘价</th>
                            <th>DIF</th>
                            <th>DEA</th>
                            <th>MACD</th>
                            <th>涨跌幅</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            for (let i = 0; i < klines.length; i++) {
                const kline = klines[i];
                const dif = macd.dif[i];
                const dea = macd.dea[i];
                const macdValue = macd.macd[i];
                
                const changeClass = kline.change > 0 ? 'positive' : kline.change < 0 ? 'negative' : 'neutral';
                const macdClass = macdValue > 0 ? 'positive' : macdValue < 0 ? 'negative' : 'neutral';
                
                html += `
                    <tr>
                        <td>${kline.date}</td>
                        <td class="${changeClass}">${kline.close.toFixed(2)}</td>
                        <td>${dif !== null ? dif.toFixed(4) : '--'}</td>
                        <td>${dea !== null ? dea.toFixed(4) : '--'}</td>
                        <td class="${macdClass}">${macdValue !== null ? macdValue.toFixed(4) : '--'}</td>
                        <td class="${changeClass}">${kline.change_percent.toFixed(2)}%</td>
                    </tr>
                `;
            }
            
            html += `
                    </tbody>
                </table>
                <div class="info-box">
                    <strong>数据说明：</strong>
                    <br>• DIF = EMA(12) - EMA(26)
                    <br>• DEA = EMA(DIF, 9)
                    <br>• MACD = (DIF - DEA) × 2
                    <br>• 数据来源：基于东方财富K线数据计算
                </div>
            `;
            
            container.innerHTML = html;
        }

        function updateLatestValues() {
            if (!macdData) return;
            
            const macd = macdData.indicators.macd;
            const lastIndex = macd.dif.length - 1;
            
            const latestDif = macd.dif[lastIndex];
            const latestDea = macd.dea[lastIndex];
            const latestMacd = macd.macd[lastIndex];
            
            document.getElementById('latestDif').textContent = latestDif !== null ? latestDif.toFixed(4) : '--';
            document.getElementById('latestDea').textContent = latestDea !== null ? latestDea.toFixed(4) : '--';
            document.getElementById('latestMacd').textContent = latestMacd !== null ? latestMacd.toFixed(4) : '--';
            
            // 设置颜色
            document.getElementById('latestDif').className = 'value ' + (latestDif > 0 ? 'positive' : latestDif < 0 ? 'negative' : 'neutral');
            document.getElementById('latestDea').className = 'value ' + (latestDea > 0 ? 'positive' : latestDea < 0 ? 'negative' : 'neutral');
            document.getElementById('latestMacd').className = 'value ' + (latestMacd > 0 ? 'positive' : latestMacd < 0 ? 'negative' : 'neutral');
        }

        function showLatest10() {
            if (!macdData) {
                alert('请先获取MACD数据');
                return;
            }
            
            const macd = macdData.indicators.macd;
            const klines = macdData.klines;
            const startIndex = Math.max(0, klines.length - 10);
            
            let output = '最新10天MACD数据：\n\n';
            output += '日期\t\t收盘价\tDIF\t\tDEA\t\tMACD\n';
            output += '='.repeat(60) + '\n';
            
            for (let i = startIndex; i < klines.length; i++) {
                const kline = klines[i];
                const dif = macd.dif[i];
                const dea = macd.dea[i];
                const macdValue = macd.macd[i];
                
                output += `${kline.date}\t${kline.close.toFixed(2)}\t`;
                output += `${dif !== null ? dif.toFixed(4) : '--'}\t`;
                output += `${dea !== null ? dea.toFixed(4) : '--'}\t`;
                output += `${macdValue !== null ? macdValue.toFixed(4) : '--'}\n`;
            }
            
            // 创建一个文本区域显示数据
            const textarea = document.createElement('textarea');
            textarea.value = output;
            textarea.style.width = '100%';
            textarea.style.height = '300px';
            textarea.style.fontFamily = 'monospace';
            textarea.style.fontSize = '12px';
            textarea.style.background = '#333';
            textarea.style.color = '#fff';
            textarea.style.border = '1px solid #555';
            textarea.style.padding = '10px';
            
            const modal = document.createElement('div');
            modal.style.position = 'fixed';
            modal.style.top = '0';
            modal.style.left = '0';
            modal.style.width = '100%';
            modal.style.height = '100%';
            modal.style.background = 'rgba(0,0,0,0.8)';
            modal.style.display = 'flex';
            modal.style.alignItems = 'center';
            modal.style.justifyContent = 'center';
            modal.style.zIndex = '1000';
            
            const content = document.createElement('div');
            content.style.background = '#2a2a2a';
            content.style.padding = '20px';
            content.style.borderRadius = '8px';
            content.style.width = '80%';
            content.style.maxWidth = '600px';
            
            const closeBtn = document.createElement('button');
            closeBtn.textContent = '关闭';
            closeBtn.className = 'btn';
            closeBtn.onclick = () => document.body.removeChild(modal);
            
            content.appendChild(textarea);
            content.appendChild(closeBtn);
            modal.appendChild(content);
            document.body.appendChild(modal);
        }

        function exportData() {
            if (!macdData) {
                alert('请先获取MACD数据');
                return;
            }
            
            const macd = macdData.indicators.macd;
            const klines = macdData.klines;
            
            let csv = 'Date,Close,DIF,DEA,MACD,Change%\n';
            
            for (let i = 0; i < klines.length; i++) {
                const kline = klines[i];
                const dif = macd.dif[i];
                const dea = macd.dea[i];
                const macdValue = macd.macd[i];
                
                csv += `${kline.date},${kline.close},`;
                csv += `${dif !== null ? dif : ''},`;
                csv += `${dea !== null ? dea : ''},`;
                csv += `${macdValue !== null ? macdValue : ''},`;
                csv += `${kline.change_percent}\n`;
            }
            
            const blob = new Blob([csv], { type: 'text/csv' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `MACD_000001_${new Date().toISOString().split('T')[0]}.csv`;
            a.click();
            URL.revokeObjectURL(url);
        }

        // 页面加载完成后自动获取数据
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(loadMacdData, 1000);
        });
    </script>
</body>
</html>
