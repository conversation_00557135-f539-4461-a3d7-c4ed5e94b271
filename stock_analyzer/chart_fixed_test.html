<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复后图表测试</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            background: var(--bg-card);
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        .chart-container {
            height: 400px;
            background: var(--bg-tertiary);
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid var(--border-color);
        }
        .test-controls {
            display: flex;
            gap: 10px;
            margin: 15px 0;
            flex-wrap: wrap;
        }
        .test-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
        }
        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
        }
        .status.success {
            background: rgba(76, 175, 80, 0.1);
            border: 1px solid var(--color-up);
            color: var(--color-up);
        }
        .status.error {
            background: rgba(244, 67, 54, 0.1);
            border: 1px solid var(--color-down);
            color: var(--color-down);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center; color: var(--primary-color);">📊 修复后图表测试</h1>
        
        <div class="test-section">
            <h3>🔧 图表修复说明</h3>
            <div class="status success">
                ✅ 已修复LightweightCharts库加载问题<br>
                ✅ 实现了专业的Canvas备用图表<br>
                ✅ 支持真实K线数据渲染<br>
                ✅ 包含移动平均线指标<br>
                ✅ 中国股市标准配色（红涨绿跌）
            </div>
        </div>

        <div class="test-section">
            <h3>📈 图表功能测试</h3>
            <div class="test-controls">
                <input type="text" id="test-stock" placeholder="股票代码 (如: 000001)" 
                       style="padding: 10px; background: var(--bg-tertiary); border: 1px solid var(--border-color); border-radius: 6px; color: var(--text-primary);">
                <button class="test-btn" onclick="testChart()">测试图表</button>
                <button class="test-btn" onclick="testMultipleStocks()">测试多只股票</button>
            </div>
            <div id="chart-container" class="chart-container"></div>
            <div id="chart-status"></div>
        </div>

        <div class="test-section">
            <h3>📊 图表特性</h3>
            <ul style="color: var(--text-secondary);">
                <li>✅ <strong>真实K线数据</strong>：来自东方财富网API</li>
                <li>✅ <strong>专业绘制</strong>：包含开高低收、影线、实体</li>
                <li>✅ <strong>移动平均线</strong>：MA5、MA10、MA20、MA60</li>
                <li>✅ <strong>中国标准</strong>：红色上涨、绿色下跌</li>
                <li>✅ <strong>响应式设计</strong>：自适应容器宽度</li>
                <li>✅ <strong>详细信息</strong>：显示开高低收、成交量</li>
                <li>✅ <strong>网格坐标</strong>：价格和时间轴标签</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="index.html" style="color: var(--primary-color); text-decoration: none;">← 返回主应用</a>
        </div>
    </div>

    <script>
        // 简化的图表渲染类
        class SimpleStockChart {
            constructor(containerId) {
                this.container = document.getElementById(containerId);
            }

            async renderChart(stockCode) {
                try {
                    showStatus('正在获取数据...', 'info');
                    
                    // 获取K线和技术指标数据
                    const [klineResponse, indicatorResponse] = await Promise.all([
                        fetch(`http://localhost:5001/api/kline/${stockCode}?period=1d&count=30`),
                        fetch(`http://localhost:5001/api/indicators/${stockCode}?period=1d`)
                    ]);

                    const klineResult = await klineResponse.json();
                    const indicatorResult = await indicatorResponse.json();

                    if (!klineResult.success || !indicatorResult.success) {
                        throw new Error('数据获取失败');
                    }

                    showStatus(`✅ 成功获取 ${stockCode} 的数据`, 'success');
                    this.drawChart(klineResult.data, indicatorResult.data);

                } catch (error) {
                    showStatus(`❌ 图表渲染失败: ${error.message}`, 'error');
                }
            }

            drawChart(klineData, indicatorData) {
                const containerWidth = this.container.clientWidth || 800;
                const containerHeight = 400;
                
                this.container.innerHTML = `<canvas id="stock-chart" width="${containerWidth}" height="${containerHeight}" style="width: 100%; height: 100%;"></canvas>`;
                const canvas = document.getElementById('stock-chart');
                const ctx = canvas.getContext('2d');
                
                if (!klineData || !klineData.klines || klineData.klines.length === 0) {
                    this.drawNoData(ctx, canvas);
                    return;
                }
                
                this.drawProfessionalChart(ctx, canvas, klineData, indicatorData);
            }

            drawNoData(ctx, canvas) {
                ctx.fillStyle = '#1e2329';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                ctx.fillStyle = '#d1d4dc';
                ctx.font = '18px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('📊 暂无数据', canvas.width / 2, canvas.height / 2);
            }

            drawProfessionalChart(ctx, canvas, klineData, indicatorData) {
                const klines = klineData.klines;
                const padding = { top: 40, right: 80, bottom: 60, left: 60 };
                const chartWidth = canvas.width - padding.left - padding.right;
                const chartHeight = canvas.height - padding.top - padding.bottom;
                
                // 计算价格范围
                const prices = klines.map(k => [k.high, k.low, k.open, k.close]).flat();
                const maxPrice = Math.max(...prices);
                const minPrice = Math.min(...prices);
                const priceRange = maxPrice - minPrice;
                const priceBuffer = priceRange * 0.1;
                
                const adjustedMax = maxPrice + priceBuffer;
                const adjustedMin = minPrice - priceBuffer;
                const adjustedRange = adjustedMax - adjustedMin;
                
                // 背景
                ctx.fillStyle = '#1e2329';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // 网格
                this.drawGrid(ctx, padding, chartWidth, chartHeight);
                
                // K线
                this.drawKlines(ctx, klines, padding, chartWidth, chartHeight, adjustedMin, adjustedRange);
                
                // 移动平均线
                if (indicatorData.indicators && indicatorData.indicators.ma) {
                    this.drawMA(ctx, klines, indicatorData.indicators.ma, padding, chartWidth, chartHeight, adjustedMin, adjustedRange);
                }
                
                // 坐标轴
                this.drawAxis(ctx, canvas, klines, padding, chartWidth, chartHeight, adjustedMin, adjustedMax);
                
                // 图例
                this.drawLegend(ctx, klines[klines.length - 1]);
            }

            drawGrid(ctx, padding, chartWidth, chartHeight) {
                ctx.strokeStyle = '#2b3139';
                ctx.lineWidth = 1;
                
                for (let i = 0; i <= 10; i++) {
                    const x = padding.left + (chartWidth / 10) * i;
                    ctx.beginPath();
                    ctx.moveTo(x, padding.top);
                    ctx.lineTo(x, padding.top + chartHeight);
                    ctx.stroke();
                }
                
                for (let i = 0; i <= 8; i++) {
                    const y = padding.top + (chartHeight / 8) * i;
                    ctx.beginPath();
                    ctx.moveTo(padding.left, y);
                    ctx.lineTo(padding.left + chartWidth, y);
                    ctx.stroke();
                }
            }

            drawKlines(ctx, klines, padding, chartWidth, chartHeight, minPrice, priceRange) {
                const candleWidth = Math.max(3, chartWidth / klines.length * 0.7);
                
                klines.forEach((kline, index) => {
                    const x = padding.left + (chartWidth / (klines.length - 1)) * index;
                    const openY = padding.top + chartHeight - ((kline.open - minPrice) / priceRange) * chartHeight;
                    const closeY = padding.top + chartHeight - ((kline.close - minPrice) / priceRange) * chartHeight;
                    const highY = padding.top + chartHeight - ((kline.high - minPrice) / priceRange) * chartHeight;
                    const lowY = padding.top + chartHeight - ((kline.low - minPrice) / priceRange) * chartHeight;
                    
                    const isUp = kline.close >= kline.open;
                    const color = isUp ? '#ff4757' : '#2ed573';
                    
                    // 影线
                    ctx.strokeStyle = color;
                    ctx.lineWidth = 1;
                    ctx.beginPath();
                    ctx.moveTo(x, highY);
                    ctx.lineTo(x, lowY);
                    ctx.stroke();
                    
                    // 实体
                    ctx.fillStyle = color;
                    const bodyTop = Math.min(openY, closeY);
                    const bodyHeight = Math.max(1, Math.abs(closeY - openY));
                    ctx.fillRect(x - candleWidth/2, bodyTop, candleWidth, bodyHeight);
                });
            }

            drawMA(ctx, klines, maData, padding, chartWidth, chartHeight, minPrice, priceRange) {
                const colors = { ma5: '#ff9800', ma10: '#9c27b0', ma20: '#2196f3', ma60: '#4caf50' };
                
                Object.entries(maData).forEach(([period, values]) => {
                    if (!values) return;
                    
                    ctx.strokeStyle = colors[period] || '#ffffff';
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    
                    let started = false;
                    values.slice(-klines.length).forEach((value, index) => {
                        if (value !== null) {
                            const x = padding.left + (chartWidth / (klines.length - 1)) * index;
                            const y = padding.top + chartHeight - ((value - minPrice) / priceRange) * chartHeight;
                            
                            if (!started) {
                                ctx.moveTo(x, y);
                                started = true;
                            } else {
                                ctx.lineTo(x, y);
                            }
                        }
                    });
                    
                    ctx.stroke();
                });
            }

            drawAxis(ctx, canvas, klines, padding, chartWidth, chartHeight, minPrice, maxPrice) {
                ctx.fillStyle = '#d1d4dc';
                ctx.font = '11px Arial';
                ctx.textAlign = 'right';
                
                // 价格标签
                for (let i = 0; i <= 8; i++) {
                    const price = minPrice + (maxPrice - minPrice) * (1 - i / 8);
                    const y = padding.top + (chartHeight / 8) * i;
                    ctx.fillText(price.toFixed(2), padding.left - 10, y + 4);
                }
                
                // 日期标签
                ctx.textAlign = 'center';
                const step = Math.max(1, Math.floor(klines.length / 6));
                for (let i = 0; i < klines.length; i += step) {
                    const kline = klines[i];
                    const x = padding.left + (chartWidth / (klines.length - 1)) * i;
                    const date = new Date(kline.date);
                    ctx.fillText(`${date.getMonth() + 1}/${date.getDate()}`, x, canvas.height - 20);
                }
            }

            drawLegend(ctx, latestKline) {
                ctx.fillStyle = '#d1d4dc';
                ctx.font = '12px Arial';
                ctx.textAlign = 'left';
                
                const y = 20;
                ctx.fillText(`开: ${latestKline.open}`, 20, y);
                ctx.fillText(`高: ${latestKline.high}`, 100, y);
                ctx.fillText(`低: ${latestKline.low}`, 180, y);
                ctx.fillText(`收: ${latestKline.close}`, 260, y);
            }
        }

        const chart = new SimpleStockChart('chart-container');

        function showStatus(message, type = 'info') {
            const statusEl = document.getElementById('chart-status');
            statusEl.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        async function testChart() {
            const stockCode = document.getElementById('test-stock').value || '000001';
            await chart.renderChart(stockCode);
        }

        async function testMultipleStocks() {
            const stocks = ['000001', '600519', '002594'];
            for (const stock of stocks) {
                showStatus(`正在测试 ${stock}...`, 'info');
                await chart.renderChart(stock);
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
            showStatus('✅ 多股票测试完成', 'success');
        }

        // 页面加载时自动测试
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => testChart(), 1000);
        });
    </script>
</body>
</html>
