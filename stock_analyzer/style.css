/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* 主色调 */
    --primary-color: #1a73e8;
    --secondary-color: #34a853;
    --danger-color: #ea4335;
    --warning-color: #fbbc04;
    
    /* 背景色 */
    --bg-primary: #0f1419;
    --bg-secondary: #1a1f2e;
    --bg-tertiary: #252d3d;
    --bg-card: #1e2329;
    
    /* 文字颜色 */
    --text-primary: #ffffff;
    --text-secondary: #b7bdc6;
    --text-muted: #848e9c;
    
    /* 边框颜色 */
    --border-color: #2b3139;
    --border-hover: #3c434d;
    
    /* 涨跌颜色 - 中国股市标准：红涨绿跌 */
    --color-up: #ff4757;     /* 红色表示上涨 */
    --color-down: #2ed573;   /* 绿色表示下跌 */
    --color-neutral: #848e9c;

    /* 专业图表颜色 */
    --chart-bg: #0f1419;
    --chart-grid: #1e2329;
    --chart-text: #d1d4dc;
    --ma5-color: #ff9800;    /* 橙色 MA5 */
    --ma10-color: #9c27b0;   /* 紫色 MA10 */
    --ma20-color: #2196f3;   /* 蓝色 MA20 */
    --ma30-color: #4caf50;   /* 绿色 MA30 */
    --ma60-color: #f44336;   /* 红色 MA60 */
    --ma120-color: #795548;  /* 棕色 MA120 */
    --ma250-color: #607d8b;  /* 蓝灰色 MA250 */
    
    /* 阴影 */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.2);
    
    /* 字体 */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

body {
    font-family: var(--font-family);
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
    min-height: 100vh; /* 最小高度100vh，但可以超出 */
}

.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 顶部导航栏 */
.header {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    padding: 0 24px;
    position: sticky;
    top: 0;
    z-index: 100;
    backdrop-filter: blur(10px);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 64px;
    max-width: 1400px;
    margin: 0 auto;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
}

.logo i {
    color: var(--primary-color);
    font-size: 24px;
}

.market-status {
    display: flex;
    gap: 32px;
}

.status-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.status-item .label {
    font-size: 12px;
    color: var(--text-muted);
}

.status-item .value {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.status-item .change {
    font-size: 12px;
    font-weight: 500;
    padding: 2px 6px;
    border-radius: 4px;
}

.change.positive {
    color: var(--color-up);
    background: rgba(0, 212, 170, 0.1);
}

.change.negative {
    color: var(--color-down);
    background: rgba(248, 73, 96, 0.1);
}

.header-actions {
    display: flex;
    gap: 12px;
}

.btn-icon {
    width: 40px;
    height: 40px;
    border: none;
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-icon:hover {
    background: var(--border-hover);
    color: var(--text-primary);
}

/* 主要内容区域 */
.main-content {
    flex: 1;
    padding: 24px;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

/* 搜索区域 */
.search-section {
    margin-bottom: 32px;
}

.search-container {
    background: var(--bg-card);
    border-radius: 12px;
    padding: 24px;
    border: 1px solid var(--border-color);
}

.search-box {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
}

.search-box i {
    color: var(--text-muted);
    font-size: 18px;
}

.search-box input {
    flex: 1;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 12px 16px;
    color: var(--text-primary);
    font-size: 16px;
    transition: border-color 0.2s ease;
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.search-box input::placeholder {
    color: var(--text-muted);
}

.search-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.search-btn:hover {
    background: #1557b0;
}

.quick-search {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.quick-label {
    color: var(--text-muted);
    font-size: 14px;
}

.quick-btn {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.quick-btn:hover {
    background: var(--border-hover);
    color: var(--text-primary);
    border-color: var(--border-hover);
}

/* 股票信息面板 */
.stock-panel {
    background: var(--bg-card);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    margin-bottom: 32px;
    overflow: hidden;
}

.stock-header {
    padding: 24px;
    border-bottom: 1px solid var(--border-color);
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 24px;
    align-items: center;
}

.stock-basic-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.stock-name {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-primary);
}

.stock-code {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
}

.stock-market {
    background: var(--primary-color);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.stock-price-info {
    text-align: center;
}

.current-price {
    font-size: 36px;
    font-weight: 700;
    color: var(--color-up);
    line-height: 1;
}

.price-change {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-top: 8px;
}

.change-amount,
.change-percent {
    font-size: 16px;
    font-weight: 600;
    color: var(--color-up);
}

.stock-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.stat-label {
    color: var(--text-muted);
    font-size: 14px;
}

.stat-value {
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 500;
}

/* 分析容器 */
.analysis-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 24px;
    padding: 24px;
}

/* 专业版紧凑布局 */
.chart-period-controls {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
}

.chart-period-controls .period-btn {
    padding: 4px 8px;
    font-size: 11px;
    min-width: 35px;
    text-align: center;
}

/* 均线按钮颜色增强 */
.ma-btn.active[data-ma="5"] {
    border-color: var(--ma5-color);
    box-shadow: 0 0 0 1px var(--ma5-color);
}

.ma-btn.active[data-ma="10"] {
    border-color: var(--ma10-color);
    box-shadow: 0 0 0 1px var(--ma10-color);
}

.ma-btn.active[data-ma="20"] {
    border-color: var(--ma20-color);
    box-shadow: 0 0 0 1px var(--ma20-color);
}

.ma-btn.active[data-ma="30"] {
    border-color: var(--ma30-color);
    box-shadow: 0 0 0 1px var(--ma30-color);
}

.ma-btn.active[data-ma="60"] {
    border-color: var(--ma60-color);
    box-shadow: 0 0 0 1px var(--ma60-color);
}

.ma-btn.active[data-ma="120"] {
    border-color: var(--ma120-color);
    box-shadow: 0 0 0 1px var(--ma120-color);
}

.ma-btn.active[data-ma="250"] {
    border-color: var(--ma250-color);
    box-shadow: 0 0 0 1px var(--ma250-color);
}

.chart-section,
.analysis-section {
    background: var(--bg-tertiary);
    border-radius: 8px;
    padding: 20px;
}

.chart-header,
.analysis-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.chart-header h3,
.analysis-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

.chart-controls {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.chart-type-controls,
.chart-period-controls {
    display: flex;
    gap: 8px;
}

.chart-type-btn,
.indicator-btn,
.ma-btn,
.period-btn {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.ma-btn {
    padding: 4px 8px;
    font-size: 11px;
    min-width: 40px;
}

.chart-type-btn.active,
.chart-type-btn:hover,
.indicator-btn.active,
.indicator-btn:hover,
.ma-btn.active,
.ma-btn:hover,
.period-btn.active,
.period-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.ma-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-bottom: 15px;
}

.indicator-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.chart-btn {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.chart-btn.active,
.chart-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.chart-container {
    height: 400px;
    position: relative;
    background: var(--bg-card);
    border-radius: 8px;
    margin-bottom: 16px;
}

.indicator-controls {
    margin-top: 16px;
}

.indicator-controls h4 {
    font-size: 14px;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.indicator-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.analysis-score {
    display: flex;
    align-items: center;
    gap: 8px;
    background: var(--bg-card);
    padding: 8px 16px;
    border-radius: 8px;
}

.score-label {
    color: var(--text-muted);
    font-size: 12px;
}

.score-value {
    color: var(--color-up);
    font-size: 20px;
    font-weight: 700;
}

.score-max {
    color: var(--text-muted);
    font-size: 14px;
}

.analysis-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.analysis-item {
    background: var(--bg-card);
    border-radius: 8px;
    padding: 16px;
}

.analysis-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.analysis-title i {
    color: var(--primary-color);
}

.analysis-title span:first-of-type {
    font-weight: 500;
    color: var(--text-primary);
}

.analysis-status {
    margin-left: auto;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.analysis-status.positive {
    background: rgba(0, 212, 170, 0.1);
    color: var(--color-up);
}

.analysis-status.negative {
    background: rgba(248, 73, 96, 0.1);
    color: var(--color-down);
}

.analysis-status.neutral {
    background: rgba(132, 142, 156, 0.1);
    color: var(--color-neutral);
}

.analysis-desc {
    color: var(--text-secondary);
    font-size: 14px;
    line-height: 1.5;
}

/* 实时数据流 */
.realtime-section {
    padding: 24px;
    border-top: 1px solid var(--border-color);
}

.realtime-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.realtime-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

.realtime-status {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-secondary);
    font-size: 14px;
}

.status-dot {
    width: 8px;
    height: 8px;
    background: var(--color-up);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.data-stream {
    background: var(--bg-tertiary);
    border-radius: 8px;
    padding: 16px;
    height: 200px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
}

.data-stream::-webkit-scrollbar {
    width: 6px;
}

.data-stream::-webkit-scrollbar-track {
    background: var(--bg-card);
    border-radius: 3px;
}

.data-stream::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

.data-stream::-webkit-scrollbar-thumb:hover {
    background: var(--border-hover);
}

/* 热门股票列表 */
.hot-stocks-section {
    background: var(--bg-card);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px;
    border-bottom: 1px solid var(--border-color);
}

.section-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

.section-tabs {
    display: flex;
    gap: 8px;
}

.tab-btn {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.tab-btn.active,
.tab-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.stocks-list {
    padding: 0;
}

.stock-item {
    display: grid;
    grid-template-columns: 1fr auto auto auto;
    align-items: center;
    padding: 16px 24px;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.stock-item:hover {
    background: var(--bg-tertiary);
}

.stock-item:last-child {
    border-bottom: none;
}

.stock-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.stock-info .name {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-primary);
}

.stock-info .code {
    font-size: 12px;
    color: var(--text-muted);
}

.stock-price {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.stock-change {
    text-align: right;
}

.stock-change .amount {
    font-size: 14px;
    font-weight: 500;
}

.stock-change .percent {
    font-size: 12px;
    margin-top: 2px;
}

.stock-volume {
    font-size: 14px;
    color: var(--text-muted);
    text-align: right;
}

/* 加载指示器 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(15, 20, 25, 0.8);
    display: none; /* 默认隐藏 */
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(4px);
}

.loading-overlay.show {
    display: flex; /* 显示时使用flex布局 */
}

.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    color: var(--text-primary);
}

.loading-spinner i {
    font-size: 32px;
    color: var(--primary-color);
}

.loading-spinner span {
    font-size: 16px;
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .analysis-container {
        grid-template-columns: 1fr;
    }

    .market-status {
        gap: 16px;
    }

    .status-item .value {
        font-size: 14px;
    }
}

@media (max-width: 768px) {
    .header-content {
        padding: 0 16px;
    }

    .market-status {
        display: none;
    }

    .main-content {
        padding: 16px;
    }

    .stock-header {
        grid-template-columns: 1fr;
        gap: 16px;
        text-align: center;
    }

    .stock-stats {
        grid-template-columns: 1fr;
    }

    .search-box {
        flex-direction: column;
        align-items: stretch;
    }

    .quick-search {
        justify-content: center;
    }

    .stock-item {
        grid-template-columns: 1fr auto;
        gap: 16px;
    }

    .stock-volume {
        display: none;
    }
}

@media (max-width: 480px) {
    .header-content {
        height: 56px;
    }

    .logo span {
        display: none;
    }

    .search-container {
        padding: 16px;
    }

    .stock-header {
        padding: 16px;
    }

    .analysis-container {
        padding: 16px;
    }

    .section-header {
        padding: 16px;
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }

    .section-tabs {
        justify-content: center;
    }
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--border-hover);
}

/* 专业版右侧面板样式 */
.right-panel {
    width: 320px;
    background: #1e2329;
    border-left: 1px solid #2b3139;
    padding: 15px;
    overflow-y: auto;
    font-size: 12px;
}

.stock-info {
    margin-bottom: 20px;
}

.stock-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.stock-name {
    font-size: 16px;
    font-weight: bold;
    color: #d1d4dc;
}

.stock-code {
    font-size: 14px;
    color: #848e9c;
}

.stock-price {
    text-align: right;
}

.current-price {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 5px;
}

.price-change {
    font-size: 14px;
}

.stock-details {
    background: #2b3139;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 15px;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 6px;
    font-size: 11px;
}

.detail-row:last-child {
    margin-bottom: 0;
}

.detail-row .label {
    color: #848e9c;
    width: 50px;
}

.detail-row .value {
    color: #d1d4dc;
    font-weight: 500;
    width: 60px;
    text-align: right;
}

/* 分时数据表格样式 */
.time-data-section {
    margin-bottom: 15px;
}

.section-header {
    margin-bottom: 8px;
    padding-bottom: 5px;
    border-bottom: 1px solid #2b3139;
}

.section-header h4 {
    color: #d1d4dc;
    font-size: 13px;
    font-weight: bold;
    margin: 0;
}

.time-data-table {
    background: #2b3139;
    border-radius: 4px;
    overflow: hidden;
}

.table-header {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    background: #3c434c;
    padding: 8px 5px;
    font-size: 10px;
    color: #848e9c;
    font-weight: bold;
}

.table-header span {
    text-align: center;
}

.table-body {
    max-height: 120px;
    overflow-y: auto;
}

.time-data-row {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    padding: 4px 5px;
    font-size: 10px;
    border-bottom: 1px solid #1e2329;
}

.time-data-row:last-child {
    border-bottom: none;
}

.time-data-row span {
    text-align: center;
    color: #d1d4dc;
}

.time-data-row .price-up {
    color: #ff4757;
}

.time-data-row .price-down {
    color: #2ed573;
}

/* 资金流向样式 */
.fund-flow-section {
    margin-bottom: 15px;
}

.fund-flow-data {
    background: #2b3139;
    border-radius: 4px;
    padding: 10px;
}

.flow-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
    font-size: 11px;
}

.flow-item:last-child {
    margin-bottom: 0;
}

.flow-label {
    color: #848e9c;
}

.flow-value {
    color: #d1d4dc;
    font-weight: 500;
}

.flow-value.positive {
    color: #ff4757;
}

.flow-value.negative {
    color: #2ed573;
}

/* 专业版布局优化 */
.main-chart-area {
    display: flex;
    flex: 1;
    overflow: hidden;
    height: calc(100vh - 200px);
}

.chart-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--chart-bg);
    margin-right: 10px;
}

.main-chart {
    flex: 1.5;
    position: relative;
    min-height: 300px;
    max-height: 400px;
    background: #0f1419;
    border-radius: 8px;
    margin-bottom: 12px;
}

.sub-charts {
    /* 移除高度限制，让内容自然展开 */
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 8px;
    background: #1a1f29;
}

.sub-chart {
    background: #0f1419;
    border-radius: 6px;
    flex: none; /* 使用固定高度，不平分 */
    display: flex;
    flex-direction: column;
    border: 1px solid #2b3139;
    min-height: 160px; /* 设置最小高度确保内容可见 */
}

.sub-chart-header {
    padding: 8px 12px;
    background: #2b3139;
    border-bottom: 1px solid #3c434c;
    font-size: 12px;
    font-weight: bold;
    color: #d1d4dc;
    border-radius: 8px 8px 0 0;
}

.sub-chart-content {
    flex: 1;
    position: relative;
}

.info-panel {
    width: 320px;
    background: #1e2329;
    border-left: 1px solid #2b3139;
    padding: 15px;
    overflow-y: auto;
    font-size: 12px;
}

/* 底部工具栏 */
.bottom-toolbar {
    background: #1e2329;
    border-top: 1px solid #2b3139;
    padding: 10px 20px;
    display: flex;
    justify-content: center;
}

.indicator-controls {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.indicator-btn {
    background: #2b3139;
    border: 1px solid #3c434c;
    color: #d1d4dc;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.indicator-btn:hover {
    background: #3c434c;
    border-color: #4a5568;
}

.indicator-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* 图表占位符 */
.chart-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #848e9c;
}

.placeholder-content {
    text-align: center;
}

.placeholder-content i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.placeholder-content p {
    font-size: 16px;
    opacity: 0.7;
}
