/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    line-height: 1.6;
}

.container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 1.5rem;
    font-weight: bold;
    color: #4f46e5;
}

.nav {
    display: flex;
    gap: 1rem;
}

.nav-btn {
    padding: 0.5rem 1rem;
    border: none;
    background: transparent;
    color: #666;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.nav-btn:hover,
.nav-btn.active {
    background: #4f46e5;
    color: white;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 0.9rem;
}

.market-status {
    padding: 0.25rem 0.5rem;
    background: #10b981;
    color: white;
    border-radius: 4px;
    font-size: 0.8rem;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
    width: 100%;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 卡片样式 */
.card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
}

/* 仪表盘网格 */
.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    gap: 1.5rem;
    grid-template-rows: auto 1fr;
}

.market-overview {
    grid-column: 1 / -1;
}

.chart-container {
    min-height: 500px;
}

.hot-stocks {
    max-height: 500px;
    overflow-y: auto;
}

/* 市场概览 */
.market-indices {
    display: flex;
    justify-content: space-around;
    margin-top: 1rem;
}

.index-item {
    text-align: center;
    padding: 1rem;
    border-radius: 8px;
    background: #f8fafc;
}

.index-name {
    display: block;
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.index-value {
    display: block;
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 0.25rem;
}

.index-change {
    font-size: 0.9rem;
    font-weight: 500;
}

.positive {
    color: #ef4444;
}

.negative {
    color: #10b981;
}

/* 图表头部 */
.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.stock-selector {
    display: flex;
    gap: 0.5rem;
}

.stock-selector input {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    width: 200px;
}

.stock-selector button {
    padding: 0.5rem 1rem;
    background: #4f46e5;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

/* TradingView图表容器 */
#tradingview_chart,
#analysis_chart {
    height: 400px;
    width: 100%;
}

/* 筛选器样式 */
.screener-container {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 1.5rem;
}

.filter-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 500;
    color: #374151;
}

.filter-group select {
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background: white;
}

.filter-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* 按钮样式 */
.btn-primary {
    background: #4f46e5;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: background 0.3s ease;
}

.btn-primary:hover {
    background: #4338ca;
}

.btn-secondary {
    background: #6b7280;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
}

.btn-success {
    background: #10b981;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
}

.btn-export {
    background: #f59e0b;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
}

/* 结果表格 */
.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.results-stats {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.results-table-container {
    overflow-x: auto;
}

.results-table,
.watchlist-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.results-table th,
.results-table td,
.watchlist-table th,
.watchlist-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
}

.results-table th,
.watchlist-table th {
    background: #f9fafb;
    font-weight: 600;
    color: #374151;
}

.results-table tr:hover,
.watchlist-table tr:hover {
    background: #f9fafb;
}

/* 股票列表 */
.stock-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.stock-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: #f8fafc;
    border-radius: 6px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.stock-item:hover {
    background: #e2e8f0;
}

.stock-info {
    display: flex;
    flex-direction: column;
}

.stock-code {
    font-weight: 600;
    color: #1f2937;
}

.stock-name {
    font-size: 0.9rem;
    color: #6b7280;
}

.stock-price {
    text-align: right;
}

.stock-value {
    font-weight: 600;
    font-size: 1.1rem;
}

.stock-change {
    font-size: 0.9rem;
}

/* 技术分析工具 */
.tool-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.tool-btn {
    padding: 1rem;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.tool-btn:hover {
    background: #4f46e5;
    color: white;
    border-color: #4f46e5;
}

/* 自选股 */
.watchlist-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* 模态框 */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 2rem;
    border-radius: 12px;
    width: 80%;
    max-width: 600px;
    position: relative;
}

.close {
    position: absolute;
    right: 1rem;
    top: 1rem;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
}

.close:hover {
    color: #333;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .screener-container {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .main-content {
        padding: 1rem;
    }
    
    .filter-grid {
        grid-template-columns: 1fr;
    }
    
    .filter-actions {
        justify-content: center;
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #4f46e5;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 评分样式 */
.score-high {
    color: #ef4444;
    font-weight: bold;
}

.score-medium {
    color: #f59e0b;
    font-weight: bold;
}

.score-low {
    color: #10b981;
    font-weight: bold;
}
