<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>A股智能交易分析平台</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://s3.tradingview.com/tv.js"></script>
</head>
<body>
    <div class="container">
        <!-- 顶部导航栏 -->
        <header class="header">
            <div class="header-content">
                <h1 class="logo">🚀 A股智能交易平台</h1>
                <nav class="nav">
                    <button class="nav-btn active" data-tab="dashboard">仪表盘</button>
                    <button class="nav-btn" data-tab="screener">智能选股</button>
                    <button class="nav-btn" data-tab="analysis">技术分析</button>
                    <button class="nav-btn" data-tab="watchlist">自选股</button>
                </nav>
                <div class="user-info">
                    <span class="time" id="currentTime"></span>
                    <span class="market-status" id="marketStatus">开盘中</span>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 仪表盘页面 -->
            <div class="tab-content active" id="dashboard">
                <div class="dashboard-grid">
                    <!-- 市场概览 -->
                    <div class="card market-overview">
                        <h3>市场概览</h3>
                        <div class="market-indices">
                            <div class="index-item">
                                <span class="index-name">上证指数</span>
                                <span class="index-value" id="sh-index">3000.00</span>
                                <span class="index-change positive" id="sh-change">+1.2%</span>
                            </div>
                            <div class="index-item">
                                <span class="index-name">深证成指</span>
                                <span class="index-value" id="sz-index">10000.00</span>
                                <span class="index-change negative" id="sz-change">-0.8%</span>
                            </div>
                            <div class="index-item">
                                <span class="index-name">创业板指</span>
                                <span class="index-value" id="cy-index">2000.00</span>
                                <span class="index-change positive" id="cy-change">+2.1%</span>
                            </div>
                        </div>
                    </div>

                    <!-- TradingView图表 -->
                    <div class="card chart-container">
                        <div class="chart-header">
                            <h3>实时K线图</h3>
                            <div class="stock-selector">
                                <input type="text" id="stockInput" placeholder="输入股票代码或名称" />
                                <button id="searchStock">搜索</button>
                            </div>
                        </div>
                        <div id="tradingview_chart"></div>
                    </div>

                    <!-- 热门股票 -->
                    <div class="card hot-stocks">
                        <h3>今日热门</h3>
                        <div class="stock-list" id="hotStocks">
                            <!-- 动态加载热门股票 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 智能选股页面 -->
            <div class="tab-content" id="screener">
                <div class="screener-container">
                    <div class="card screener-filters">
                        <h3>🎯 智能筛选条件</h3>
                        <div class="filter-grid">
                            <div class="filter-group">
                                <label>市值范围</label>
                                <select id="marketCapFilter">
                                    <option value="">不限</option>
                                    <option value="large">大盘股(>500亿)</option>
                                    <option value="mid">中盘股(100-500亿)</option>
                                    <option value="small">小盘股(<100亿)</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label>涨跌幅</label>
                                <select id="changeFilter">
                                    <option value="">不限</option>
                                    <option value="up5">涨幅>5%</option>
                                    <option value="up3">涨幅>3%</option>
                                    <option value="down3">跌幅>3%</option>
                                    <option value="down5">跌幅>5%</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label>成交量</label>
                                <select id="volumeFilter">
                                    <option value="">不限</option>
                                    <option value="high">放量(>平均2倍)</option>
                                    <option value="low">缩量(<平均0.5倍)</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label>技术指标</label>
                                <select id="technicalFilter">
                                    <option value="">不限</option>
                                    <option value="macd_golden">MACD金叉</option>
                                    <option value="kdj_oversold">KDJ超卖</option>
                                    <option value="rsi_strong">RSI强势</option>
                                    <option value="ma_breakthrough">突破均线</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label>行业板块</label>
                                <select id="sectorFilter">
                                    <option value="">不限</option>
                                    <option value="tech">科技股</option>
                                    <option value="finance">金融股</option>
                                    <option value="healthcare">医药股</option>
                                    <option value="consumer">消费股</option>
                                    <option value="energy">能源股</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label>智能策略</label>
                                <select id="strategyFilter">
                                    <option value="">自定义</option>
                                    <option value="momentum">动量策略</option>
                                    <option value="value">价值投资</option>
                                    <option value="growth">成长股</option>
                                    <option value="reversal">反转策略</option>
                                </select>
                            </div>
                        </div>
                        <div class="filter-actions">
                            <button id="applyFilters" class="btn-primary">🔍 开始筛选</button>
                            <button id="resetFilters" class="btn-secondary">🔄 重置条件</button>
                            <button id="saveStrategy" class="btn-success">💾 保存策略</button>
                        </div>
                    </div>

                    <div class="card screener-results">
                        <div class="results-header">
                            <h3>📊 筛选结果</h3>
                            <div class="results-stats">
                                <span id="resultsCount">找到 0 只股票</span>
                                <button id="exportResults" class="btn-export">📤 导出</button>
                            </div>
                        </div>
                        <div class="results-table-container">
                            <table class="results-table" id="resultsTable">
                                <thead>
                                    <tr>
                                        <th>股票代码</th>
                                        <th>股票名称</th>
                                        <th>现价</th>
                                        <th>涨跌幅</th>
                                        <th>成交量</th>
                                        <th>市值</th>
                                        <th>技术评分</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="resultsBody">
                                    <!-- 动态加载筛选结果 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 技术分析页面 -->
            <div class="tab-content" id="analysis">
                <div class="analysis-container">
                    <div class="card analysis-tools">
                        <h3>📈 技术分析工具</h3>
                        <div class="tool-grid">
                            <button class="tool-btn" data-tool="trend">趋势分析</button>
                            <button class="tool-btn" data-tool="support">支撑阻力</button>
                            <button class="tool-btn" data-tool="pattern">形态识别</button>
                            <button class="tool-btn" data-tool="indicator">技术指标</button>
                        </div>
                    </div>
                    <div class="card analysis-chart">
                        <div id="analysis_chart"></div>
                    </div>
                </div>
            </div>

            <!-- 自选股页面 -->
            <div class="tab-content" id="watchlist">
                <div class="watchlist-container">
                    <div class="card watchlist-header">
                        <h3>⭐ 我的自选股</h3>
                        <button id="addToWatchlist" class="btn-primary">+ 添加股票</button>
                    </div>
                    <div class="card watchlist-content">
                        <div class="watchlist-table-container">
                            <table class="watchlist-table" id="watchlistTable">
                                <thead>
                                    <tr>
                                        <th>股票代码</th>
                                        <th>股票名称</th>
                                        <th>现价</th>
                                        <th>涨跌</th>
                                        <th>涨跌幅</th>
                                        <th>成交量</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="watchlistBody">
                                    <!-- 动态加载自选股 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 模态框 -->
    <div id="stockModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3>股票详情</h3>
            <div id="modalContent"></div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
