#!/bin/bash

echo "🚀 A股智能交易分析平台启动脚本"
echo "=================================="

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装，请先安装Python3"
    exit 1
fi

# 检查pip
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 未安装，请先安装pip3"
    exit 1
fi

echo "📦 检查并安装依赖包..."

# 创建虚拟环境（可选）
if [ ! -d "venv" ]; then
    echo "🔧 创建虚拟环境..."
    python3 -m venv venv
fi

# 激活虚拟环境
echo "🔧 激活虚拟环境..."
source venv/bin/activate

# 安装依赖
echo "📥 安装Python依赖包..."
pip install -r requirements.txt

# 检查TA-Lib安装
echo "🔍 检查TA-Lib安装..."
python3 -c "import talib" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "⚠️  TA-Lib未正确安装，尝试安装..."
    
    # macOS安装方法
    if [[ "$OSTYPE" == "darwin"* ]]; then
        if command -v brew &> /dev/null; then
            echo "🍺 使用Homebrew安装TA-Lib..."
            brew install ta-lib
            pip install TA-Lib
        else
            echo "❌ 请先安装Homebrew，然后运行: brew install ta-lib"
            echo "   或访问: https://github.com/mrjbq7/ta-lib#installation"
        fi
    # Linux安装方法
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        echo "🐧 Linux系统，请手动安装TA-Lib:"
        echo "   sudo apt-get install libta-lib-dev  # Ubuntu/Debian"
        echo "   sudo yum install ta-lib-devel       # CentOS/RHEL"
        echo "   然后运行: pip install TA-Lib"
    fi
fi

echo ""
echo "🌐 启动Web服务器..."
echo "📊 前端地址: http://localhost:5000"
echo "🔌 API地址: http://localhost:5000/api"
echo ""
echo "💡 使用说明:"
echo "   • 在浏览器中打开 http://localhost:5000"
echo "   • 使用智能选股功能筛选牛股"
echo "   • 查看TradingView技术分析图表"
echo "   • 管理自选股列表"
echo ""
echo "⚠️  注意事项:"
echo "   • 首次运行可能需要下载股票数据，请耐心等待"
echo "   • 建议在交易时间使用以获得最新数据"
echo "   • 技术分析仅供参考，投资有风险"
echo ""

# 启动Flask应用
python3 api.py
