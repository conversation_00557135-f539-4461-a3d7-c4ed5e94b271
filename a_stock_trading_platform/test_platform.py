#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A股智能交易分析平台 - 功能测试脚本
"""

import requests
import json
import time
from datetime import datetime

class PlatformTester:
    def __init__(self, base_url="http://localhost:8080"):
        self.base_url = base_url
        self.session = requests.Session()
        
    def test_server_status(self):
        """测试服务器状态"""
        print("🔍 测试服务器连接...")
        try:
            response = self.session.get(f"{self.base_url}/")
            if response.status_code == 200:
                print("✅ 服务器连接正常")
                return True
            else:
                print(f"❌ 服务器响应异常: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 服务器连接失败: {e}")
            return False
    
    def test_market_indices(self):
        """测试市场指数API"""
        print("\n📊 测试市场指数API...")
        try:
            response = self.session.get(f"{self.base_url}/api/market/indices")
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    indices = data.get('data', {})
                    print("✅ 市场指数获取成功:")
                    for key, index in indices.items():
                        print(f"   {index['name']}: {index['value']:.2f} ({index['change_pct']:+.2f}%)")
                    return True
                else:
                    print(f"❌ API返回错误: {data.get('error')}")
                    return False
            else:
                print(f"❌ API请求失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 市场指数测试失败: {e}")
            return False
    
    def test_stock_info(self, symbol="000001"):
        """测试股票信息API"""
        print(f"\n📈 测试股票信息API (股票代码: {symbol})...")
        try:
            response = self.session.get(f"{self.base_url}/api/stock/info/{symbol}")
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    stock = data.get('data', {})
                    print("✅ 股票信息获取成功:")
                    print(f"   股票名称: {stock.get('name')}")
                    print(f"   当前价格: ¥{stock.get('price', 0):.2f}")
                    print(f"   涨跌幅: {stock.get('change_pct', 0):+.2f}%")
                    print(f"   技术评分: {stock.get('technical_score', 0):.1f}")
                    return True
                else:
                    print(f"❌ API返回错误: {data.get('error')}")
                    return False
            else:
                print(f"❌ API请求失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 股票信息测试失败: {e}")
            return False
    
    def test_stock_screening(self):
        """测试股票筛选API"""
        print("\n🎯 测试智能选股API...")
        try:
            # 测试动量策略筛选
            filters = {
                "strategy": "momentum",
                "change": "up3",
                "volume": "high",
                "technical": "macd_golden"
            }
            
            response = self.session.post(
                f"{self.base_url}/api/stock/screen",
                json=filters,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    stocks = data.get('data', [])
                    print(f"✅ 股票筛选成功，找到 {len(stocks)} 只股票:")
                    for i, stock in enumerate(stocks[:5]):  # 只显示前5只
                        print(f"   {i+1}. {stock['code']} {stock['name']} "
                              f"¥{stock['price']:.2f} ({stock['change_pct']:+.2f}%) "
                              f"评分:{stock['technical_score']:.1f}")
                    return True
                else:
                    print(f"❌ API返回错误: {data.get('error')}")
                    return False
            else:
                print(f"❌ API请求失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 股票筛选测试失败: {e}")
            return False
    
    def test_hot_stocks(self):
        """测试热门股票API"""
        print("\n🔥 测试热门股票API...")
        try:
            response = self.session.get(f"{self.base_url}/api/stock/hot")
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    stocks = data.get('data', [])
                    print(f"✅ 热门股票获取成功，共 {len(stocks)} 只:")
                    for i, stock in enumerate(stocks[:5]):  # 只显示前5只
                        print(f"   {i+1}. {stock['code']} {stock['name']} "
                              f"¥{stock['price']:.2f} ({stock['change_pct']:+.2f}%)")
                    return True
                else:
                    print(f"❌ API返回错误: {data.get('error')}")
                    return False
            else:
                print(f"❌ API请求失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 热门股票测试失败: {e}")
            return False
    
    def test_performance(self):
        """测试API性能"""
        print("\n⚡ 测试API响应性能...")
        
        test_cases = [
            ("市场指数", f"{self.base_url}/api/market/indices"),
            ("股票信息", f"{self.base_url}/api/stock/info/000001"),
            ("热门股票", f"{self.base_url}/api/stock/hot")
        ]
        
        for name, url in test_cases:
            try:
                start_time = time.time()
                response = self.session.get(url)
                end_time = time.time()
                
                response_time = (end_time - start_time) * 1000  # 转换为毫秒
                
                if response.status_code == 200:
                    print(f"   {name}: {response_time:.0f}ms ✅")
                else:
                    print(f"   {name}: {response_time:.0f}ms ❌ (状态码: {response.status_code})")
                    
            except Exception as e:
                print(f"   {name}: 测试失败 ❌ ({e})")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 A股智能交易分析平台 - 功能测试")
        print("=" * 50)
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"测试地址: {self.base_url}")
        print("=" * 50)
        
        test_results = []
        
        # 基础连接测试
        test_results.append(("服务器连接", self.test_server_status()))
        
        # API功能测试
        test_results.append(("市场指数", self.test_market_indices()))
        test_results.append(("股票信息", self.test_stock_info()))
        test_results.append(("智能选股", self.test_stock_screening()))
        test_results.append(("热门股票", self.test_hot_stocks()))
        
        # 性能测试
        self.test_performance()
        
        # 测试结果汇总
        print("\n" + "=" * 50)
        print("📋 测试结果汇总:")
        print("=" * 50)
        
        passed = 0
        total = len(test_results)
        
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n总计: {passed}/{total} 项测试通过")
        
        if passed == total:
            print("🎉 所有测试通过！平台运行正常。")
        else:
            print("⚠️  部分测试失败，请检查相关功能。")
        
        print("\n💡 使用提示:")
        print("   • 在浏览器中访问: http://localhost:5000")
        print("   • 使用智能选股功能筛选牛股")
        print("   • 查看TradingView技术分析图表")
        print("   • 管理自选股列表")
        
        return passed == total

def main():
    """主函数"""
    tester = PlatformTester()
    
    # 等待用户确认服务器已启动
    print("请确保平台服务器已启动 (运行 python api.py 或 ./start.sh)")
    input("按回车键开始测试...")
    
    # 运行测试
    success = tester.run_all_tests()
    
    if not success:
        print("\n🔧 故障排除建议:")
        print("   1. 检查Python依赖是否正确安装")
        print("   2. 确认网络连接正常")
        print("   3. 检查防火墙设置")
        print("   4. 查看服务器日志输出")

if __name__ == "__main__":
    main()
