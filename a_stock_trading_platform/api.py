#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A股智能交易分析平台 - 后端API服务
提供股票数据获取、智能筛选、技术分析等功能
"""

from flask import Flask, jsonify, request, send_from_directory
from flask_cors import CORS
import pandas as pd
import numpy as np
import requests
from datetime import datetime, timedelta
import json
import os
import time

app = Flask(__name__)
CORS(app)

class StockAnalyzer:
    def __init__(self):
        self.stock_data_cache = {}
        self.cache_timeout = 300  # 5分钟缓存
        
    def get_stock_data(self, symbol, period='1d'):
        """获取股票数据"""
        cache_key = f"{symbol}_{period}"
        current_time = datetime.now()

        # 检查缓存
        if cache_key in self.stock_data_cache:
            cached_data, cached_time = self.stock_data_cache[cache_key]
            if (current_time - cached_time).seconds < self.cache_timeout:
                return cached_data

        try:
            # 生成模拟数据用于演示
            dates = pd.date_range(start='2024-01-01', end=datetime.now().strftime('%Y-%m-%d'), freq='D')
            np.random.seed(hash(symbol) % 2**32)  # 使用股票代码作为随机种子，确保数据一致性

            # 生成模拟价格数据
            base_price = 10 + (hash(symbol) % 100)
            prices = []
            current_price = base_price

            for _ in range(len(dates)):
                change = np.random.normal(0, 0.02)  # 2%的日波动
                current_price = current_price * (1 + change)
                prices.append(current_price)

            # 创建DataFrame
            df = pd.DataFrame({
                '日期': dates,
                '开盘': [p * (1 + np.random.normal(0, 0.005)) for p in prices],
                '收盘': prices,
                '最高': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
                '最低': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
                '成交量': [np.random.randint(1000000, 10000000) for _ in prices]
            })

            # 缓存数据
            self.stock_data_cache[cache_key] = (df, current_time)
            return df

        except Exception as e:
            print(f"获取股票数据失败: {e}")
            return None
    
    def calculate_sma(self, data, period):
        """计算简单移动平均线"""
        if len(data) < period:
            return [np.nan] * len(data)

        sma = []
        for i in range(len(data)):
            if i < period - 1:
                sma.append(np.nan)
            else:
                sma.append(np.mean(data[i-period+1:i+1]))
        return np.array(sma)

    def calculate_ema(self, data, period):
        """计算指数移动平均线"""
        if len(data) == 0:
            return np.array([])

        ema = [data[0]]
        multiplier = 2 / (period + 1)

        for i in range(1, len(data)):
            ema.append((data[i] * multiplier) + (ema[i-1] * (1 - multiplier)))

        return np.array(ema)

    def calculate_rsi(self, data, period=14):
        """计算RSI指标"""
        if len(data) < period + 1:
            return np.array([50] * len(data))  # 返回中性值

        deltas = np.diff(data)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)

        avg_gains = self.calculate_sma(gains, period)
        avg_losses = self.calculate_sma(losses, period)

        rs = avg_gains / (avg_losses + 1e-10)  # 避免除零
        rsi = 100 - (100 / (1 + rs))

        return np.concatenate([[50], rsi])  # 第一个值设为中性

    def calculate_technical_indicators(self, df):
        """计算技术指标"""
        if df is None or df.empty:
            return {}

        try:
            close = df['收盘'].values
            high = df['最高'].values
            low = df['最低'].values
            volume = df['成交量'].values

            # 移动平均线
            ma5 = self.calculate_sma(close, 5)
            ma10 = self.calculate_sma(close, 10)
            ma20 = self.calculate_sma(close, 20)
            ma60 = self.calculate_sma(close, 60)

            # MACD (简化版)
            ema12 = self.calculate_ema(close, 12)
            ema26 = self.calculate_ema(close, 26)
            macd = ema12 - ema26
            macd_signal = self.calculate_ema(macd, 9)
            macd_hist = macd - macd_signal

            # RSI
            rsi = self.calculate_rsi(close, 14)

            # KDJ (简化版)
            k_values = []
            d_values = []
            for i in range(len(close)):
                if i < 8:  # 需要至少9个数据点
                    k_values.append(50)
                    d_values.append(50)
                else:
                    period_high = max(high[i-8:i+1])
                    period_low = min(low[i-8:i+1])
                    if period_high != period_low:
                        k = (close[i] - period_low) / (period_high - period_low) * 100
                    else:
                        k = 50
                    k_values.append(k)

                    # D值是K值的3日移动平均
                    if len(k_values) >= 3:
                        d = np.mean(k_values[-3:])
                    else:
                        d = k
                    d_values.append(d)

            k = np.array(k_values)
            d = np.array(d_values)
            j = 3 * k - 2 * d

            # 布林带 (简化版)
            bb_middle = ma20
            std = np.array([np.std(close[max(0, i-19):i+1]) if i >= 19 else np.std(close[:i+1]) for i in range(len(close))])
            bb_upper = bb_middle + 2 * std
            bb_lower = bb_middle - 2 * std

            # 成交量指标
            volume_ma5 = self.calculate_sma(volume.astype(float), 5)

            return {
                'ma5': ma5[-1] if len(ma5) > 0 and not np.isnan(ma5[-1]) else None,
                'ma10': ma10[-1] if len(ma10) > 0 and not np.isnan(ma10[-1]) else None,
                'ma20': ma20[-1] if len(ma20) > 0 and not np.isnan(ma20[-1]) else None,
                'ma60': ma60[-1] if len(ma60) > 0 and not np.isnan(ma60[-1]) else None,
                'macd': macd[-1] if len(macd) > 0 and not np.isnan(macd[-1]) else None,
                'macd_signal': macd_signal[-1] if len(macd_signal) > 0 and not np.isnan(macd_signal[-1]) else None,
                'macd_hist': macd_hist[-1] if len(macd_hist) > 0 and not np.isnan(macd_hist[-1]) else None,
                'rsi': rsi[-1] if len(rsi) > 0 and not np.isnan(rsi[-1]) else None,
                'k': k[-1] if len(k) > 0 and not np.isnan(k[-1]) else None,
                'd': d[-1] if len(d) > 0 and not np.isnan(d[-1]) else None,
                'j': j[-1] if len(j) > 0 and not np.isnan(j[-1]) else None,
                'bb_upper': bb_upper[-1] if len(bb_upper) > 0 and not np.isnan(bb_upper[-1]) else None,
                'bb_middle': bb_middle[-1] if len(bb_middle) > 0 and not np.isnan(bb_middle[-1]) else None,
                'bb_lower': bb_lower[-1] if len(bb_lower) > 0 and not np.isnan(bb_lower[-1]) else None,
                'volume_ratio': volume[-1] / volume_ma5[-1] if len(volume_ma5) > 0 and not np.isnan(volume_ma5[-1]) and volume_ma5[-1] != 0 else 1.0
            }

        except Exception as e:
            print(f"计算技术指标失败: {e}")
            return {}
    
    def calculate_technical_score(self, indicators, current_price):
        """计算技术评分"""
        score = 50  # 基础分数
        
        try:
            # MA趋势评分
            if indicators.get('ma5') and indicators.get('ma10') and indicators.get('ma20'):
                if current_price > indicators['ma5'] > indicators['ma10'] > indicators['ma20']:
                    score += 20  # 多头排列
                elif current_price < indicators['ma5'] < indicators['ma10'] < indicators['ma20']:
                    score -= 20  # 空头排列
            
            # MACD评分
            if indicators.get('macd') and indicators.get('macd_signal'):
                if indicators['macd'] > indicators['macd_signal'] and indicators['macd'] > 0:
                    score += 15  # MACD金叉且在零轴上方
                elif indicators['macd'] < indicators['macd_signal'] and indicators['macd'] < 0:
                    score -= 15  # MACD死叉且在零轴下方
            
            # RSI评分
            if indicators.get('rsi'):
                if 30 < indicators['rsi'] < 70:
                    score += 10  # RSI在正常区间
                elif indicators['rsi'] > 80:
                    score -= 10  # 超买
                elif indicators['rsi'] < 20:
                    score += 5   # 超卖反弹机会
            
            # KDJ评分
            if indicators.get('k') and indicators.get('d'):
                if indicators['k'] > indicators['d'] and indicators['k'] < 80:
                    score += 10  # KDJ金叉且未超买
                elif indicators['k'] < indicators['d'] and indicators['k'] > 20:
                    score -= 10  # KDJ死叉且未超卖
            
            # 成交量评分
            if indicators.get('volume_ratio'):
                if 1.5 < indicators['volume_ratio'] < 3:
                    score += 10  # 适度放量
                elif indicators['volume_ratio'] > 5:
                    score -= 5   # 过度放量
            
            return max(0, min(100, score))
            
        except Exception as e:
            print(f"计算技术评分失败: {e}")
            return 50

analyzer = StockAnalyzer()

@app.route('/')
def index():
    """返回主页"""
    return send_from_directory('.', 'index.html')

@app.route('/<path:filename>')
def static_files(filename):
    """返回静态文件"""
    return send_from_directory('.', filename)

@app.route('/api/market/indices')
def get_market_indices():
    """获取市场指数"""
    try:
        # 生成模拟指数数据
        base_time = int(time.time()) // 60  # 每分钟变化
        np.random.seed(base_time)

        indices = {
            'sh': {
                'name': '上证指数',
                'value': 3000 + np.random.normal(0, 50),
                'change': np.random.normal(0, 20),
                'change_pct': np.random.normal(0, 1.5)
            },
            'sz': {
                'name': '深证成指',
                'value': 10000 + np.random.normal(0, 200),
                'change': np.random.normal(0, 50),
                'change_pct': np.random.normal(0, 1.2)
            },
            'cy': {
                'name': '创业板指',
                'value': 2000 + np.random.normal(0, 100),
                'change': np.random.normal(0, 30),
                'change_pct': np.random.normal(0, 2.0)
            }
        }

        # 确保数据格式正确
        for key in indices:
            indices[key]['value'] = round(float(indices[key]['value']), 2)
            indices[key]['change'] = round(float(indices[key]['change']), 2)
            indices[key]['change_pct'] = round(float(indices[key]['change_pct']), 2)

        return jsonify({'success': True, 'data': indices})

    except Exception as e:
        print(f"获取市场指数失败: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/stock/info/<symbol>')
def get_stock_info(symbol):
    """获取股票基本信息"""
    try:
        # 生成模拟股票数据
        np.random.seed(hash(symbol) % 2**32)

        # 股票名称映射
        stock_names = {
            '000001': '平安银行',
            '000002': '万科A',
            '600036': '招商银行',
            '600519': '贵州茅台',
            '000858': '五粮液'
        }

        base_price = 10 + (hash(symbol) % 100)
        price_change = np.random.normal(0, 0.03)
        current_price = base_price * (1 + price_change)

        # 获取技术指标
        df = analyzer.get_stock_data(symbol)
        indicators = analyzer.calculate_technical_indicators(df)
        technical_score = analyzer.calculate_technical_score(indicators, current_price)

        result = {
            'code': symbol,
            'name': stock_names.get(symbol, f'股票{symbol}'),
            'price': round(current_price, 2),
            'change': round(current_price - base_price, 2),
            'change_pct': round(price_change * 100, 2),
            'volume': int(np.random.randint(1000000, 50000000)),
            'turnover': round(current_price * np.random.randint(1000000, 50000000) / 10000, 2),
            'market_cap': round(current_price * np.random.randint(100000000, 10000000000), 2),
            'pe_ratio': round(np.random.uniform(10, 50), 2),
            'pb_ratio': round(np.random.uniform(1, 5), 2),
            'technical_indicators': indicators,
            'technical_score': technical_score
        }

        return jsonify({'success': True, 'data': result})

    except Exception as e:
        print(f"获取股票信息失败: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/stock/screen', methods=['POST'])
def screen_stocks():
    """智能股票筛选"""
    try:
        filters = request.json or {}

        # 生成模拟股票列表
        stock_codes = ['000001', '000002', '600036', '600519', '000858', '002415', '000063', '600887', '002304', '300059',
                      '000166', '600000', '000725', '002142', '600276', '000776', '600309', '002230', '000568', '600104']
        stock_names = ['平安银行', '万科A', '招商银行', '贵州茅台', '五粮液', '海康威视', '中兴通讯', '伊利股份', '洋河股份', '东方财富',
                      '申万宏源', '浦发银行', '桐昆股份', '宁波银行', '恒瑞医药', '广发证券', '万华化学', '科大讯飞', '泸州老窖', '上汽集团']

        filtered_stocks = []

        for i, (code, name) in enumerate(zip(stock_codes, stock_names)):
            try:
                np.random.seed(hash(code) % 2**32)

                # 生成模拟数据
                base_price = 10 + (hash(code) % 100)
                price_change_pct = np.random.normal(0, 3)
                current_price = base_price * (1 + price_change_pct / 100)
                market_cap = current_price * np.random.randint(100000000, 10000000000)
                volume = np.random.randint(1000000, 100000000)

                # 应用筛选条件
                if filters.get('marketCap'):
                    if filters['marketCap'] == 'large' and market_cap < 50000000000:
                        continue
                    elif filters['marketCap'] == 'mid' and (market_cap < 10000000000 or market_cap > 50000000000):
                        continue
                    elif filters['marketCap'] == 'small' and market_cap > 10000000000:
                        continue

                if filters.get('change'):
                    if filters['change'] == 'up5' and price_change_pct < 5:
                        continue
                    elif filters['change'] == 'up3' and price_change_pct < 3:
                        continue
                    elif filters['change'] == 'down3' and price_change_pct > -3:
                        continue
                    elif filters['change'] == 'down5' and price_change_pct > -5:
                        continue

                # 计算技术评分
                df = analyzer.get_stock_data(code)
                indicators = analyzer.calculate_technical_indicators(df)
                technical_score = analyzer.calculate_technical_score(indicators, current_price)

                # 技术指标筛选
                if filters.get('technical'):
                    if filters['technical'] == 'macd_golden':
                        if not (indicators.get('macd', 0) > indicators.get('macd_signal', 0)):
                            continue
                    elif filters['technical'] == 'kdj_oversold':
                        if not (indicators.get('k', 50) < 20):
                            continue
                    elif filters['technical'] == 'rsi_strong':
                        if not (indicators.get('rsi', 50) > 60):
                            continue

                filtered_stocks.append({
                    'code': code,
                    'name': name,
                    'price': round(current_price, 2),
                    'change': round(current_price - base_price, 2),
                    'change_pct': round(price_change_pct, 2),
                    'volume': volume,
                    'market_cap': market_cap,
                    'technical_score': technical_score
                })

            except Exception as e:
                print(f"处理股票 {code} 时出错: {e}")
                continue

        # 根据策略排序
        if filters.get('strategy'):
            if filters['strategy'] == 'momentum':
                filtered_stocks.sort(key=lambda x: x['change_pct'], reverse=True)
            elif filters['strategy'] == 'value':
                filtered_stocks.sort(key=lambda x: x['price'])
            elif filters['strategy'] == 'growth':
                filtered_stocks.sort(key=lambda x: x['technical_score'], reverse=True)
            elif filters['strategy'] == 'reversal':
                filtered_stocks.sort(key=lambda x: x['change_pct'])

        return jsonify({'success': True, 'data': filtered_stocks[:50]})

    except Exception as e:
        print(f"股票筛选失败: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/stock/hot')
def get_hot_stocks():
    """获取热门股票"""
    try:
        # 生成模拟热门股票数据
        hot_stocks_data = [
            ('000001', '平安银行'),
            ('000002', '万科A'),
            ('600036', '招商银行'),
            ('600519', '贵州茅台'),
            ('000858', '五粮液'),
            ('002415', '海康威视'),
            ('000063', '中兴通讯'),
            ('600887', '伊利股份'),
            ('002304', '洋河股份'),
            ('300059', '东方财富')
        ]

        result = []
        base_time = int(time.time()) // 60  # 每分钟变化

        for i, (code, name) in enumerate(hot_stocks_data):
            np.random.seed(hash(code + str(base_time)) % 2**32)

            base_price = 10 + (hash(code) % 100)
            price_change_pct = np.random.uniform(1, 8)  # 热门股票都是上涨的
            current_price = base_price * (1 + price_change_pct / 100)

            result.append({
                'code': code,
                'name': name,
                'price': round(current_price, 2),
                'change': round(current_price - base_price, 2),
                'change_pct': round(price_change_pct, 2),
                'volume': int(np.random.randint(10000000, 100000000))
            })

        # 按涨幅排序
        result.sort(key=lambda x: x['change_pct'], reverse=True)

        return jsonify({'success': True, 'data': result})

    except Exception as e:
        print(f"获取热门股票失败: {e}")
        return jsonify({'success': False, 'error': str(e)})

if __name__ == '__main__':
    print("🚀 启动A股智能交易分析平台后端服务...")
    print("📊 支持功能:")
    print("   • 实时股票数据获取")
    print("   • 智能股票筛选")
    print("   • 技术指标计算")
    print("   • 市场指数监控")
    print("🌐 访问地址: http://localhost:5000")
    
    app.run(debug=True, host='0.0.0.0', port=8080)
