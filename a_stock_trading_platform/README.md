# 🚀 A股智能交易分析平台

基于TradingView的专业A股股票分析交易平台，集成智能选股、技术分析、实时数据等功能。

## ✨ 核心功能

### 📊 仪表盘
- **实时市场概览**: 上证指数、深证成指、创业板指实时数据
- **TradingView图表**: 专业K线图表，支持多种技术指标
- **热门股票**: 实时涨幅榜，快速发现市场热点

### 🎯 智能选股
- **多维度筛选**: 市值、涨跌幅、成交量、技术指标、行业板块
- **智能策略**: 
  - 🔥 动量策略：捕捉强势上涨股票
  - 💎 价值投资：发现低估值优质股
  - 📈 成长股：筛选高成长潜力股票
  - 🔄 反转策略：寻找超跌反弹机会
- **技术评分**: AI算法综合评估股票技术面强弱
- **一键筛选**: 快速找到符合条件的牛股

### 📈 技术分析
- **专业指标**: MACD、RSI、KDJ、布林带等20+技术指标
- **趋势分析**: 自动识别支撑阻力位、趋势线
- **形态识别**: 智能识别经典技术形态
- **多周期分析**: 支持分钟、小时、日线、周线等多时间框架

### ⭐ 自选股管理
- **个性化关注**: 添加关注股票到自选列表
- **实时监控**: 自选股价格变动实时提醒
- **批量操作**: 支持批量添加、删除、导出

## 🛠️ 技术架构

### 前端技术栈
- **HTML5 + CSS3**: 响应式现代化界面设计
- **JavaScript ES6+**: 原生JS实现，无框架依赖
- **TradingView**: 专业金融图表库
- **科技风UI**: 深蓝色主题，专业交易界面

### 后端技术栈
- **Python Flask**: 轻量级Web框架
- **AKShare**: A股数据获取库
- **TA-Lib**: 专业技术分析库
- **Pandas**: 数据处理分析
- **NumPy**: 数值计算

### 数据源
- **实时行情**: 东方财富、新浪财经API
- **历史数据**: 支持多年历史K线数据
- **基本面**: 财务数据、公司信息
- **技术指标**: 实时计算各类技术指标

## 🚀 快速开始

### 环境要求
- Python 3.8+
- pip 包管理器
- 现代浏览器 (Chrome/Firefox/Safari)

### 安装步骤

1. **克隆项目**
```bash
git clone <项目地址>
cd a_stock_trading_platform
```

2. **一键启动**
```bash
./start.sh
```

3. **手动安装（可选）**
```bash
# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate  # Linux/macOS
# venv\Scripts\activate   # Windows

# 安装依赖
pip install -r requirements.txt

# 启动服务
python api.py
```

4. **访问平台**
打开浏览器访问: http://localhost:5000

## 📱 使用指南

### 智能选股操作流程

1. **设置筛选条件**
   - 选择市值范围（大盘股/中盘股/小盘股）
   - 设置涨跌幅区间
   - 选择技术指标条件
   - 指定行业板块

2. **选择投资策略**
   - 动量策略：适合短线交易
   - 价值投资：适合长期持有
   - 成长股：适合中长期投资
   - 反转策略：适合抄底操作

3. **执行筛选**
   - 点击"开始筛选"按钮
   - 系统自动分析全市场股票
   - 显示符合条件的股票列表

4. **结果分析**
   - 查看技术评分（0-100分）
   - 分析关键技术指标
   - 添加心仪股票到自选

### 技术分析使用技巧

1. **多时间框架分析**
   - 日线看趋势
   - 60分钟线找买卖点
   - 15分钟线精确入场

2. **指标组合使用**
   - MACD + RSI：确认趋势强度
   - KDJ + 布林带：寻找买卖时机
   - 成交量 + 均线：验证突破有效性

3. **风险控制**
   - 设置止损位
   - 控制仓位大小
   - 分散投资风险

## 🎯 智能选股策略详解

### 动量策略 🔥
**适用场景**: 牛市、强势股追涨
**筛选条件**:
- 涨幅 > 3%
- 成交量放大 > 2倍
- 突破重要均线
- MACD金叉

**操作建议**: 快进快出，严格止损

### 价值投资 💎
**适用场景**: 熊市、价值回归
**筛选条件**:
- 大盘股（市值 > 500亿）
- PE < 15，PB < 2
- ROE > 15%
- 股息率 > 3%

**操作建议**: 长期持有，定期定投

### 成长股策略 📈
**适用场景**: 科技股、新兴行业
**筛选条件**:
- 中盘股（100-500亿）
- 营收增长 > 20%
- 净利润增长 > 30%
- 技术评分 > 80

**操作建议**: 中长期持有，关注业绩

### 反转策略 🔄
**适用场景**: 超跌反弹、底部抄底
**筛选条件**:
- 跌幅 > 5%
- RSI < 30（超卖）
- KDJ < 20
- 成交量放大

**操作建议**: 分批建仓，快速止盈

## 📊 技术指标说明

### 趋势指标
- **MA均线**: 判断趋势方向
- **MACD**: 趋势转换信号
- **布林带**: 价格波动区间

### 震荡指标
- **RSI**: 超买超卖判断
- **KDJ**: 短期买卖时机
- **威廉指标**: 反转信号

### 成交量指标
- **成交量比**: 资金关注度
- **OBV**: 量价配合分析
- **换手率**: 活跃程度

## ⚠️ 风险提示

1. **投资有风险，入市需谨慎**
2. **技术分析仅供参考，不构成投资建议**
3. **请根据自身风险承受能力合理投资**
4. **建议结合基本面分析做出投资决策**
5. **严格执行风险管理，设置止损止盈**

## 🔧 开发说明

### 项目结构
```
a_stock_trading_platform/
├── index.html          # 主页面
├── styles.css          # 样式文件
├── script.js           # 前端逻辑
├── api.py              # 后端API
├── requirements.txt    # Python依赖
├── start.sh           # 启动脚本
└── README.md          # 说明文档
```

### API接口
- `GET /api/market/indices` - 获取市场指数
- `GET /api/stock/info/<symbol>` - 获取股票信息
- `POST /api/stock/screen` - 智能股票筛选
- `GET /api/stock/hot` - 获取热门股票

### 自定义开发
1. **添加新的筛选条件**: 修改 `script.js` 中的筛选逻辑
2. **集成新的数据源**: 在 `api.py` 中添加数据接口
3. **扩展技术指标**: 使用TA-Lib库添加更多指标
4. **优化UI界面**: 修改 `styles.css` 样式文件

## 📞 技术支持

如有问题或建议，欢迎联系：
- 📧 邮箱: <EMAIL>
- 💬 微信: stockplatform
- 🐛 Bug反馈: GitHub Issues

## 📄 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

---

**免责声明**: 本平台仅供学习交流使用，不构成任何投资建议。投资者应当根据自身情况独立做出投资决策，并承担相应风险。
