// A股智能交易分析平台 - 主要JavaScript文件

class AStockTradingPlatform {
    constructor() {
        this.currentTab = 'dashboard';
        this.watchlist = JSON.parse(localStorage.getItem('watchlist')) || [];
        this.tradingViewWidget = null;
        this.analysisWidget = null;
        this.stockData = new Map();
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.updateTime();
        this.loadMarketData();
        this.initTradingView();
        this.loadHotStocks();
        this.loadWatchlist();
        setInterval(() => this.updateTime(), 1000);
        setInterval(() => this.updateMarketData(), 30000); // 30秒更新一次
    }

    setupEventListeners() {
        // 导航标签切换
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // 股票搜索
        document.getElementById('searchStock').addEventListener('click', () => {
            this.searchStock();
        });

        document.getElementById('stockInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.searchStock();
            }
        });

        // 筛选功能
        document.getElementById('applyFilters').addEventListener('click', () => {
            this.applyFilters();
        });

        document.getElementById('resetFilters').addEventListener('click', () => {
            this.resetFilters();
        });

        document.getElementById('saveStrategy').addEventListener('click', () => {
            this.saveStrategy();
        });

        // 策略选择
        document.getElementById('strategyFilter').addEventListener('change', (e) => {
            this.loadStrategy(e.target.value);
        });

        // 自选股功能
        document.getElementById('addToWatchlist').addEventListener('click', () => {
            this.showAddStockModal();
        });

        // 技术分析工具
        document.querySelectorAll('.tool-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.selectAnalysisTool(e.target.dataset.tool);
            });
        });

        // 模态框
        document.querySelector('.close').addEventListener('click', () => {
            this.closeModal();
        });

        window.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                this.closeModal();
            }
        });
    }

    switchTab(tabName) {
        // 更新导航按钮状态
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // 切换内容
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(tabName).classList.add('active');

        this.currentTab = tabName;

        // 特殊处理
        if (tabName === 'analysis' && !this.analysisWidget) {
            this.initAnalysisChart();
        }
    }

    updateTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('zh-CN');
        document.getElementById('currentTime').textContent = timeString;

        // 更新市场状态
        const hour = now.getHours();
        const minute = now.getMinutes();
        const currentTime = hour * 100 + minute;
        
        let marketStatus = '休市';
        if ((currentTime >= 930 && currentTime <= 1130) || (currentTime >= 1300 && currentTime <= 1500)) {
            marketStatus = '开盘中';
        } else if ((currentTime >= 915 && currentTime < 930) || (currentTime >= 1257 && currentTime < 1300)) {
            marketStatus = '集合竞价';
        }
        
        document.getElementById('marketStatus').textContent = marketStatus;
        document.getElementById('marketStatus').className = `market-status ${marketStatus === '开盘中' ? 'open' : 'closed'}`;
    }

    async loadMarketData() {
        // 模拟市场数据
        const indices = [
            { id: 'sh-index', name: '上证指数', value: 3000 + Math.random() * 200 - 100 },
            { id: 'sz-index', name: '深证成指', value: 10000 + Math.random() * 1000 - 500 },
            { id: 'cy-index', name: '创业板指', value: 2000 + Math.random() * 300 - 150 }
        ];

        indices.forEach(index => {
            const change = (Math.random() - 0.5) * 4; // -2% 到 +2%
            const valueElement = document.getElementById(index.id);
            const changeElement = document.getElementById(index.id.replace('index', 'change'));
            
            if (valueElement && changeElement) {
                valueElement.textContent = index.value.toFixed(2);
                changeElement.textContent = `${change >= 0 ? '+' : ''}${change.toFixed(2)}%`;
                changeElement.className = `index-change ${change >= 0 ? 'positive' : 'negative'}`;
            }
        });
    }

    updateMarketData() {
        this.loadMarketData();
        this.updateHotStocks();
        this.updateWatchlist();
    }

    initTradingView() {
        if (typeof TradingView !== 'undefined') {
            this.tradingViewWidget = new TradingView.widget({
                "width": "100%",
                "height": 400,
                "symbol": "SSE:000001",
                "interval": "D",
                "timezone": "Asia/Shanghai",
                "theme": "light",
                "style": "1",
                "locale": "zh_CN",
                "toolbar_bg": "#f1f3f6",
                "enable_publishing": false,
                "allow_symbol_change": true,
                "container_id": "tradingview_chart",
                "studies": [
                    "MASimple@tv-basicstudies",
                    "MACD@tv-basicstudies"
                ]
            });
        }
    }

    initAnalysisChart() {
        if (typeof TradingView !== 'undefined') {
            this.analysisWidget = new TradingView.widget({
                "width": "100%",
                "height": 500,
                "symbol": "SSE:000001",
                "interval": "D",
                "timezone": "Asia/Shanghai",
                "theme": "light",
                "style": "1",
                "locale": "zh_CN",
                "toolbar_bg": "#f1f3f6",
                "enable_publishing": false,
                "allow_symbol_change": true,
                "container_id": "analysis_chart",
                "studies": [
                    "MASimple@tv-basicstudies",
                    "MACD@tv-basicstudies",
                    "RSI@tv-basicstudies",
                    "StochasticRSI@tv-basicstudies"
                ]
            });
        }
    }

    searchStock() {
        const input = document.getElementById('stockInput').value.trim();
        if (!input) return;

        // 这里应该调用实际的股票搜索API
        // 现在使用模拟数据
        const symbol = this.formatStockSymbol(input);
        if (this.tradingViewWidget) {
            this.tradingViewWidget.chart().setSymbol(symbol);
        }
    }

    formatStockSymbol(input) {
        // 将中国股票代码转换为TradingView格式
        if (input.startsWith('00') || input.startsWith('30')) {
            return `SZSE:${input}`;
        } else if (input.startsWith('60') || input.startsWith('68')) {
            return `SSE:${input}`;
        }
        return input;
    }

    async loadHotStocks() {
        // 模拟热门股票数据
        const hotStocks = [
            { code: '000001', name: '平安银行', price: 12.50, change: 2.1 },
            { code: '000002', name: '万科A', price: 18.30, change: -1.2 },
            { code: '600036', name: '招商银行', price: 45.60, change: 1.8 },
            { code: '600519', name: '贵州茅台', price: 1680.00, change: 0.5 },
            { code: '000858', name: '五粮液', price: 168.50, change: -0.8 }
        ];

        const container = document.getElementById('hotStocks');
        container.innerHTML = hotStocks.map(stock => `
            <div class="stock-item" onclick="platform.showStockDetail('${stock.code}')">
                <div class="stock-info">
                    <span class="stock-code">${stock.code}</span>
                    <span class="stock-name">${stock.name}</span>
                </div>
                <div class="stock-price">
                    <span class="stock-value">¥${stock.price}</span>
                    <span class="stock-change ${stock.change >= 0 ? 'positive' : 'negative'}">
                        ${stock.change >= 0 ? '+' : ''}${stock.change}%
                    </span>
                </div>
            </div>
        `).join('');
    }

    updateHotStocks() {
        // 更新热门股票价格
        this.loadHotStocks();
    }

    // 智能筛选功能
    async applyFilters() {
        const filters = this.getFilterValues();
        const button = document.getElementById('applyFilters');
        const originalText = button.textContent;
        
        button.innerHTML = '<span class="loading"></span> 筛选中...';
        button.disabled = true;

        try {
            // 模拟API调用延迟
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            const results = await this.performStockScreening(filters);
            this.displayScreeningResults(results);
        } catch (error) {
            console.error('筛选失败:', error);
            alert('筛选失败，请稍后重试');
        } finally {
            button.textContent = originalText;
            button.disabled = false;
        }
    }

    getFilterValues() {
        return {
            marketCap: document.getElementById('marketCapFilter').value,
            change: document.getElementById('changeFilter').value,
            volume: document.getElementById('volumeFilter').value,
            technical: document.getElementById('technicalFilter').value,
            sector: document.getElementById('sectorFilter').value,
            strategy: document.getElementById('strategyFilter').value
        };
    }

    async performStockScreening(filters) {
        // 模拟智能筛选算法
        const allStocks = this.generateMockStockData();
        
        let filteredStocks = allStocks.filter(stock => {
            // 市值筛选
            if (filters.marketCap) {
                if (filters.marketCap === 'large' && stock.marketCap < 50000000000) return false;
                if (filters.marketCap === 'mid' && (stock.marketCap < 10000000000 || stock.marketCap > 50000000000)) return false;
                if (filters.marketCap === 'small' && stock.marketCap > 10000000000) return false;
            }

            // 涨跌幅筛选
            if (filters.change) {
                if (filters.change === 'up5' && stock.change < 5) return false;
                if (filters.change === 'up3' && stock.change < 3) return false;
                if (filters.change === 'down3' && stock.change > -3) return false;
                if (filters.change === 'down5' && stock.change > -5) return false;
            }

            // 技术指标筛选
            if (filters.technical) {
                if (filters.technical === 'macd_golden' && !stock.macdGolden) return false;
                if (filters.technical === 'kdj_oversold' && stock.kdj > 20) return false;
                if (filters.technical === 'rsi_strong' && stock.rsi < 60) return false;
                if (filters.technical === 'ma_breakthrough' && !stock.maBreakthrough) return false;
            }

            return true;
        });

        // 根据策略排序
        if (filters.strategy) {
            filteredStocks = this.applyStrategy(filteredStocks, filters.strategy);
        }

        return filteredStocks.slice(0, 50); // 返回前50只股票
    }

    generateMockStockData() {
        const stocks = [];
        const stockCodes = ['000001', '000002', '600036', '600519', '000858', '002415', '000063', '600887', '002304', '300059'];
        const stockNames = ['平安银行', '万科A', '招商银行', '贵州茅台', '五粮液', '海康威视', '中兴通讯', '伊利股份', '洋河股份', '东方财富'];
        
        for (let i = 0; i < 100; i++) {
            const baseIndex = i % stockCodes.length;
            stocks.push({
                code: stockCodes[baseIndex] + (i > 9 ? i : ''),
                name: stockNames[baseIndex] + (i > 9 ? i : ''),
                price: Math.random() * 100 + 10,
                change: (Math.random() - 0.5) * 20,
                volume: Math.random() * 1000000000,
                marketCap: Math.random() * 100000000000,
                technicalScore: Math.random() * 100,
                macdGolden: Math.random() > 0.7,
                kdj: Math.random() * 100,
                rsi: Math.random() * 100,
                maBreakthrough: Math.random() > 0.6
            });
        }
        
        return stocks;
    }

    applyStrategy(stocks, strategy) {
        switch (strategy) {
            case 'momentum':
                return stocks.sort((a, b) => b.change - a.change);
            case 'value':
                return stocks.sort((a, b) => a.price - b.price);
            case 'growth':
                return stocks.sort((a, b) => b.technicalScore - a.technicalScore);
            case 'reversal':
                return stocks.sort((a, b) => a.change - b.change);
            default:
                return stocks;
        }
    }

    displayScreeningResults(results) {
        const tbody = document.getElementById('resultsBody');
        const countElement = document.getElementById('resultsCount');
        
        countElement.textContent = `找到 ${results.length} 只股票`;
        
        tbody.innerHTML = results.map(stock => `
            <tr>
                <td>${stock.code}</td>
                <td>${stock.name}</td>
                <td>¥${stock.price.toFixed(2)}</td>
                <td class="${stock.change >= 0 ? 'positive' : 'negative'}">
                    ${stock.change >= 0 ? '+' : ''}${stock.change.toFixed(2)}%
                </td>
                <td>${this.formatVolume(stock.volume)}</td>
                <td>${this.formatMarketCap(stock.marketCap)}</td>
                <td class="${this.getScoreClass(stock.technicalScore)}">
                    ${stock.technicalScore.toFixed(1)}
                </td>
                <td>
                    <button onclick="platform.addToWatchlistFromResult('${stock.code}', '${stock.name}')" class="btn-primary" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;">
                        加自选
                    </button>
                </td>
            </tr>
        `).join('');
    }

    formatVolume(volume) {
        if (volume > 100000000) {
            return (volume / 100000000).toFixed(1) + '亿';
        } else if (volume > 10000) {
            return (volume / 10000).toFixed(1) + '万';
        }
        return volume.toString();
    }

    formatMarketCap(marketCap) {
        if (marketCap > 100000000000) {
            return (marketCap / 100000000000).toFixed(1) + '千亿';
        } else if (marketCap > 1000000000) {
            return (marketCap / 1000000000).toFixed(1) + '百亿';
        }
        return (marketCap / 100000000).toFixed(1) + '亿';
    }

    getScoreClass(score) {
        if (score >= 80) return 'score-high';
        if (score >= 60) return 'score-medium';
        return 'score-low';
    }

    resetFilters() {
        document.querySelectorAll('#screener select').forEach(select => {
            select.value = '';
        });
        document.getElementById('resultsBody').innerHTML = '';
        document.getElementById('resultsCount').textContent = '找到 0 只股票';
    }

    loadStrategy(strategy) {
        if (!strategy) return;
        
        const strategies = {
            momentum: {
                marketCap: '',
                change: 'up3',
                volume: 'high',
                technical: 'ma_breakthrough',
                sector: ''
            },
            value: {
                marketCap: 'large',
                change: '',
                volume: '',
                technical: '',
                sector: 'finance'
            },
            growth: {
                marketCap: 'mid',
                change: 'up5',
                volume: 'high',
                technical: 'rsi_strong',
                sector: 'tech'
            },
            reversal: {
                marketCap: '',
                change: 'down5',
                volume: 'high',
                technical: 'kdj_oversold',
                sector: ''
            }
        };

        const config = strategies[strategy];
        if (config) {
            Object.keys(config).forEach(key => {
                const element = document.getElementById(key + 'Filter');
                if (element) {
                    element.value = config[key];
                }
            });
        }
    }

    saveStrategy() {
        const filters = this.getFilterValues();
        const name = prompt('请输入策略名称:');
        if (name) {
            const strategies = JSON.parse(localStorage.getItem('customStrategies')) || {};
            strategies[name] = filters;
            localStorage.setItem('customStrategies', JSON.stringify(strategies));
            alert('策略保存成功！');
        }
    }

    // 自选股功能
    loadWatchlist() {
        this.updateWatchlistDisplay();
    }

    updateWatchlist() {
        // 更新自选股价格
        this.updateWatchlistDisplay();
    }

    updateWatchlistDisplay() {
        const tbody = document.getElementById('watchlistBody');
        
        if (this.watchlist.length === 0) {
            tbody.innerHTML = '<tr><td colspan="7" style="text-align: center; color: #666;">暂无自选股，点击"添加股票"开始添加</td></tr>';
            return;
        }

        tbody.innerHTML = this.watchlist.map(stock => {
            const currentPrice = stock.price * (1 + (Math.random() - 0.5) * 0.02); // 模拟价格波动
            const change = ((currentPrice - stock.price) / stock.price * 100);
            
            return `
                <tr>
                    <td>${stock.code}</td>
                    <td>${stock.name}</td>
                    <td>¥${currentPrice.toFixed(2)}</td>
                    <td class="${change >= 0 ? 'positive' : 'negative'}">
                        ${change >= 0 ? '+' : ''}${(currentPrice - stock.price).toFixed(2)}
                    </td>
                    <td class="${change >= 0 ? 'positive' : 'negative'}">
                        ${change >= 0 ? '+' : ''}${change.toFixed(2)}%
                    </td>
                    <td>${this.formatVolume(Math.random() * 1000000000)}</td>
                    <td>
                        <button onclick="platform.removeFromWatchlist('${stock.code}')" class="btn-secondary" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;">
                            删除
                        </button>
                    </td>
                </tr>
            `;
        }).join('');
    }

    showAddStockModal() {
        const code = prompt('请输入股票代码:');
        const name = prompt('请输入股票名称:');
        
        if (code && name) {
            this.addToWatchlistFromResult(code, name);
        }
    }

    addToWatchlistFromResult(code, name) {
        if (this.watchlist.find(stock => stock.code === code)) {
            alert('该股票已在自选股中');
            return;
        }

        const stock = {
            code: code,
            name: name,
            price: Math.random() * 100 + 10,
            addedAt: new Date().toISOString()
        };

        this.watchlist.push(stock);
        localStorage.setItem('watchlist', JSON.stringify(this.watchlist));
        this.updateWatchlistDisplay();
        alert('添加成功！');
    }

    removeFromWatchlist(code) {
        if (confirm('确定要删除这只股票吗？')) {
            this.watchlist = this.watchlist.filter(stock => stock.code !== code);
            localStorage.setItem('watchlist', JSON.stringify(this.watchlist));
            this.updateWatchlistDisplay();
        }
    }

    // 技术分析工具
    selectAnalysisTool(tool) {
        document.querySelectorAll('.tool-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        event.target.classList.add('active');

        // 这里可以添加不同工具的具体功能
        console.log('选择了分析工具:', tool);
    }

    // 股票详情
    showStockDetail(code) {
        const modal = document.getElementById('stockModal');
        const content = document.getElementById('modalContent');
        
        // 这里应该获取真实的股票详情数据
        content.innerHTML = `
            <h4>股票代码: ${code}</h4>
            <p>这里显示股票的详细信息...</p>
            <div style="margin-top: 1rem;">
                <button onclick="platform.addToWatchlistFromResult('${code}', '股票名称')" class="btn-primary">
                    加入自选股
                </button>
            </div>
        `;
        
        modal.style.display = 'block';
    }

    closeModal() {
        document.getElementById('stockModal').style.display = 'none';
    }
}

// 初始化平台
const platform = new AStockTradingPlatform();

// 全局函数，供HTML调用
window.platform = platform;
