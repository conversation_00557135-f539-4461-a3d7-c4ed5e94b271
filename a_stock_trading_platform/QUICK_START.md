# 🚀 A股智能交易分析平台 - 快速使用指南

## ✅ 平台已成功启动！

### 🌐 访问地址
- **主页面**: http://localhost:8080
- **API接口**: http://localhost:8080/api

### 📊 功能测试结果
✅ 服务器连接正常  
✅ 市场指数API正常  
✅ 股票信息API正常  
✅ 智能选股API正常  
✅ 热门股票API正常  

### 🎯 核心功能使用

#### 1. 仪表盘
- 查看上证指数、深证成指、创业板指实时数据
- 使用TradingView专业K线图表
- 浏览今日热门股票

#### 2. 智能选股 🔥
**快速上手**:
1. 点击"智能选股"标签
2. 选择筛选条件：
   - 市值范围：大盘股/中盘股/小盘股
   - 涨跌幅：涨幅>5%、涨幅>3%等
   - 技术指标：MACD金叉、RSI强势等
3. 选择投资策略：
   - 🔥 动量策略：捕捉强势股
   - 💎 价值投资：发现低估股
   - 📈 成长股：高成长潜力
   - 🔄 反转策略：超跌反弹
4. 点击"开始筛选"
5. 查看筛选结果和技术评分

#### 3. 技术分析
- 专业技术指标：MACD、RSI、KDJ、布林带
- 多时间框架分析
- 趋势识别和形态分析

#### 4. 自选股管理
- 添加关注股票
- 实时价格监控
- 批量管理功能

### 💡 使用技巧

#### 智能选股策略推荐

**短线交易（1-5天）**:
```
策略：动量策略
条件：涨幅>3% + 成交量放大 + MACD金叉
评分：>80分
```

**中线投资（1-3个月）**:
```
策略：成长股
条件：中盘股 + 技术评分>70 + RSI强势
```

**长线投资（6个月以上）**:
```
策略：价值投资
条件：大盘股 + 低PE + 高股息率
```

#### 技术分析要点
1. **趋势判断**: 看均线排列
2. **买卖时机**: 结合MACD和KDJ
3. **风险控制**: 关注RSI超买超卖
4. **量价配合**: 成交量确认突破

### 🔧 故障排除

如果遇到问题：
1. **页面无法访问**: 确认服务器在http://localhost:8080运行
2. **数据不更新**: 刷新页面或重启服务器
3. **筛选无结果**: 放宽筛选条件
4. **图表不显示**: 检查网络连接

### 📞 技术支持

- 查看控制台日志获取详细错误信息
- 重启服务器：Ctrl+C 停止，然后重新运行 `python3 api.py`
- 检查依赖：`pip3 install -r requirements.txt`

### ⚠️ 重要提醒

1. **投资有风险，入市需谨慎**
2. **技术分析仅供参考，不构成投资建议**
3. **请结合基本面分析做出投资决策**
4. **严格执行风险管理，设置止损止盈**

### 🎉 开始使用

现在您可以：
1. 在浏览器中访问 http://localhost:8080
2. 探索各项功能
3. 使用智能选股发现投资机会
4. 进行专业技术分析

祝您投资顺利！ 📈
