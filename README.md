# 🚀 A股智能交易分析平台 - macOS专业版

专为交易员设计的原生macOS股票分析应用，具备实时数据、智能选股、专业图表等功能。

## ✨ 核心功能

### 📊 实时数据监控
- **市场指数**: 上证指数、深证成指、创业板指实时更新
- **自选股监控**: 实时价格、涨跌幅、成交量监控
- **数据刷新**: 5秒自动更新，支持手动刷新

### 🎯 智能选股系统
- **多维度筛选**: 涨跌幅、市值、成交量、技术指标
- **投资策略**:
  - 🔥 动量策略：捕捉强势上涨股票
  - 💎 价值投资：发现低估值优质股
  - 📈 成长股：筛选高成长潜力股票
  - 🔄 反转策略：寻找超跌反弹机会
- **技术评分**: AI算法综合评估股票技术面

### 📈 专业技术分析
- **K线图表**: 基于pyqtgraph的高性能图表
- **技术指标**: MA5/MA10移动平均线
- **多周期**: 支持分钟、小时、日线等多时间框架
- **实时更新**: 图表数据实时同步

### ⭐ 自选股管理
- **快速添加**: 支持代码/名称搜索添加
- **实时监控**: 价格变动实时显示
- **本地存储**: 自选股列表本地持久化
- **批量操作**: 支持批量管理

### ⌨️ 专业操作体验
- **快捷键**: 全面的快捷键支持
- **分屏显示**: 自选股+图表双屏布局
- **深色主题**: 护眼的专业交易界面
- **全屏模式**: 专注的交易环境

## 🛠️ 技术架构

### 前端技术
- **PyQt6**: 原生macOS界面框架
- **pyqtgraph**: 高性能金融图表库
- **多线程**: 数据更新与UI分离
- **响应式**: 自适应窗口大小

### 数据处理
- **pandas**: 数据分析处理
- **numpy**: 数值计算
- **技术指标**: 自研技术分析算法
- **实时缓存**: 智能数据缓存机制

## 🚀 快速开始

### 环境要求
- macOS 10.14+
- Python 3.8+
- pip 包管理器

### 一键启动
```bash
./run.sh
```

### 手动安装
```bash
# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 启动应用
python3 main.py
```

## ⌨️ 快捷键说明

### 数据操作
- `F5` - 刷新数据
- `Ctrl+A` - 添加股票
- `Ctrl+I` - 导入自选股
- `Ctrl+E` - 导出自选股

### 界面操作
- `Ctrl+1` - 切换到主界面
- `Ctrl+2` - 切换到智能选股
- `Ctrl+S` - 打开智能选股
- `F11` - 全屏模式
- `ESC` - 退出全屏

### 系统操作
- `Ctrl+Q` - 退出应用
- `Enter` - 搜索股票

## 🎯 使用指南

### 自选股管理
1. **添加股票**: 点击"添加"按钮或使用Ctrl+A
2. **搜索股票**: 在工具栏搜索框输入代码或名称
3. **查看图表**: 双击自选股查看技术分析图表
4. **删除股票**: 点击操作列的"删除"按钮

### 智能选股操作
1. **设置条件**: 选择涨跌幅、市值、成交量等筛选条件
2. **选择策略**: 选择预设的投资策略或自定义
3. **开始筛选**: 点击"开始筛选"按钮
4. **查看结果**: 分析筛选结果和技术评分

### 技术分析
1. **选择股票**: 从自选股列表选择要分析的股票
2. **查看图表**: 右侧显示K线图和技术指标
3. **切换周期**: 选择不同的时间周期
4. **分析趋势**: 结合MA均线判断趋势方向

## 🎨 界面特色

### 专业交易界面
- **深色主题**: 减少眼部疲劳
- **红涨绿跌**: 符合中国股市习惯
- **数据对齐**: 专业的表格布局
- **状态提示**: 实时连接和更新状态

### 响应式布局
- **分割器**: 可调整左右面板比例
- **自适应**: 窗口大小自动适配
- **全屏支持**: 专注的交易环境
- **多标签**: 功能模块清晰分离

## 📊 数据说明

### 数据来源
- 当前使用模拟数据进行演示
- 支持接入真实股票数据API
- 数据更新频率：5秒/次
- 缓存机制：30秒本地缓存

### 技术指标
- **MA5/MA10**: 5日/10日移动平均线
- **RSI**: 相对强弱指标
- **MACD**: 指数平滑移动平均线
- **技术评分**: 综合技术面评估

## ⚠️ 风险提示

1. **投资有风险，入市需谨慎**
2. **技术分析仅供参考，不构成投资建议**
3. **请结合基本面分析做出投资决策**
4. **严格执行风险管理，设置止损止盈**
5. **模拟数据仅用于功能演示**

## 🔧 开发说明

### 项目结构
```
AStockTrader.app/
├── main.py              # 主程序入口
├── requirements.txt     # Python依赖
├── run.sh              # 启动脚本
└── README.md           # 说明文档
```

### 核心类说明
- `MainWindow`: 主窗口类
- `StockDataProvider`: 数据提供者
- `MarketOverviewWidget`: 市场概览组件
- `StockListWidget`: 自选股列表组件
- `StockChartWidget`: 股票图表组件
- `SmartScreenerWidget`: 智能选股组件

### 扩展开发
1. **接入真实数据**: 修改`StockDataProvider`类
2. **添加技术指标**: 扩展`TechnicalAnalyzer`类
3. **自定义界面**: 修改各Widget组件
4. **新增功能**: 在主窗口添加新标签页

## 📞 技术支持

如有问题或建议：
- 查看控制台输出获取详细错误信息
- 确保Python环境和依赖正确安装
- 检查网络连接状态

---

**免责声明**: 本应用仅供学习交流使用，不构成任何投资建议。投资者应当根据自身情况独立做出投资决策，并承担相应风险。
